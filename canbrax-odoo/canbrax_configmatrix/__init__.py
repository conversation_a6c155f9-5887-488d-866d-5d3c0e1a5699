# -*- coding: utf-8 -*-

from . import models
from . import wizards
from . import controllers

def post_init_hook(env):
    """Post installation hook - create sample stock for mesh products and set work centers"""
    import logging
    _logger = logging.getLogger(__name__)

    try:
        # Create sample stock for mesh products
        _create_sample_mesh_stock(env)
    except Exception as e:
        # Don't fail installation if stock creation fails
        _logger.warning(f"Failed to create sample mesh stock: {e}")

    try:
        # Set work centers for mesh operation templates
        _set_mesh_operation_work_centers(env)
    except Exception as e:
        # Don't fail installation if work center setup fails
        _logger.warning(f"Failed to set mesh operation work centers: {e}")

    try:
        # Set up mesh operation mappings for existing templates
        _setup_mesh_operation_mappings(env)
    except Exception as e:
        # Don't fail installation if operation mapping setup fails
        _logger.warning(f"Failed to set up mesh operation mappings: {e}")

def _create_sample_mesh_stock(env):
    """Create sample stock for mesh products"""
    # Get stock location
    stock_location = env.ref('stock.stock_location_stock')

    # Create stock for master sheets
    master_products = [
        ('canbrax_configmatrix.product_saltwater_master_1100x620', 5.0),
        ('canbrax_configmatrix.product_saltwater_master_1250x1000', 3.0),
    ]

    for product_ref, qty in master_products:
        try:
            product = env.ref(product_ref)
            if product and product.type == 'product':
                env['stock.quant']._update_available_quantity(
                    product.product_variant_ids[0],
                    stock_location,
                    qty
                )
        except:
            continue

    # Create stock for planned off-cuts
    planned_products = [
        ('canbrax_configmatrix.product_saltwater_offcut_600x400', 8.0),
        ('canbrax_configmatrix.product_saltwater_offcut_500x300', 12.0),
    ]

    for product_ref, qty in planned_products:
        try:
            product = env.ref(product_ref)
            if product and product.type == 'product':
                env['stock.quant']._update_available_quantity(
                    product.product_variant_ids[0],
                    stock_location,
                    qty
                )
        except:
            continue

    # Create stock for unplanned off-cuts with lots
    unplanned_lots = [
        ('canbrax_configmatrix.lot_unplanned_450x380', 1.0),
        ('canbrax_configmatrix.lot_unplanned_320x280', 1.0),
        ('canbrax_configmatrix.lot_unplanned_500x400', 1.0),
        ('canbrax_configmatrix.lot_unplanned_600x350', 1.0),
    ]

    for lot_ref, qty in unplanned_lots:
        try:
            lot = env.ref(lot_ref)
            if lot and lot.product_id.type == 'product':
                env['stock.quant']._update_available_quantity(
                    lot.product_id.product_variant_ids[0],
                    stock_location,
                    qty,
                    lot_id=lot
                )
        except:
            continue

def _set_mesh_operation_work_centers(env):
    """Set work centers for mesh operation templates using global configuration"""
    import logging
    _logger = logging.getLogger(__name__)

    try:
        # Create/get global settings
        settings = env['config.matrix.settings'].get_settings()

        # Find mesh operation templates without work centers
        mesh_templates = env['config.matrix.operation.template'].search([
            ('is_mesh_operation', '=', True),
            ('workcenter_id', '=', False)
        ])

        if not mesh_templates:
            _logger.info("No mesh operation templates found or all already have work centers")
            return

        # Try to find work center by code UP1 (UPCUT SAW) first
        workcenter = env['mrp.workcenter'].search([('code', '=', 'UP1')], limit=1)

        # If UP1 not found, try other cutting-related work centers
        if not workcenter:
            for code in ['CUT', 'SAW', 'CUTTING']:
                workcenter = env['mrp.workcenter'].search([('code', 'ilike', code)], limit=1)
                if workcenter:
                    break

        # If still not found, get the first available work center
        if not workcenter:
            workcenter = env['mrp.workcenter'].search([], limit=1)

        # Set the default mesh work center in global settings
        if workcenter and not settings.default_mesh_workcenter_id:
            settings.default_mesh_workcenter_id = workcenter.id
            _logger.info(f"Set default mesh work center to '{workcenter.name}' (code: {workcenter.code})")

        # Assign the work center to mesh operation templates
        if workcenter:
            for template in mesh_templates:
                template.workcenter_id = workcenter.id
                _logger.info(f"Assigned work center '{workcenter.name}' (code: {workcenter.code}) to template '{template.name}'")
        else:
            _logger.warning("No work centers found - mesh operation templates will need manual work center assignment")

        _logger.info(f"Successfully processed {len(mesh_templates)} mesh operation templates")

    except Exception as e:
        _logger.error(f"Error setting mesh operation work centers: {str(e)}")

def _setup_mesh_operation_mappings(env):
    """Set up mesh operation mappings for existing templates"""
    import logging
    _logger = logging.getLogger(__name__)

    try:
        # Find the mesh cut operation template
        mesh_operation_template = env.ref('canbrax_configmatrix.operation_template_mesh_cut', raise_if_not_found=False)
        if not mesh_operation_template:
            _logger.warning("Mesh cut operation template not found, skipping operation mapping setup")
            return

        # Find all active configuration templates
        templates = env['config.matrix.template'].search([('active', '=', True)])
        _logger.info(f"Found {len(templates)} active templates for mesh operation mapping setup")

        mappings_created = 0

        for template in templates:
            # Look for any field that could trigger mesh operations
            # We'll use the first field in the first section as a trigger
            if template.section_ids and template.section_ids[0].field_ids:
                first_field = template.section_ids[0].field_ids[0]

                # Check if this field already has a mesh operation mapping
                existing_mapping = env['config.matrix.field.operation.mapping'].search([
                    ('field_id', '=', first_field.id),
                    ('operation_template_id', '=', mesh_operation_template.id)
                ])

                if not existing_mapping:
                    # Create the operation mapping
                    mapping_vals = {
                        'field_id': first_field.id,
                        'operation_template_id': mesh_operation_template.id,
                        'condition': '_CALCULATED_mesh_operation_required == true',
                        'sequence': 100,
                        'name': f'Mesh Cut Operation for {template.name}',
                        'notes': 'Automatically created mesh cut operation mapping'
                    }

                    env['config.matrix.field.operation.mapping'].create(mapping_vals)
                    mappings_created += 1
                    _logger.info(f"Created mesh operation mapping for template: {template.name}")

        _logger.info(f"Mesh operation mapping setup completed. Created {mappings_created} operation mappings.")

    except Exception as e:
        _logger.error(f"Error in mesh operation mapping setup: {str(e)}")

def uninstall_hook(env):
    """Uninstall hook - simplified to avoid dependency issues"""
    # For now, just pass - the module should uninstall cleanly
    pass
