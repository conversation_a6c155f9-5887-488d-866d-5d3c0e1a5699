<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Mesh-Related Calculated Fields -->
        <!-- These fields enable automatic mesh cut operation creation based on door dimensions -->
        
        <!-- Core mesh detection field -->
        <record id="calc_field_mesh_required" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_required</field>
            <field name="description">Whether mesh is required for this configuration - true when door dimensions exist</field>
            <field name="formula">(_CALCULATED_largest_door_width > 0) &amp;&amp; (_CALCULATED_largest_door_height > 0)</field>
            <field name="sequence">200</field>
            <field name="category">condition</field>
            <field name="data_type">boolean</field>
            <field name="depends_on">_CALCULATED_largest_door_width,_CALCULATED_largest_door_height</field>
            <field name="active">True</field>
        </record>
        
        <!-- Mesh width using existing door dimension calculations -->
        <record id="calc_field_mesh_width" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_width</field>
            <field name="description">Required mesh width using largest door width</field>
            <field name="formula">_CALCULATED_mesh_required ? (_CALCULATED_largest_door_width || 0) : 0</field>
            <field name="sequence">201</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">_CALCULATED_mesh_required,_CALCULATED_largest_door_width</field>
            <field name="active">True</field>
        </record>

        <!-- Mesh height using existing door dimension calculations -->
        <record id="calc_field_mesh_height" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_height</field>
            <field name="description">Required mesh height using largest door height</field>
            <field name="formula">_CALCULATED_mesh_required ? (_CALCULATED_largest_door_height || 0) : 0</field>
            <field name="sequence">202</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">_CALCULATED_mesh_required,_CALCULATED_largest_door_height</field>
            <field name="active">True</field>
        </record>
        
        <!-- Mesh series detection - default to saltwater for now -->
        <record id="calc_field_mesh_series" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_series</field>
            <field name="description">Mesh series type - defaults to saltwater</field>
            <field name="formula">_CALCULATED_mesh_required ? 'saltwaterseries' : ''</field>
            <field name="sequence">203</field>
            <field name="category">basic</field>
            <field name="data_type">string</field>
            <field name="depends_on">_CALCULATED_mesh_required</field>
            <field name="active">True</field>
        </record>
        
        <!-- Total mesh area calculation -->
        <record id="calc_field_mesh_area" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_area</field>
            <field name="description">Total mesh area in square mm</field>
            <field name="formula">_CALCULATED_mesh_required ? (_CALCULATED_mesh_width * _CALCULATED_mesh_height) : 0</field>
            <field name="sequence">204</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">_CALCULATED_mesh_required,_CALCULATED_mesh_width,_CALCULATED_mesh_height</field>
            <field name="active">True</field>
        </record>
        

        
        <!-- Operation trigger field -->
        <record id="calc_field_mesh_operation_required" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_operation_required</field>
            <field name="description">Whether a mesh cut operation should be created</field>
            <field name="formula">_CALCULATED_mesh_required &amp;&amp; _CALCULATED_mesh_width &gt; 0 &amp;&amp; _CALCULATED_mesh_height &gt; 0</field>
            <field name="sequence">206</field>
            <field name="category">condition</field>
            <field name="data_type">boolean</field>
            <field name="depends_on">_CALCULATED_mesh_required,_CALCULATED_mesh_width,_CALCULATED_mesh_height</field>
            <field name="active">True</field>
        </record>
        
        <!-- Operation type selection -->
        <record id="calc_field_mesh_operation_type" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_operation_type</field>
            <field name="description">Type of mesh operation to create</field>
            <field name="formula">_CALCULATED_mesh_operation_required ? (_CALCULATED_mesh_area &gt; 1000000 ? 'precision' : 'standard') : 'none'</field>
            <field name="sequence">207</field>
            <field name="category">condition</field>
            <field name="data_type">string</field>
            <field name="depends_on">_CALCULATED_mesh_operation_required,_CALCULATED_mesh_area</field>
            <field name="active">True</field>
        </record>
        
        <!-- Additional helper fields for advanced scenarios -->
        
        <!-- Mesh area in square meters (for easier cost calculations) -->
        <record id="calc_field_mesh_area_m2" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_area_m2</field>
            <field name="description">Mesh area in square meters</field>
            <field name="formula">_CALCULATED_mesh_area / 1000000</field>
            <field name="sequence">208</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">_CALCULATED_mesh_area</field>
            <field name="active">True</field>
        </record>
        
        <!-- Mesh perimeter calculation (useful for frame calculations) -->
        <record id="calc_field_mesh_perimeter" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_perimeter</field>
            <field name="description">Mesh perimeter in mm (for frame calculations)</field>
            <field name="formula">_CALCULATED_mesh_required ? (2 * _CALCULATED_mesh_width + 2 * _CALCULATED_mesh_height) : 0</field>
            <field name="sequence">209</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">_CALCULATED_mesh_required,_CALCULATED_mesh_width,_CALCULATED_mesh_height</field>
            <field name="active">True</field>
        </record>
        
        <!-- Mesh aspect ratio (width/height) for cutting optimization -->
        <record id="calc_field_mesh_aspect_ratio" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_aspect_ratio</field>
            <field name="description">Mesh aspect ratio (width/height) for cutting optimization</field>
            <field name="formula">_CALCULATED_mesh_required &amp;&amp; _CALCULATED_mesh_height &gt; 0 ? (_CALCULATED_mesh_width / _CALCULATED_mesh_height) : 0</field>
            <field name="sequence">210</field>
            <field name="category">basic</field>
            <field name="data_type">number</field>
            <field name="depends_on">_CALCULATED_mesh_required,_CALCULATED_mesh_width,_CALCULATED_mesh_height</field>
            <field name="active">True</field>
        </record>
        
        <!-- Mesh size category for easy filtering -->
        <record id="calc_field_mesh_size_category" model="config.matrix.calculated.field">
            <field name="name">_CALCULATED_mesh_size_category</field>
            <field name="description">Mesh size category for easy filtering and reporting</field>
            <field name="formula">_CALCULATED_mesh_required ? (_CALCULATED_mesh_area &lt; 100000 ? 'small' : (_CALCULATED_mesh_area &lt; 500000 ? 'medium' : (_CALCULATED_mesh_area &lt; 1000000 ? 'large' : 'extra_large'))) : 'none'</field>
            <field name="sequence">211</field>
            <field name="category">condition</field>
            <field name="data_type">string</field>
            <field name="depends_on">_CALCULATED_mesh_required,_CALCULATED_mesh_area</field>
            <field name="active">True</field>
        </record>
        
    </data>
</odoo>
