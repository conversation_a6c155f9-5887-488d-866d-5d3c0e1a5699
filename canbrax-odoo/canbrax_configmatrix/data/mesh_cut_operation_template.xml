<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Mesh Cut Operation Template -->
        <record id="operation_template_mesh_cut" model="config.matrix.operation.template">
            <field name="name">Mesh Cut Operation</field>
            <field name="sequence">100</field>
            <field name="active">True</field>
            <field name="category">cutting</field>
            <field name="description">Automatically creates mesh cut operations based on calculated door dimensions</field>

            <!-- Mesh-specific fields -->
            <field name="is_mesh_operation">True</field>
            <field name="mesh_operation_type">auto</field>
            <field name="auto_find_mesh">True</field>
            <field name="create_bom_component">True</field>
            
            <!-- Work Center - Will be set by post-install hook -->
            <!-- <field name="workcenter_id" ref="mrp.workcenter_1"/> -->
            
            <!-- Simple duration and cost -->
            <field name="duration_formula">30</field>
            <field name="default_duration">30.0</field>

            <!-- Simple cost calculation based on area -->
            <field name="cost_formula">(_CALCULATED_mesh_area / 1000000) * 2.5</field>
            
            <!-- Matrix type for variable calculation -->
            <field name="matrix_type">fixed</field>
            
            <!-- Worksheet content -->
            <field name="worksheet_type">text</field>
            <field name="worksheet_content">Mesh Cut Operation Instructions:
1. Review required dimensions: {_CALCULATED_mesh_width}mm x {_CALCULATED_mesh_height}mm
2. Mesh series: {_CALCULATED_mesh_series}
3. Check mesh cut operation record for optimal cutting plan
4. Follow cutting diagram if available
5. Create byproducts as specified in cut plan</field>
            
            <!-- Notes -->
            <field name="notes">This operation automatically creates mesh cut operation records and finds the best mesh cutting option based on calculated door dimensions. The operation can be opened to view cutting diagrams and modify selections if needed.</field>
        </record>
        
        <!-- Alternative template for precision mesh operations -->
        <record id="operation_template_mesh_cut_precision" model="config.matrix.operation.template">
            <field name="name">Precision Mesh Cut Operation</field>
            <field name="sequence">101</field>
            <field name="active">True</field>
            <field name="category">cutting</field>
            <field name="description">High-precision mesh cutting for large mesh requirements</field>

            <!-- Mesh-specific fields -->
            <field name="is_mesh_operation">True</field>
            <field name="mesh_operation_type">precision</field>
            <field name="auto_find_mesh">True</field>
            <field name="create_bom_component">True</field>

            <!-- Work Center - Will be set by post-install hook -->
            <!-- <field name="workcenter_id" ref="mrp.workcenter_1"/> -->

            <!-- Longer duration for precision work -->
            <field name="duration_formula">60</field>
            <field name="default_duration">60.0</field>

            <!-- Higher cost for precision work -->
            <field name="cost_formula">(_CALCULATED_mesh_area / 1000000) * 4.0</field>

            <!-- Matrix type -->
            <field name="matrix_type">fixed</field>

            <!-- Worksheet content -->
            <field name="worksheet_type">text</field>
            <field name="worksheet_content">Precision Mesh Cut Operation Instructions:
1. Required dimensions: {_CALCULATED_mesh_width}mm x {_CALCULATED_mesh_height}mm
2. Mesh series: {_CALCULATED_mesh_series}
3. Use precision cutting tools and techniques
4. Review cutting diagram carefully
5. Quality check all dimensions before proceeding</field>

            <!-- Notes -->
            <field name="notes">Used for large mesh cutting operations that require higher precision and more time.</field>
        </record>
    </data>
</odoo>
