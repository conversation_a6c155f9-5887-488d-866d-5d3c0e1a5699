<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Sample Cut Operations for Testing -->
        
        <!-- Small pieces (should find unplanned off-cuts) -->
        <record id="sample_cut_op_01" model="mesh.cut.operation">
            <field name="name">Sample 01 - Small Window 300x250mm</field>
            <field name="required_width">300</field>
            <field name="required_height">250</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_02" model="mesh.cut.operation">
            <field name="name">Sample 02 - Small Door Panel 350x280mm</field>
            <field name="required_width">350</field>
            <field name="required_height">280</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_03" model="mesh.cut.operation">
            <field name="name">Sample 03 - Medium Window 400x300mm</field>
            <field name="required_width">400</field>
            <field name="required_height">300</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <!-- Medium pieces (should find planned off-cuts) -->
        <record id="sample_cut_op_04" model="mesh.cut.operation">
            <field name="name">Sample 04 - Medium Window 480x280mm</field>
            <field name="required_width">480</field>
            <field name="required_height">280</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_05" model="mesh.cut.operation">
            <field name="name">Sample 05 - Standard Door Panel 500x350mm</field>
            <field name="required_width">500</field>
            <field name="required_height">350</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_06" model="mesh.cut.operation">
            <field name="name">Sample 06 - Large Window 600x400mm</field>
            <field name="required_width">600</field>
            <field name="required_height">400</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <!-- Large pieces (should find master sheets) -->
        <record id="sample_cut_op_07" model="mesh.cut.operation">
            <field name="name">Sample 07 - Large Door 800x600mm</field>
            <field name="required_width">800</field>
            <field name="required_height">600</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_08" model="mesh.cut.operation">
            <field name="name">Sample 08 - Extra Large Door 1000x800mm</field>
            <field name="required_width">1000</field>
            <field name="required_height">800</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_09" model="mesh.cut.operation">
            <field name="name">Sample 09 - Sliding Door Panel 900x700mm</field>
            <field name="required_width">900</field>
            <field name="required_height">700</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_10" model="mesh.cut.operation">
            <field name="name">Sample 10 - Wide Door 1100x600mm</field>
            <field name="required_width">1100</field>
            <field name="required_height">600</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <!-- Edge cases -->
        <record id="sample_cut_op_11" model="mesh.cut.operation">
            <field name="name">Sample 11 - Custom Size 1 450x380mm</field>
            <field name="required_width">450</field>
            <field name="required_height">380</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_12" model="mesh.cut.operation">
            <field name="name">Sample 12 - Custom Size 2 550x420mm</field>
            <field name="required_width">550</field>
            <field name="required_height">420</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_13" model="mesh.cut.operation">
            <field name="name">Sample 13 - Custom Size 3 750x500mm</field>
            <field name="required_width">750</field>
            <field name="required_height">500</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_14" model="mesh.cut.operation">
            <field name="name">Sample 14 - Small Custom 320x280mm</field>
            <field name="required_width">320</field>
            <field name="required_height">280</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="sample_cut_op_15" model="mesh.cut.operation">
            <field name="name">Sample 15 - Oversized Panel 1200x900mm</field>
            <field name="required_width">1200</field>
            <field name="required_height">900</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>
    </data>
</odoo>
