<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        
        <!-- Mesh Product Categories -->
        <record id="product_category_mesh" model="product.category">
            <field name="name">Mesh</field>
            <field name="parent_id" ref="product.product_category_all"/>
        </record>
        
        <record id="product_category_mesh_master" model="product.category">
            <field name="name">Master Sheets</field>
            <field name="parent_id" ref="product_category_mesh"/>
        </record>
        
        <record id="product_category_mesh_offcuts" model="product.category">
            <field name="name">Off-cuts</field>
            <field name="parent_id" ref="product_category_mesh"/>
        </record>
        
        <!-- Sample Mesh Cut Matrix -->
        <record id="mesh_cut_matrix_saltwater" model="mesh.cut.matrix">
            <field name="name">Saltwater Series Cutting Matrix</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="description">Standard cutting matrix for Saltwater series mesh products</field>
            <field name="height_values">100,200,300,400,500,600,700,800,900,1000,1100,1200,1300,1400,1500</field>
            <field name="width_values">100,200,300,325,375,425,475,525,575,620,675,725,775,825,875,925,975,1000,1100,1200</field>
            <field name="matrix_data">{"1100_620": {"is_master": true, "cut_template": "Master_1100x620"}, "1250_1000": {"is_master": true, "cut_template": "Master_1250x1000"}, "800_500": {"arrow": "right", "target_size": "800x620"}, "900_600": {"arrow": "down", "target_size": "1100x600"}, "300_800": {"arrow": "right", "target_size": "620x800"}, "400_700": {"arrow": "down", "target_size": "620x800"}, "500_600": {"arrow": "right", "target_size": "620x600"}, "100_100": {"arrow": "right", "target_size": "325x200"}, "200_300": {"arrow": "right", "target_size": "325x300"}}</field>
            <field name="cut_rules">{"master_sheets": {"1100x620": {"max_cuts": 4}, "1250x1000": {"max_cuts": 6}}, "cutting_rules": {"efficiency_threshold": 70}}</field>
        </record>
        
        <!-- Sample Cut Plans -->
        <record id="mesh_cut_plan_1100x620" model="mesh.cut.plan">
            <field name="matrix_id" ref="mesh_cut_matrix_saltwater"/>
            <field name="master_width">1100</field>
            <field name="master_height">620</field>
            <field name="cut_instructions">Standard cutting plan for 1100x620mm master sheet. Cut along marked lines to create planned off-cuts.</field>
            <field name="cut_diagram"><![CDATA[
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Grid background -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)"/>

  <!-- Header -->
  <rect x="200" y="50" width="400" height="60" fill="#e6ccff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="400" y="75" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#dc2626">
    ** SPLIT OFFCUT IN 1/2 TO CREATE
  </text>
  <text x="400" y="95" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#dc2626">
    2 - 6x12 PLANNED OFFCUTS **
  </text>

  <!-- Main cutting area -->
  <rect x="150" y="150" width="500" height="350" fill="none" stroke="#000" stroke-width="3"/>

  <!-- Left section - 1/2 of O/C -->
  <rect x="150" y="150" width="150" height="175" fill="#e6ccff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="225" y="200" text-anchor="middle" font-family="Arial" font-size="24" font-weight="bold" fill="#8b5cf6">1/2</text>
  <text x="225" y="225" text-anchor="middle" font-family="Arial" font-size="18" fill="#8b5cf6">of</text>
  <text x="225" y="250" text-anchor="middle" font-family="Arial" font-size="24" font-weight="bold" fill="#8b5cf6">O/C</text>

  <!-- Top right section - 6x12 Plan O/C -->
  <ellipse cx="450" cy="200" rx="120" ry="40" fill="none" stroke="#8b5cf6" stroke-width="3"/>
  <text x="450" y="190" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold">6 x 12</text>
  <text x="450" y="210" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold">Plan O/C</text>

  <!-- Bottom left section - 1/2 of O/C -->
  <rect x="150" y="325" width="150" height="175" fill="#e6ccff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="225" y="375" text-anchor="middle" font-family="Arial" font-size="24" font-weight="bold" fill="#8b5cf6">1/2</text>
  <text x="225" y="400" text-anchor="middle" font-family="Arial" font-size="18" fill="#8b5cf6">of</text>
  <text x="225" y="425" text-anchor="middle" font-family="Arial" font-size="24" font-weight="bold" fill="#8b5cf6">O/C</text>

  <!-- Bottom right section - 6x12 Plan O/C -->
  <ellipse cx="450" cy="375" rx="120" ry="40" fill="none" stroke="#8b5cf6" stroke-width="3"/>
  <text x="450" y="365" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold">6 x 12</text>
  <text x="450" y="385" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold">Plan O/C</text>

  <!-- Cut line indicator -->
  <text x="380" y="290" text-anchor="middle" font-family="Arial" font-size="24" font-weight="bold">+</text>

  <!-- 1st Cut label -->
  <rect x="300" y="520" width="80" height="25" fill="#ff69b4" stroke="#ff1493" stroke-width="1"/>
  <text x="340" y="537" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">1st Cut</text>

  <!-- 2nd Cut label -->
  <rect x="580" y="450" width="80" height="25" fill="#ff8c00" stroke="#ff6347" stroke-width="1"/>
  <text x="620" y="467" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="white">2nd Cut</text>

  <!-- Dimensions -->
  <text x="50" y="325" text-anchor="middle" font-family="Arial" font-size="48" font-weight="bold">1670</text>

  <text x="225" y="550" text-anchor="middle" font-family="Arial" font-size="36" font-weight="bold" fill="#ff69b4">620</text>

  <text x="550" y="550" text-anchor="middle" font-family="Arial" font-size="36" font-weight="bold" fill="#ff8c00">1100</text>

  <text x="400" y="580" text-anchor="middle" font-family="Arial" font-size="48" font-weight="bold">1125</text>

  <!-- Hatched area for waste -->
  <pattern id="hatch" patternUnits="userSpaceOnUse" width="4" height="4">
    <path d="M-1,1 l2,-2 M0,4 l4,-4 M3,5 l2,-2" stroke="#666" stroke-width="1"/>
  </pattern>
  <rect x="650" y="150" width="20" height="350" fill="url(#hatch)" stroke="#666" stroke-width="1"/>

  <!-- Small green square in corner -->
  <rect x="720" y="480" width="30" height="30" fill="#22c55e" stroke="#16a34a" stroke-width="2"/>
</svg>
            ]]></field>
        </record>
        
        <record id="mesh_cut_plan_1250x1000" model="mesh.cut.plan">
            <field name="matrix_id" ref="mesh_cut_matrix_saltwater"/>
            <field name="master_width">1250</field>
            <field name="master_height">1000</field>
            <field name="cut_instructions">Standard cutting plan for 1250x1000mm master sheet. Optimized for maximum material utilization.</field>
            <field name="cut_diagram"><![CDATA[
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Grid background -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)"/>

  <!-- Header -->
  <rect x="150" y="30" width="500" height="50" fill="#cce5ff" stroke="#3b82f6" stroke-width="2"/>
  <text x="400" y="50" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#1e40af">
    MASTER SHEET 1250x1000mm - OPTIMIZED CUTTING PLAN
  </text>
  <text x="400" y="70" text-anchor="middle" font-family="Arial" font-size="12" fill="#1e40af">
    Maximum material utilization with minimal waste
  </text>

  <!-- Main cutting area -->
  <rect x="100" y="100" width="600" height="400" fill="none" stroke="#000" stroke-width="4"/>

  <!-- Section 1 - Top Left -->
  <rect x="100" y="100" width="200" height="150" fill="#e6f3ff" stroke="#3b82f6" stroke-width="2"/>
  <text x="200" y="170" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold">Section A</text>
  <text x="200" y="190" text-anchor="middle" font-family="Arial" font-size="12">600x400mm</text>

  <!-- Section 2 - Top Right -->
  <rect x="300" y="100" width="200" height="150" fill="#fff2e6" stroke="#f59e0b" stroke-width="2"/>
  <text x="400" y="170" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold">Section B</text>
  <text x="400" y="190" text-anchor="middle" font-family="Arial" font-size="12">600x400mm</text>

  <!-- Section 3 - Middle Left -->
  <rect x="100" y="250" width="150" height="125" fill="#e6ffe6" stroke="#22c55e" stroke-width="2"/>
  <text x="175" y="310" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">C</text>
  <text x="175" y="325" text-anchor="middle" font-family="Arial" font-size="10">450x300</text>

  <!-- Section 4 - Middle Center -->
  <rect x="250" y="250" width="150" height="125" fill="#ffe6f2" stroke="#ec4899" stroke-width="2"/>
  <text x="325" y="310" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">D</text>
  <text x="325" y="325" text-anchor="middle" font-family="Arial" font-size="10">450x300</text>

  <!-- Section 5 - Middle Right -->
  <rect x="400" y="250" width="150" height="125" fill="#f3e6ff" stroke="#8b5cf6" stroke-width="2"/>
  <text x="475" y="310" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">E</text>
  <text x="475" y="325" text-anchor="middle" font-family="Arial" font-size="10">450x300</text>

  <!-- Section 6 - Bottom -->
  <rect x="100" y="375" width="450" height="125" fill="#fef3c7" stroke="#f59e0b" stroke-width="2"/>
  <text x="325" y="435" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold">Section F - Large Panel</text>
  <text x="325" y="455" text-anchor="middle" font-family="Arial" font-size="12">1000x300mm</text>

  <!-- Waste area -->
  <pattern id="waste" patternUnits="userSpaceOnUse" width="6" height="6">
    <path d="M-1,1 l2,-2 M0,6 l6,-6 M5,7 l2,-2" stroke="#dc2626" stroke-width="1"/>
  </pattern>
  <rect x="550" y="375" width="150" height="125" fill="url(#waste)" stroke="#dc2626" stroke-width="2"/>
  <text x="625" y="435" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="#dc2626">WASTE</text>
  <text x="625" y="450" text-anchor="middle" font-family="Arial" font-size="10" fill="#dc2626">150x300</text>

  <!-- Cut lines -->
  <line x1="300" y1="100" x2="300" y2="500" stroke="#000" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="100" y1="250" x2="700" y2="250" stroke="#000" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="100" y1="375" x2="700" y2="375" stroke="#000" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="250" y1="250" x2="250" y2="375" stroke="#000" stroke-width="1" stroke-dasharray="3,3"/>
  <line x1="400" y1="250" x2="400" y2="375" stroke="#000" stroke-width="1" stroke-dasharray="3,3"/>
  <line x1="550" y1="250" x2="550" y2="500" stroke="#000" stroke-width="2" stroke-dasharray="5,5"/>

  <!-- Dimensions -->
  <text x="50" y="300" text-anchor="middle" font-family="Arial" font-size="24" font-weight="bold" transform="rotate(-90 50 300)">1000</text>
  <text x="400" y="540" text-anchor="middle" font-family="Arial" font-size="24" font-weight="bold">1250</text>

  <!-- Efficiency indicator -->
  <rect x="550" y="30" width="120" height="40" fill="#22c55e" stroke="#16a34a" stroke-width="2"/>
  <text x="610" y="45" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="white">EFFICIENCY</text>
  <text x="610" y="60" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="white">88%</text>
</svg>
            ]]></field>
        </record>
        
        <!-- Sample Mesh Products -->
        
        <!-- Saltwater Series Master Sheets -->
        <record id="product_saltwater_master_1100x620" model="product.template">
            <field name="name">Saltwater Mesh Master Sheet 1100x620mm</field>
            <field name="categ_id" ref="product_category_mesh_master"/>
            <field name="type">consu</field>
            <field name="is_mesh_product" eval="True"/>
            <field name="mesh_type">master</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="mesh_width">1100</field>
            <field name="mesh_height">620</field>
            <field name="list_price">150.00</field>
            <field name="standard_price">120.00</field>
            <field name="tracking">none</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="cut_matrix_id" ref="mesh_cut_matrix_saltwater"/>
        </record>

        <record id="product_saltwater_master_1250x1000" model="product.template">
            <field name="name">Saltwater Mesh Master Sheet 1250x1000mm</field>
            <field name="categ_id" ref="product_category_mesh_master"/>
            <field name="type">consu</field>
            <field name="is_mesh_product" eval="True"/>
            <field name="mesh_type">master</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="mesh_width">1250</field>
            <field name="mesh_height">1000</field>
            <field name="list_price">200.00</field>
            <field name="standard_price">160.00</field>
            <field name="tracking">none</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
            <field name="cut_matrix_id" ref="mesh_cut_matrix_saltwater"/>
        </record>
        
        <!-- Sample Planned Off-cuts -->
        <record id="product_saltwater_offcut_600x400" model="product.template">
            <field name="name">Saltwater Mesh Off-cut 600x400mm</field>
            <field name="categ_id" ref="product_category_mesh_offcuts"/>
            <field name="type">consu</field>
            <field name="is_mesh_product" eval="True"/>
            <field name="mesh_type">planned</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="mesh_width">600</field>
            <field name="mesh_height">400</field>
            <field name="list_price">75.00</field>
            <field name="standard_price">60.00</field>
            <field name="tracking">none</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
        </record>

        <record id="product_saltwater_offcut_500x300" model="product.template">
            <field name="name">Saltwater Mesh Off-cut 500x300mm</field>
            <field name="categ_id" ref="product_category_mesh_offcuts"/>
            <field name="type">consu</field>
            <field name="is_mesh_product" eval="True"/>
            <field name="mesh_type">planned</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="mesh_width">500</field>
            <field name="mesh_height">300</field>
            <field name="list_price">50.00</field>
            <field name="standard_price">40.00</field>
            <field name="tracking">none</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
        </record>

        <!-- Unplanned Off-cut Product (generic) -->
        <record id="product_saltwater_unplanned_offcut" model="product.template">
            <field name="name">Saltwater Mesh Unplanned Off-cut</field>
            <field name="categ_id" ref="product_category_mesh_offcuts"/>
            <field name="type">consu</field>
            <field name="is_mesh_product" eval="True"/>
            <field name="mesh_type">unplanned</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="mesh_width">0</field>
            <field name="mesh_height">0</field>
            <field name="list_price">0.00</field>
            <field name="standard_price">0.00</field>
            <field name="tracking">lot</field>
            <field name="uom_id" ref="uom.product_uom_unit"/>
            <field name="uom_po_id" ref="uom.product_uom_unit"/>
        </record>
        
        <!-- Sample Byproducts for Cut Plans -->
        <record id="mesh_byproduct_600x400_from_1100x620" model="mesh.cut.byproduct">
            <field name="cut_plan_id" ref="mesh_cut_plan_1100x620"/>
            <field name="product_id" ref="product_saltwater_offcut_600x400"/>
            <field name="width">600</field>
            <field name="height">400</field>
            <field name="quantity">1</field>
        </record>
        
        <record id="mesh_byproduct_500x300_from_1100x620" model="mesh.cut.byproduct">
            <field name="cut_plan_id" ref="mesh_cut_plan_1100x620"/>
            <field name="product_id" ref="product_saltwater_offcut_500x300"/>
            <field name="width">500</field>
            <field name="height">300</field>
            <field name="quantity">1</field>
        </record>
        
        <record id="mesh_byproduct_600x400_from_1250x1000" model="mesh.cut.byproduct">
            <field name="cut_plan_id" ref="mesh_cut_plan_1250x1000"/>
            <field name="product_id" ref="product_saltwater_offcut_600x400"/>
            <field name="width">600</field>
            <field name="height">400</field>
            <field name="quantity">2</field>
        </record>
        
        <!-- Sample Mesh Cut Operations -->
        <record id="mesh_cut_operation_sample_1" model="mesh.cut.operation">
            <field name="name">Cut Operation - Door Screen 580x380mm</field>
            <field name="required_width">580</field>
            <field name="required_height">380</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <!-- Test operations for different mesh types -->
        <record id="mesh_cut_operation_test_unplanned" model="mesh.cut.operation">
            <field name="name">Test - Small Window 300x250mm (should find unplanned)</field>
            <field name="required_width">300</field>
            <field name="required_height">250</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="mesh_cut_operation_test_planned" model="mesh.cut.operation">
            <field name="name">Test - Medium Window 480x280mm (should find planned)</field>
            <field name="required_width">480</field>
            <field name="required_height">280</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>

        <record id="mesh_cut_operation_test_master" model="mesh.cut.operation">
            <field name="name">Test - Large Door 1000x800mm (should find master)</field>
            <field name="required_width">1000</field>
            <field name="required_height">800</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">draft</field>
        </record>
        
        <record id="mesh_cut_operation_sample_2" model="mesh.cut.operation">
            <field name="name">Cut Operation - Window Screen 450x280mm</field>
            <field name="required_width">450</field>
            <field name="required_height">280</field>
            <field name="required_qty">2.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">confirmed</field>
            <field name="source_type">planned</field>
            <field name="actual_width">500</field>
            <field name="actual_height">300</field>
            <field name="waste_width">50</field>
            <field name="waste_height">20</field>
            <field name="efficiency">84.0</field>
        </record>
        
        <record id="mesh_cut_operation_sample_3" model="mesh.cut.operation">
            <field name="name">Cut Operation - Large Door 1000x800mm</field>
            <field name="required_width">1000</field>
            <field name="required_height">800</field>
            <field name="required_qty">1.0</field>
            <field name="mesh_series">saltwaterseries</field>
            <field name="state">done</field>
            <field name="source_type">master</field>
            <field name="actual_width">1250</field>
            <field name="actual_height">1000</field>
            <field name="waste_width">250</field>
            <field name="waste_height">200</field>
            <field name="efficiency">80.0</field>
        </record>

        <!-- Sample Stock Lots for Unplanned Off-cuts -->
        <record id="lot_unplanned_450x380" model="stock.lot">
            <field name="name">SW_450x380_20241208_001</field>
            <field name="product_id" ref="product_saltwater_unplanned_offcut"/>
            <field name="mesh_width">450</field>
            <field name="mesh_height">380</field>
            <field name="is_unplanned_offcut">True</field>
        </record>

        <record id="lot_unplanned_320x280" model="stock.lot">
            <field name="name">SW_320x280_20241208_002</field>
            <field name="product_id" ref="product_saltwater_unplanned_offcut"/>
            <field name="mesh_width">320</field>
            <field name="mesh_height">280</field>
            <field name="is_unplanned_offcut">True</field>
        </record>

        <record id="lot_unplanned_500x400" model="stock.lot">
            <field name="name">SW_500x400_20241208_003</field>
            <field name="product_id" ref="product_saltwater_unplanned_offcut"/>
            <field name="mesh_width">500</field>
            <field name="mesh_height">400</field>
            <field name="is_unplanned_offcut">True</field>
        </record>

        <record id="lot_unplanned_600x350" model="stock.lot">
            <field name="name">SW_600x350_20241208_004</field>
            <field name="product_id" ref="product_saltwater_unplanned_offcut"/>
            <field name="mesh_width">600</field>
            <field name="mesh_height">350</field>
            <field name="is_unplanned_offcut">True</field>
        </record>



    </data>
</odoo>
