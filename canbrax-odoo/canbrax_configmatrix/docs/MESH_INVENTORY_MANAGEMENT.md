# Mesh Inventory Management System

## Overview

The Mesh Inventory Management System is an integrated solution within the ConfigMatrix module that provides sophisticated tracking and optimization of mesh materials for door and screen manufacturing. The system handles three types of mesh inventory:

1. **Master Sheets** - Full-size sheets purchased from manufacturers
2. **Planned Off-cuts** - Predetermined sizes created from cutting operations  
3. **Unplanned Off-cuts** - Random sizes stored with specific lot tracking

## Key Features

### Intelligent Mesh Selection
- **Priority-based Search**: Automatically searches unplanned → planned → master sheets
- **Efficiency Optimization**: Calculates material efficiency and waste percentages
- **Real-time Availability**: Checks stock levels during selection process

### Integration with ConfigMatrix
- **Automatic Detection**: Identifies configurations requiring mesh materials
- **Dimension Extraction**: Pulls required dimensions from configuration values
- **BOM Integration**: Includes specific mesh components with lot tracking

### Cutting Operations Management
- **Operation Tracking**: Full lifecycle management from draft to completion
- **Byproduct Creation**: Automatic generation of off-cuts from master sheet cutting
- **Stock Move Integration**: Seamless inventory adjustments

## Data Models

### Core Models

#### `mesh.cut.matrix`
Stores cutting rules and master sheet configurations for each mesh series.

**Key Fields:**
- `mesh_series`: Saltwater, Diamond, or Flyscreen
- `cut_rules`: JSON structure with cutting configurations
- `cut_plan_ids`: Related cutting plans

#### `mesh.cut.operation`
Manages individual cutting operations from requirement to completion.

**Key Fields:**
- `required_width/height`: Dimensions needed
- `source_type`: unplanned/planned/master
- `efficiency`: Material utilization percentage
- `state`: draft/confirmed/cutting/done

#### `mesh.cut.plan`
Defines how master sheets are cut and what byproducts are created.

**Key Fields:**
- `master_width/height`: Master sheet dimensions
- `byproduct_ids`: Planned off-cuts created
- `waste_percentage`: Calculated waste

### Extended Models

#### `product.template` Extensions
- `is_mesh_product`: Boolean flag
- `mesh_type`: master/planned/unplanned
- `mesh_series`: Product series type
- `mesh_width/height`: Dimensions

#### `stock.lot` Extensions  
- `mesh_width/height`: Lot-specific dimensions
- `source_master_lot_id`: Traceability to source
- `display_name_with_size`: Enhanced display

#### `config.matrix.configuration` Extensions
- `requires_mesh`: Auto-computed from field values
- `mesh_width/height_required`: Extracted dimensions
- `mesh_cut_operation_id`: Linked cutting operation

## User Interface

### Menu Structure
```
Mesh Management
├── Operations
│   ├── Create Cut Operation (Wizard)
│   └── Cut Operations (List)
├── Configuration  
│   ├── Cut Matrices
│   ├── Cut Plans
│   └── Import from Excel
└── Inventory
    ├── Master Sheets
    ├── Unplanned Off-cuts
    └── Lots by Dimensions
```

### Key Views

#### Cut Operation Form
- Requirements section (dimensions, series, quantity)
- Source details (product, lot, efficiency)
- Byproducts tracking
- State management workflow

#### Product Mesh Properties Tab
- Mesh type and series configuration
- Dimension specifications
- Relationships to masters and off-cuts
- Quick access to operations and stock

#### Configuration Mesh Requirements Tab
- Auto-computed mesh requirements
- Link to cutting operations
- Quick operation creation

## Workflows

### 1. Configuration to Cutting Operation

```mermaid
graph TD
    A[Configuration Created] --> B{Requires Mesh?}
    B -->|Yes| C[Extract Dimensions]
    C --> D[Create Cut Operation]
    D --> E[Find Best Mesh]
    E --> F[Assign Source]
    F --> G[Confirm Operation]
```

### 2. Mesh Selection Algorithm

```mermaid
graph TD
    A[Search Request] --> B[Search Unplanned Off-cuts]
    B --> C{Found Suitable?}
    C -->|Yes| D[Return Best Option]
    C -->|No| E[Search Planned Off-cuts]
    E --> F{Found Suitable?}
    F -->|Yes| D
    F -->|No| G[Search Master Sheets]
    G --> H{Found Suitable?}
    H -->|Yes| I[Create Cut Plan]
    I --> D
    H -->|No| J[No Options Available]
```

### 3. Cutting Operation Execution

```mermaid
graph TD
    A[Draft Operation] --> B[Find Mesh Source]
    B --> C[Confirm Operation]
    C --> D[Start Cutting]
    D --> E[Complete Cutting]
    E --> F[Create Stock Moves]
    F --> G[Generate Byproducts]
    G --> H[Done]
```

## Excel Import System

### Supported File Formats
The system can import cutting matrix data from Excel files following the pattern:
- `SWS1100x620MS.xlsx` - Saltwater Series 1100x620mm Master Sheet
- `SWS1250x1000MS.xlsx` - Saltwater Series 1250x1000mm Master Sheet
- etc.

### Import Process
1. Upload Excel file via Import Wizard
2. System detects master sheet dimensions from filename
3. Parses cutting plans and byproduct information
4. Creates mesh.cut.matrix and related records
5. Generates cut plans with byproduct specifications

## Configuration Examples

### Mesh Product Setup
```python
# Master Sheet Product
{
    'name': 'Saltwater Mesh Master Sheet 1100x620mm',
    'is_mesh_product': True,
    'mesh_type': 'master',
    'mesh_series': 'saltwater',
    'mesh_width': 1100,
    'mesh_height': 620,
}

# Planned Off-cut Product  
{
    'name': 'Saltwater Mesh Off-cut 600x400mm',
    'is_mesh_product': True,
    'mesh_type': 'planned', 
    'mesh_series': 'saltwater',
    'mesh_width': 600,
    'mesh_height': 400,
}
```

### Cut Matrix Configuration
```json
{
    "master_sheets": {
        "1100x620": {"max_cuts": 4},
        "1250x1000": {"max_cuts": 6}
    },
    "cutting_rules": {
        "efficiency_threshold": 70,
        "waste_tolerance": 50
    }
}
```

## Best Practices

### Product Configuration
1. Set up product categories for mesh types
2. Configure master sheet products with accurate dimensions
3. Create planned off-cut products for common sizes
4. Use generic unplanned off-cut products with lot tracking

### Cutting Matrix Setup
1. Import existing Excel cutting matrices
2. Define cut plans for each master sheet size
3. Specify expected byproducts with quantities
4. Set efficiency thresholds for optimization

### Operational Workflow
1. Configure products to auto-detect mesh requirements
2. Use cutting operation wizard for manual operations
3. Confirm operations before cutting begins
4. Complete operations to update inventory

## Technical Notes

### Dependencies
- `openpyxl` library required for Excel import functionality
- Standard Odoo stock management modules
- ConfigMatrix base functionality

### Performance Considerations
- Mesh dimensions indexed for fast searching
- JSON storage for flexible cutting rules
- Computed fields cached for efficiency

### Customization Points
- Mesh series can be extended via selection field
- Excel parser can be customized for different formats
- Search algorithm priorities can be modified
- Efficiency calculations can be enhanced

## Troubleshooting

### Common Issues
1. **No mesh found**: Check master sheet availability and cutting matrices
2. **Import errors**: Verify Excel file format and openpyxl installation  
3. **Efficiency warnings**: Review cutting plans and waste thresholds
4. **Stock discrepancies**: Ensure cutting operations are properly completed

### Debug Mode
Enable debug logging for detailed operation tracking:
```python
_logger.setLevel(logging.DEBUG)
```
