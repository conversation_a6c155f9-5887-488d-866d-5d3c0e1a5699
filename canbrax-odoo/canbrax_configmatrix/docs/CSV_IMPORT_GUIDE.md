# CSV Import Guide for Config Matrix Fields

## Overview

The CSV import functionality allows you to quickly import field options and their component mappings from a structured CSV file. This is particularly useful for fields with many options or complex component mappings.

## How to Use

### 1. Access the Import Feature

1. Go to **Configuration Matrix** > **Fields**
2. Open a field with type "Selection"
3. Navigate to the **Field Options** tab
4. Click the **"Import from CSV"** button

### 2. CSV File Structure

Your CSV file must have the following required headers:

- `Field Label`: The field name (for reference only)
- `Options/Option Text`: Display text for the option
- `Options/Option Value`: Internal value for the option

Optional headers for component mappings:

- `Options/Component Mappings/Mapping Type`: "Static Product" or "Dynamic Field Match"
- `Options/Component Mappings/Component Product`: Name of the product to map
- `Options/Component Mappings/Quantity Formula`: Formula to calculate quantity
- `Options/Component Mappings/Match Against`: Field to match against for dynamic mapping
- `Options/Component Mappings/Additional Condition`: Extra condition

### 3. Sample CSV Format

Based on your provided file, here's the expected structure:

```csv
Field Label,Template,Technical Name,Options/Option Text,Options/Option Value,Options/Field/Quantity Formula,Options/Has Multiple Components,Options/Component Mappings/Mapping Type,Options/Component Mappings/Base Component,Options/Component Mappings/Component Product,Options/Component Mappings/Match Against,Options/Component Mappings/Quantity Formula,Options/Component Mappings/Additional Condition
Frame Colour,Subcategory - Hinged Door SWS (IMPORT),sws_hinge_frame_colour,Black Custom Matt GN248A,black_custom_matt_gn248a,,TRUE,Static Product,,Commandex Door Frame - Black Custom Matt GN248A,Color,(_CALCULATED_smallest_door_height*2) + _CALCULATED_Top_Width + _CALCULATED_Bottom_Width,
,,,,,,,Static Product,,Secureview Plugh - Black,Color,(_CALCULATED_smallest_door_height - 144) + (_CALCULATED_smallest_door_height - 144) + (_CALCULATED_Top_Width - 144) + (_CALCULATED_Bottom_Width - 144),
```

### 4. Multiple Component Mappings per Option

Each option can have multiple component mappings. To add multiple components for a single option:

1. First row: Include the option text, value, and first component mapping
2. Subsequent rows: Leave option text and value empty, but include additional component mappings

### 5. Import Process

1. Click **"Import from CSV"**
2. Select your CSV file
3. Preview the data in the wizard
4. Choose whether to clear existing options (optional)
5. Click **"Import"**

### 6. Download Template

Click **"Download Template"** in the import wizard to get a sample CSV file with the correct structure.

## Features

### ✅ Supported Features

- **Multiple options**: Import many options at once
- **Component mappings**: Both static products and dynamic field matching
- **Quantity formulas**: Complex calculation formulas for component quantities
- **Validation**: Automatic validation of CSV structure and data
- **Preview**: See data before importing
- **Clear existing**: Option to replace all existing options

### ⚠️ Important Notes

1. **Product Names**: Component products must exist in Odoo with exact name matches
2. **Separator Rows**: Rows with option values starting with "separator_" are skipped
3. **Empty Rows**: Rows without option text and value are skipped
4. **Backup**: Consider backing up your data before importing large CSV files

### 🔧 Troubleshooting

**Error: "Missing required CSV headers"**
- Ensure your CSV has the required columns: Field Label, Options/Option Text, Options/Option Value

**Error: "Component product not found"**
- Check that the product names in your CSV exactly match product names in Odoo

**Error: "No valid option rows found"**
- Ensure at least one row has both Options/Option Text and Options/Option Value filled

## Example Use Cases

1. **Color Options**: Import product variants with different color frames and components
2. **Size Options**: Import size variations with different material quantities
3. **Material Options**: Import different materials with specific component mappings
4. **Configuration Sets**: Import complete option sets for product configurations

## Advanced Features

### Dynamic Field Matching

When using "Dynamic Field Match" mapping type:
- Set `Base Component`: The base component name to search for
- Set `Match Against`: The field name to match against (e.g., "Color")
- The system will find products that match the pattern: "{Base Component} - {Field Value}"

### Quantity Formulas

Quantity formulas support:
- Mathematical operations: `+`, `-`, `*`, `/`
- Calculated fields: `_CALCULATED_fieldname`
- Conditional logic: `field > 100 ? 2 : 1`
- Field references: Direct field name references

## Security

Only users with "Config Matrix Admin" permissions can import CSV files.
