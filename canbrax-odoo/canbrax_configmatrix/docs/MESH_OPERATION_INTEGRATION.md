# Mesh Cut Operation Integration Guide

This document explains how to integrate mesh cut operations into your Product Configurator decision tree using calculated fields and operation mappings.

## Overview

The mesh cut operation integration uses **Option 1: Calculated Field + Operation Mapping** approach with the following components:

1. **Calculated Fields**: Detect mesh requirements and determine dimensions using existing `_CALCULATED_largest_door_width` and `_CALCULATED_largest_door_height`
2. **Operation Templates**: Create mesh cut operations automatically
3. **BOM Integration**: Add mesh products as components and operations to the BOM
4. **User Interface**: Allow users to view/edit operations and cutting diagrams

## Setup Steps

### Step 1: Calculated Fields Are Automatically Available (GLOBAL)

The mesh calculated fields are automatically loaded as **global fields** when the module is updated. They are immediately available to ALL configuration templates without any setup required.

**Available Global Fields:**
- **Core Fields**: `_CALCULATED_mesh_required`, `_CALCULATED_mesh_width`, `_CALCULATED_mesh_height`, `_CALCULATED_mesh_series`
- **Calculation Fields**: `_CALCULATED_mesh_area`, `_CALCULATED_mesh_complexity`, `_CALCULATED_mesh_perimeter`
- **Operation Fields**: `_CALCULATED_mesh_operation_required`, `_CALCULATED_mesh_operation_type`
- **Helper Fields**: `_CALCULATED_mesh_area_m2`, `_CALCULATED_mesh_aspect_ratio`, `_CALCULATED_mesh_size_category`

**Key Calculated Fields:**

```javascript
// Core mesh detection
_CALCULATED_mesh_required: "mesh_type && mesh_type !== 'none' && mesh_type !== '' && mesh_type !== 'no_mesh'"

// Use existing door dimension calculations
_CALCULATED_mesh_width: "_CALCULATED_mesh_required ? (_CALCULATED_largest_door_width || _CALCULATED_door_width || 0) : 0"
_CALCULATED_mesh_height: "_CALCULATED_mesh_required ? (_CALCULATED_largest_door_height || _CALCULATED_door_height || 0) : 0"

// Mesh series detection
_CALCULATED_mesh_series: "_CALCULATED_mesh_required ? (mesh_type === 'saltwater' ? 'saltwater' : (mesh_type === 'diamond' ? 'diamond' : (mesh_type === 'flyscreen' ? 'flyscreen' : 'saltwater'))) : ''"

// Operation triggers
_CALCULATED_mesh_operation_required: "_CALCULATED_mesh_required && _CALCULATED_mesh_width > 0 && _CALCULATED_mesh_height > 0"
_CALCULATED_mesh_operation_type: "_CALCULATED_mesh_operation_required ? (_CALCULATED_mesh_complexity === 'complex' ? 'precision' : 'standard') : 'none'"
```

### Step 2: Configure Operation Templates (AUTOMATED)

The system includes two pre-configured operation templates that are automatically loaded:

1. **Mesh Cut Operation** (Standard) - For simple to medium complexity mesh
2. **Precision Mesh Cut Operation** - For complex or large mesh requirements

These templates are automatically loaded and will:
- Create mesh cut operation records
- Find the best mesh cutting option
- Add mesh products to the BOM
- Calculate appropriate durations and costs

### Step 3: Test the Integration (READY TO USE)

The mesh integration is now **ready to use immediately** after module update:

1. **Create a configuration** with mesh requirements
2. **Set door dimensions** (width/height) - system uses `_CALCULATED_largest_door_width/height`
3. **Select a mesh type** (saltwater, diamond, flyscreen) via `mesh_type` field
4. **Generate BOM** - mesh operations are created automatically
5. **Verify results**:
   - Mesh cut operation is created automatically
   - Best mesh product is selected and added to BOM
   - Cutting operation appears in routing with calculated duration/cost
   - User can click operation to view cutting diagram

## How It Works

### Automatic Process Flow

1. **User Input**: User answers questions including mesh type and door dimensions
2. **Calculated Fields**: System calculates mesh requirements using largest door dimensions
3. **Operation Trigger**: When BOM is generated, mesh operation templates are executed
4. **Mesh Operation Creation**: System creates `mesh.cut.operation` record with:
   - Required dimensions from calculated fields
   - Mesh series from user selection
   - Automatic mesh finding (best fit algorithm)
5. **BOM Integration**: 
   - Selected mesh product added as BOM component
   - Mesh cut operation added to routing
   - Duration and cost calculated automatically

### User Experience

1. **Configuration**: Users configure products normally through decision tree
2. **BOM Generation**: System automatically handles mesh operations
3. **Review/Override**: Users can click on mesh operations to:
   - View cutting diagrams
   - Change mesh selection if needed
   - See byproduct information
   - Review efficiency calculations

## Customization Options

### Mesh Detection Logic

Modify the `_CALCULATED_mesh_required` formula to match your field names:

```javascript
// Example for different field names
_CALCULATED_mesh_required: "screen_type && screen_type !== 'none'"
_CALCULATED_mesh_required: "security_mesh === 'yes'"
_CALCULATED_mesh_required: "flyscreen_required === true"
```

### Dimension Sources

The system uses `_CALCULATED_largest_door_width` and `_CALCULATED_largest_door_height` by default. You can modify to use different sources:

```javascript
// Use specific door dimensions
_CALCULATED_mesh_width: "_CALCULATED_mesh_required ? door_width : 0"
_CALCULATED_mesh_height: "_CALCULATED_mesh_required ? door_height : 0"

// Use opening dimensions
_CALCULATED_mesh_width: "_CALCULATED_mesh_required ? opening_width : 0"
_CALCULATED_mesh_height: "_CALCULATED_mesh_required ? opening_height : 0"
```

### Operation Templates

Create custom operation templates for specific scenarios:

1. **Different Work Centers**: Assign to specific cutting stations
2. **Custom Duration Formulas**: Based on your production requirements
3. **Specialized Worksheets**: Include specific cutting instructions

## Troubleshooting

### Common Issues

1. **No Mesh Operation Created**
   - Check calculated fields are properly configured
   - Verify mesh_type field has valid values
   - Ensure door dimensions are available

2. **Wrong Mesh Selected**
   - Review mesh cutting matrix configuration
   - Check mesh series mapping
   - Verify stock availability

3. **BOM Component Not Added**
   - Check operation template has `create_bom_component = True`
   - Verify mesh product exists in selected option
   - Review BOM generation logs

### Debug Information

Enable debug logging to see mesh operation processing:

```python
# In configuration
_logger.setLevel(logging.DEBUG)
```

Look for log messages like:
- "Mesh operation not required for this configuration"
- "Using mesh operation template: Mesh Cut Operation"
- "Successfully created mesh operation: Cut Mesh - 600x400mm"
- "Added mesh product Saltwater Mesh to BOM"

## Advanced Features

### Multiple Mesh Requirements

For configurations requiring multiple mesh pieces:

```javascript
// Calculate total mesh area for multiple doors
_CALCULATED_total_mesh_area: "_CALCULATED_mesh_required ? (door_count * _CALCULATED_mesh_area) : 0"

// Adjust quantity in operation template
quantity_formula: "door_count || 1"
```

### Conditional Mesh Types

For complex mesh selection logic:

```javascript
// Conditional mesh series based on multiple factors
_CALCULATED_mesh_series: `
  _CALCULATED_mesh_required ? 
    (location === 'coastal' ? 'saltwater' : 
     (security_level === 'high' ? 'diamond' : 'flyscreen')) : ''
`
```

This integration provides a seamless way to automatically handle mesh cutting operations while maintaining flexibility for user overrides and customization.
