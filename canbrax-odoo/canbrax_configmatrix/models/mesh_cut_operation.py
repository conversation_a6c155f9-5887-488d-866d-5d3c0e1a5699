# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from .mesh_constants import MESH_SERIES_SELECTION, DEFAULT_MESH_SERIES
import json
import logging

_logger = logging.getLogger(__name__)

class MeshCutOperation(models.Model):
    _name = 'mesh.cut.operation'
    _description = 'Mesh Cutting Operation'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'
    _rec_name = 'display_name'
    
    name = fields.Char('Operation Name', required=True)
    display_name = fields.Char('Display Name', compute='_compute_display_name', store=True)
    
    # Related order information
    sale_order_id = fields.Many2one('sale.order', 'Sale Order', compute='_compute_sale_order_id', store=True)
    sale_order_line_id = fields.Many2one('sale.order.line', 'Sale Order Line')
    config_id = fields.Many2one('config.matrix.configuration', 'Configuration')
    customer_id = fields.Many2one('res.partner', 'Customer', compute='_compute_customer_id', store=True)
    operation_template_id = fields.Many2one('config.matrix.operation.template', 'Operation Template',
                                           help='Operation template that created this mesh cut operation')
    
    # Mesh hunting fields (NEW)
    mesh_product_id = fields.Many2one('product.product', 'Selected Mesh Product', 
                                     help='Mesh product selected during hunting')
    series = fields.Char('Mesh Series', help='Series from mesh requirements')
    selection_reason = fields.Text('Selection Reason', help='Reason for mesh selection')
    
    # Required dimensions
    required_width = fields.Float('Required Width (mm)', required=True)
    required_height = fields.Float('Required Height (mm)', required=True)
    required_qty = fields.Float('Required Quantity', default=1.0, required=True)

    # Panel identification (NEW for individual hunting)
    panel_number = fields.Integer('Panel Number', default=1, help='Panel number (1 of X)')
    total_panels = fields.Integer('Total Panels', default=1, help='Total number of panels in this configuration')
    panel_position = fields.Char('Panel Position', help='Position identifier (top, bottom, full, etc.)')
    door_reference = fields.Char('Door Reference', help='Which door this panel belongs to (Door 1, Door 2, etc.)')
    mesh_series = fields.Selection(MESH_SERIES_SELECTION, string='Mesh Series', required=True, default=DEFAULT_MESH_SERIES)
    
    # Selected mesh source
    source_type = fields.Selection([
        ('unplanned', 'Unplanned Off-cut'),
        ('planned', 'Planned Off-cut'),
        ('master', 'Master Sheet'),
    ], string='Source Type', readonly=True)


    
    source_product_id = fields.Many2one('product.product', 'Source Product', readonly=True)
    source_lot_id = fields.Many2one('stock.lot', 'Source Lot/Serial', readonly=True)
    source_quant_id = fields.Many2one('stock.quant', 'Source Stock', readonly=True)
    
    # Cut details
    actual_width = fields.Float('Actual Source Width (mm)', readonly=True)
    actual_height = fields.Float('Actual Source Height (mm)', readonly=True)
    waste_width = fields.Float('Waste Width (mm)', readonly=True)
    waste_height = fields.Float('Waste Height (mm)', readonly=True)
    efficiency = fields.Float('Material Efficiency (%)', readonly=True)
    

    
    # Byproducts
    byproduct_ids = fields.One2many('mesh.cut.byproduct.line', 'cut_operation_id', 'Byproducts')
    
    # State management
    state = fields.Selection([
        ('draft', 'Draft'),
        ('auto_selected', 'Auto Selected'),
        ('confirmed', 'Confirmed'),
        ('cutting', 'Cutting'),
        ('done', 'Done'),
        ('cancelled', 'Cancelled'),
    ], default='draft', required=True, tracking=True)
    
    # Stock moves
    consumption_move_id = fields.Many2one('stock.move', 'Consumption Move', readonly=True)
    production_move_ids = fields.One2many('stock.move', 'mesh_cut_operation_id', 'Production Moves')

    # Computed fields for list view
    actual_source_width = fields.Float('Source Width', related='actual_width', readonly=True)
    actual_source_height = fields.Float('Source Height', related='actual_height', readonly=True)

    @api.depends('selected_option_id', 'selected_option_id.matrix_cell_height', 'selected_option_id.matrix_cell_width',
                 'selected_option_id.matrix_arrow_path', 'selected_option_id.matrix_cell_reference',
                 'selected_option_id.cut_plan_id', 'selected_option_id.efficiency_percentage', 'selected_option_id.byproduct_count',
                 'mesh_series')
    def _compute_matrix_result_fields(self):
        """Compute matrix result fields from selected option"""
        for record in self:
            if record.selected_option_id:
                record.matrix_cell_height = record.selected_option_id.matrix_cell_height
                record.matrix_cell_width = record.selected_option_id.matrix_cell_width
                record.matrix_cell_reference = record.selected_option_id.matrix_cell_reference
                record.matrix_arrow_path = record.selected_option_id.matrix_arrow_path
                record.selected_cut_plan_id = record.selected_option_id.cut_plan_id
                record.selected_efficiency_percentage = record.selected_option_id.efficiency_percentage
                record.selected_byproduct_count = record.selected_option_id.byproduct_count

                # Get the assigned matrix for this mesh series
                matrix = self.env['mesh.cut.matrix'].search([
                    ('mesh_series', '=', record.mesh_series),
                    ('active', '=', True)
                ], limit=1)
                record.assigned_matrix_id = matrix.id if matrix else False

                # Create a clean display for the selected option
                if record.matrix_cell_reference:
                    record.selected_option_display = f"Cell {record.matrix_cell_reference}"
                else:
                    record.selected_option_display = record.selected_option_id.name_get()[0][1] if record.selected_option_id else ""

                # Get the cut diagram display from the selected cut plan
                if record.selected_cut_plan_id and record.selected_cut_plan_id.cut_diagram_display:
                    record.cut_diagram_display = record.selected_cut_plan_id.cut_diagram_display
                else:
                    record.cut_diagram_display = '''
                    <div class="text-center text-muted p-5">
                        <i class="fas fa-image fa-3x mb-3"></i>
                        <p>No cutting diagram available</p>
                        <p class="small">The selected cut plan does not have a cutting diagram</p>
                    </div>
                    '''
            else:
                record.matrix_cell_height = 0
                record.matrix_cell_width = 0
                record.matrix_cell_reference = ""
                record.matrix_arrow_path = ""
                record.selected_cut_plan_id = False
                record.selected_efficiency_percentage = 0
                record.selected_byproduct_count = 0
                record.assigned_matrix_id = False
                record.selected_option_display = ""
                record.cut_diagram_display = '''
                <div class="text-center text-muted p-5">
                    <i class="fas fa-image fa-3x mb-3"></i>
                    <p>No cut plan selected</p>
                    <p class="small">Select a mesh option to view the cutting diagram</p>
                </div>
                '''

    # Available options analysis
    available_options_ids = fields.One2many('mesh.cut.option.analysis', 'operation_id', 'Available Options')
    options_analysis_performed = fields.Boolean('Options Analysis Performed', default=False)
    selected_option_id = fields.Many2one('mesh.cut.option.analysis', 'Selected Option')

    # Matrix result fields (computed from selected option)
    matrix_cell_height = fields.Float('Matrix Cell Height (mm)', compute='_compute_matrix_result_fields')
    matrix_cell_width = fields.Float('Matrix Cell Width (mm)', compute='_compute_matrix_result_fields')
    matrix_cell_reference = fields.Char('Matrix Cell Reference', compute='_compute_matrix_result_fields')
    matrix_arrow_path = fields.Char('Matrix Arrow Path', compute='_compute_matrix_result_fields')
    selected_cut_plan_id = fields.Many2one('mesh.cut.plan', 'Selected Cut Plan', compute='_compute_matrix_result_fields')
    selected_efficiency_percentage = fields.Float('Selected Efficiency %', compute='_compute_matrix_result_fields')
    selected_byproduct_count = fields.Integer('Selected Byproduct Count', compute='_compute_matrix_result_fields')
    assigned_matrix_id = fields.Many2one('mesh.cut.matrix', 'Assigned Matrix', compute='_compute_matrix_result_fields')
    selected_option_display = fields.Char('Selected Option Display', compute='_compute_matrix_result_fields')
    cut_diagram_display = fields.Html('Cut Diagram Display', compute='_compute_matrix_result_fields', sanitize=False)
    
    @api.depends('name', 'required_width', 'required_height', 'mesh_series')
    def _compute_display_name(self):
        for operation in self:
            operation.display_name = f"{operation.name} - {operation.required_width}x{operation.required_height}mm ({operation.mesh_series})"

    @api.depends('sale_order_line_id')
    def _compute_sale_order_id(self):
        for operation in self:
            if operation.sale_order_line_id:
                operation.sale_order_id = operation.sale_order_line_id.order_id
            else:
                operation.sale_order_id = False

    @api.depends('sale_order_line_id', 'sale_order_id')
    def _compute_customer_id(self):
        for operation in self:
            if operation.sale_order_id:
                operation.customer_id = operation.sale_order_id.partner_id
            elif operation.sale_order_line_id and operation.sale_order_line_id.order_id:
                operation.customer_id = operation.sale_order_line_id.order_id.partner_id
            else:
                operation.customer_id = False

    @api.model
    def find_best_mesh(self, required_width, required_height, mesh_series='saltwater'):
        """Find the best available mesh for cutting
        
        Args:
            required_width (float): Required width in mm
            required_height (float): Required height in mm
            mesh_series (str): Mesh series type
            
        Returns:
            dict: Best mesh option details or None
        """
        # Get the cutting matrix for this series
        matrix = self.env['mesh.cut.matrix'].search([
            ('mesh_series', '=', mesh_series),
            ('active', '=', True)
        ], limit=1)
        
        if not matrix:
            _logger.warning(f"No cutting matrix found for mesh series: {mesh_series}")
            return None
        
        return matrix.get_best_fit(required_width, required_height)
    
    def action_find_mesh(self):
        """Find and assign the best mesh for this operation"""
        self.ensure_one()

        if self.state not in ['draft', 'auto_selected', 'confirmed']:
            raise UserError(_("Can only find mesh for draft, auto-selected, or confirmed operations."))

        # Clear previous analysis
        self.available_options_ids.unlink()

        # Get the cutting matrix for this series
        matrix = self.env['mesh.cut.matrix'].search([
            ('mesh_series', '=', self.mesh_series),
            ('active', '=', True)
        ], limit=1)

        if not matrix:
            raise UserError(_(
                "No cutting matrix found for mesh series: %s"
            ) % self.mesh_series)

        # Get all available options for analysis
        all_options = matrix.get_all_suitable_options(self.required_width, self.required_height)

        if not all_options:
            raise UserError(_(
                "No suitable mesh found for dimensions %sx%smm in %s series. "
                "Please check inventory or consider purchasing new master sheets."
            ) % (self.required_width, self.required_height, self.mesh_series))

        # Store all options for analysis
        best_option = max(all_options, key=lambda x: x.get('efficiency', 0))
        selected_option_record = self._store_options_analysis(all_options, best_option)

        # Update operation with found mesh
        self._assign_mesh_source(best_option)

        # Set the selected option
        self.selected_option_id = selected_option_record

        # Mark analysis as performed
        self.options_analysis_performed = True

        # Return a reload action to refresh the view and show the options
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    def hunt_individual_panel(self, width=None, height=None, series=None, priority_order=None):
        """Hunt for mesh for individual panel with priority order"""
        self.ensure_one()

        # Use operation dimensions if not provided
        width = width or self.required_width
        height = height or self.required_height
        series = series or self.mesh_series

        if not priority_order:
            priority_order = 'unplanned_planned_master'  # Default priority

        hunting_methods = self._get_hunting_methods(priority_order)
        hunting_log = []

        _logger.info(f"[HUNT] Starting individual hunt for panel {self.panel_number}: {width}x{height}mm (series: {series})")
        _logger.info(f"[HUNT] Priority order: {priority_order}")
        _logger.info(f"[HUNT] Operation mesh_series field: {self.mesh_series}")

        # Clear previous analysis
        self.available_options_ids.unlink()

        # Collect all options for analysis
        all_options = []
        selected_result = None

        for method_name, method_func in hunting_methods:
            hunting_log.append(f"Trying {method_name}...")
            _logger.info(f"[HUNT] Trying {method_name} for panel {self.panel_number}")

            result = method_func(width, height, series)
            if result and result.get('product'):
                # Add to options list
                all_options.append(result)

                # If this is the first successful result, use it as the selected option
                if not selected_result:
                    selected_result = result
                    hunting_log.append(f"✓ Found: {result.get('details', 'No details')}")
                    _logger.info(f"[HUNT] ✓ Found mesh for panel {self.panel_number}: {result.get('details')}")
                else:
                    hunting_log.append(f"✓ Alternative: {result.get('details', 'No details')}")
                    _logger.info(f"[HUNT] ✓ Alternative mesh for panel {self.panel_number}: {result.get('details')}")
            else:
                reason = result.get('reason', 'No suitable mesh found') if result else 'Method failed'
                hunting_log.append(f"  ✗ {method_name}: {reason}")
                _logger.info(f"[HUNT] ✗ {method_name} failed for panel {self.panel_number}: {reason}")

        # Process results
        if all_options:
            _logger.info(f"[HUNT] Processing {len(all_options)} options for panel {self.panel_number}")

            # Convert hunting results to analysis format
            analysis_options = []
            for i, option in enumerate(all_options):
                _logger.info(f"[HUNT] Option {i+1}: {option.get('source_type')} - {option.get('details')}")

                analysis_option = {
                    'type': option.get('source_type', 'unplanned'),
                    'product': option['product'],
                    'efficiency': option.get('efficiency', 0),
                    'waste_width': option.get('waste_width', 0),
                    'waste_height': option.get('waste_height', 0),
                }

                # Add type-specific fields
                if option.get('lot'):
                    analysis_option['lot'] = option['lot']
                if option.get('quant'):
                    analysis_option['quant'] = option['quant']
                if option.get('cut_plan'):
                    analysis_option['cut_plan'] = option['cut_plan']
                if option.get('byproducts'):
                    analysis_option['byproducts'] = option['byproducts']

                analysis_options.append(analysis_option)

            # Store all options for analysis
            _logger.info(f"[HUNT] Storing {len(analysis_options)} analysis options")
            best_option = max(all_options, key=lambda x: x.get('efficiency', 0))
            best_analysis_option = max(analysis_options, key=lambda x: x.get('efficiency', 0))

            try:
                selected_option_record = self._store_options_analysis(analysis_options, best_analysis_option)
                _logger.info(f"[HUNT] Created {len(analysis_options)} option analysis records")
            except Exception as e:
                _logger.error(f"[HUNT] Error storing options analysis: {str(e)}")
                selected_option_record = None

            # Update operation with found mesh
            self.write({
                'source_product_id': best_option['product'].id,
                'source_type': best_option.get('source_type', 'unplanned'),
                'actual_width': best_option.get('actual_width', width),
                'actual_height': best_option.get('actual_height', height),
                'efficiency': best_option.get('efficiency', 0.0),
                'selection_reason': '\n'.join(hunting_log),
                'selected_option_id': selected_option_record.id if selected_option_record else False,
                'options_analysis_performed': True
            })

            _logger.info(f"[HUNT] ✓ Completed hunt for panel {self.panel_number} with {len(all_options)} options")
            return best_option

        # No mesh found
        self.write({
            'source_type': 'unplanned',  # Keep as unplanned for now
            'selection_reason': '\n'.join(hunting_log + ["✗ No suitable mesh found in any source"])
        })
        _logger.warning(f"[HUNT] ✗ No mesh found for panel {self.panel_number}")
        return None

    def _get_hunting_methods(self, priority_order):
        """Get hunting methods in priority order"""
        methods = {
            'unplanned_offcuts': ('Unplanned Offcuts', self._hunt_unplanned_offcuts),
            'planned_offcuts': ('Planned Offcuts', self._hunt_planned_offcuts),
            'master_sheets': ('Master Sheets', self._hunt_master_sheets)
        }

        if priority_order == 'unplanned_planned_master':
            return [methods['unplanned_offcuts'], methods['planned_offcuts'], methods['master_sheets']]
        elif priority_order == 'planned_unplanned_master':
            return [methods['planned_offcuts'], methods['unplanned_offcuts'], methods['master_sheets']]
        elif priority_order == 'master_only':
            return [methods['master_sheets']]
        elif priority_order == 'offcuts_only':
            return [methods['unplanned_offcuts'], methods['planned_offcuts']]
        else:
            return [methods['unplanned_offcuts'], methods['planned_offcuts'], methods['master_sheets']]

    def _hunt_unplanned_offcuts(self, width, height, series):
        """Hunt in unplanned offcuts"""
        _logger.info(f"[HUNT] Searching unplanned offcuts for {width}x{height}mm {series}")

        # Search for unplanned mesh products that can fit the required dimensions
        domain = [
            ('mesh_series', '=', series),
            ('mesh_width', '>=', width),
            ('mesh_height', '>=', height),
            ('mesh_type', '=', 'unplanned'),
            ('is_mesh_product', '=', True),
        ]

        # Search for available products with stock
        suitable_products = self.env['product.product'].search(domain)

        if not suitable_products:
            return {'reason': 'No unplanned offcuts found with suitable dimensions'}

        # Find the most efficient option (smallest waste)
        best_product = None
        best_efficiency = 0

        for product in suitable_products:
            # Check stock availability
            available_qty = product.qty_available
            if available_qty <= 0:
                continue

            # Calculate efficiency
            required_area = width * height
            available_area = product.mesh_width * product.mesh_height
            efficiency = (required_area / available_area) * 100 if available_area > 0 else 0

            if efficiency > best_efficiency:
                best_efficiency = efficiency
                best_product = product

        if best_product:
            # Calculate waste
            waste_width = best_product.mesh_width - width
            waste_height = best_product.mesh_height - height

            return {
                'product': best_product,
                'source_type': 'unplanned',
                'actual_width': best_product.mesh_width,
                'actual_height': best_product.mesh_height,
                'efficiency': best_efficiency,
                'waste_width': waste_width,
                'waste_height': waste_height,
                'details': f"Unplanned offcut {best_product.mesh_width}x{best_product.mesh_height}mm (Efficiency: {best_efficiency:.1f}%)"
            }

        return {'reason': 'No unplanned offcuts available in stock'}

    def _hunt_planned_offcuts(self, width, height, series):
        """Hunt in planned offcuts"""
        _logger.info(f"[HUNT] Searching planned offcuts for {width}x{height}mm {series}")

        # Search for planned mesh products that can fit the required dimensions
        domain = [
            ('mesh_series', '=', series),
            ('mesh_width', '>=', width),
            ('mesh_height', '>=', height),
            ('mesh_type', '=', 'planned'),
            ('is_mesh_product', '=', True),
        ]

        # Search for available products with stock
        suitable_products = self.env['product.product'].search(domain)
        _logger.info(f"[HUNT] Found {len(suitable_products)} planned products matching domain")

        for product in suitable_products:
            _logger.info(f"[HUNT] Planned product: {product.name} - {product.mesh_width}x{product.mesh_height}mm (Stock: {product.qty_available})")

        if not suitable_products:
            return {'reason': 'No planned offcuts found with suitable dimensions'}

        # Find the most efficient option (smallest waste)
        best_product = None
        best_efficiency = 0

        for product in suitable_products:
            # Check stock availability
            available_qty = product.qty_available
            if available_qty <= 0:
                continue

            # Calculate efficiency
            required_area = width * height
            available_area = product.mesh_width * product.mesh_height
            efficiency = (required_area / available_area) * 100 if available_area > 0 else 0

            if efficiency > best_efficiency:
                best_efficiency = efficiency
                best_product = product

        if best_product:
            return {
                'product': best_product,
                'source_type': 'planned',
                'actual_width': best_product.mesh_width,
                'actual_height': best_product.mesh_height,
                'efficiency': best_efficiency,
                'details': f"Planned offcut {best_product.mesh_width}x{best_product.mesh_height}mm (Efficiency: {best_efficiency:.1f}%)"
            }

        return {'reason': 'No planned offcuts available in stock'}

    def _hunt_master_sheets(self, width, height, series):
        """Hunt in master sheets using cutting matrix"""
        _logger.info(f"[HUNT] Searching master sheets for {width}x{height}mm {series}")

        # Get the cutting matrix for this series
        matrix = self.env['mesh.cut.matrix'].search([
            ('mesh_series', '=', series),
            ('active', '=', True)
        ], limit=1)

        if not matrix:
            return {'reason': f'No cutting matrix found for {series} series'}

        # Skip matrix lookup for now and go straight to fallback search
        # The matrix.get_best_fit() method seems to have an invalid order clause
        master_product = None
        _logger.info(f"[HUNT] Skipping matrix lookup, going straight to fallback search")

        # If no specific cut plan or master sheet found, try to find any master sheet that can accommodate
        if not master_product:
            _logger.info(f"[HUNT] No specific cut plan found, searching for any suitable master sheet for {width}x{height}mm")

            # First, let's see what master products exist for this series
            all_master_products = self.env['product.product'].search([
                ('mesh_type', '=', 'master'),
                ('is_mesh_product', '=', True)
            ])
            _logger.info(f"[HUNT] All master products in system:")
            for p in all_master_products:
                _logger.info(f"[HUNT]   - {p.name} | Series: '{p.mesh_series}' | Size: {p.mesh_width}x{p.mesh_height}mm | Stock: {p.qty_available}")

            _logger.info(f"[HUNT] Searching for series: '{series}' (type: {type(series)})")

            # Try case-insensitive search for series
            master_products = self.env['product.product'].search([
                ('mesh_series', 'ilike', series),
                ('mesh_type', '=', 'master'),
                ('is_mesh_product', '=', True),
                ('mesh_width', '>=', width),
                ('mesh_height', '>=', height)
            ])

            _logger.info(f"[HUNT] Found {len(master_products)} master products for series '{series}'")
            for product in master_products:
                _logger.info(f"[HUNT] Master product: {product.name} - {product.mesh_width}x{product.mesh_height}mm (Stock: {product.qty_available})")

            # Filter by stock availability and sort by area (smallest first for best efficiency)
            available_products = [p for p in master_products if p.qty_available > 0]
            if available_products:
                master_product = min(available_products, key=lambda p: p.mesh_width * p.mesh_height)
                _logger.info(f"[HUNT] Selected best master sheet: {master_product.mesh_width}x{master_product.mesh_height}mm")
            else:
                master_product = None
                _logger.info(f"[HUNT] No master products with stock found for series '{series}'")

        if master_product:
            # Check stock availability
            available_qty = master_product.qty_available
            if available_qty <= 0:
                return {'reason': f'Master sheet {master_product.mesh_width}x{master_product.mesh_height}mm out of stock'}

            # Calculate efficiency and waste
            required_area = width * height
            available_area = master_product.mesh_width * master_product.mesh_height
            efficiency = (required_area / available_area) * 100 if available_area > 0 else 0
            waste_width = master_product.mesh_width - width
            waste_height = master_product.mesh_height - height

            result = {
                'product': master_product,
                'source_type': 'master',
                'actual_width': master_product.mesh_width,
                'actual_height': master_product.mesh_height,
                'efficiency': efficiency,
                'waste_width': waste_width,
                'waste_height': waste_height,
                'details': f"Master sheet {master_product.mesh_width}x{master_product.mesh_height}mm (Efficiency: {efficiency:.1f}%)"
            }

            return result

        return {'reason': 'No master sheet products found'}

    def action_hunt_individual_panel(self):
        """Action to hunt for this individual panel"""
        self.ensure_one()

        if self.state not in ['draft', 'confirmed']:
            raise UserError(_("Can only hunt for draft or confirmed operations."))

        result = self.hunt_individual_panel()

        # Always return a reload action to refresh the view and show updated Available Options
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }
    
    def _assign_mesh_source(self, mesh_result):
        """Assign mesh source from search result"""
        values = {
            'source_type': mesh_result['type'],
            'source_product_id': mesh_result['product'].id,
            'source_quant_id': mesh_result['quant'].id,
            'efficiency': mesh_result.get('efficiency', 0),
            'waste_width': mesh_result.get('waste_width', 0),
            'waste_height': mesh_result.get('waste_height', 0),
        }
        
        # Set dimensions based on source type
        if mesh_result['type'] == 'unplanned':
            values.update({
                'source_lot_id': mesh_result['lot'].id,
                'actual_width': mesh_result['lot'].mesh_width,
                'actual_height': mesh_result['lot'].mesh_height,
            })
        else:
            values.update({
                'actual_width': mesh_result['product'].mesh_width,
                'actual_height': mesh_result['product'].mesh_height,
            })
        
        # Handle master sheet byproducts
        if mesh_result['type'] == 'master' and mesh_result.get('byproducts'):
            # Clear existing byproducts
            self.byproduct_ids.unlink()
            
            # Create new byproducts
            for byproduct in mesh_result['byproducts']:
                self.env['mesh.cut.byproduct.line'].create({
                    'cut_operation_id': self.id,
                    'product_id': byproduct.product_id.id if byproduct.product_id else False,
                    'quantity': byproduct.quantity,
                    'width': byproduct.width,
                    'height': byproduct.height,
                })
        
        self.write(values)

    def _store_options_analysis(self, options, best_option):
        """Store all options for analysis"""
        sequence = 10
        selected_option_record = None

        for option in options:
            # Determine if this is the selected option (highest efficiency)
            is_selected = option == best_option

            # Get dimensions based on option type
            if option['type'] == 'unplanned' and option.get('lot'):
                available_width = option['lot'].mesh_width
                available_height = option['lot'].mesh_height
            elif option.get('product'):
                available_width = option['product'].mesh_width
                available_height = option['product'].mesh_height
            else:
                available_width = 0
                available_height = 0

            # Create analysis record
            analysis_record = self.env['mesh.cut.option.analysis'].create({
                'operation_id': self.id,
                'sequence': sequence,
                'option_type': option['type'],
                'product_id': option['product'].id,
                'lot_id': option.get('lot', False) and option['lot'].id,
                'quant_id': option.get('quant', False) and option['quant'].id,
                'available_width': available_width,
                'available_height': available_height,
                'efficiency': option.get('efficiency', 0),
                'waste_width': option.get('waste_width', 0),
                'waste_height': option.get('waste_height', 0),
                'is_selected': is_selected,
                'available_qty': option.get('quant', False) and option['quant'].quantity or 0,
                'cut_plan_id': option.get('cut_plan', False) and option['cut_plan'].id,
                'byproduct_count': len(option.get('byproducts', [])),
                'matrix_cell_height': option.get('matrix_cell_height', 0),
                'matrix_cell_width': option.get('matrix_cell_width', 0),
                'matrix_arrow_path': option.get('matrix_arrow_path', ''),
            })

            if is_selected:
                selected_option_record = analysis_record

            sequence += 10

        return selected_option_record

    def action_select_option(self, option_id):
        """Manually select a different option"""
        self.ensure_one()

        if self.state not in ['draft', 'auto_selected']:
            raise UserError(_("Can only change mesh selection for draft or auto-selected operations."))

        # Find the option record
        option_record = self.available_options_ids.filtered(lambda x: x.id == option_id)
        if not option_record:
            raise UserError(_("Option not found."))

        # Clear previous selection
        self.available_options_ids.write({'is_selected': False})

        # Mark new selection
        option_record.is_selected = True
        self.selected_option_id = option_record

        # Reconstruct the option dict for _assign_mesh_source
        option_dict = {
            'type': option_record.option_type,
            'product': option_record.product_id,
            'lot': option_record.lot_id,
            'quant': option_record.quant_id,
            'efficiency': option_record.efficiency,
            'waste_width': option_record.waste_width,
            'waste_height': option_record.waste_height,
            'matrix_cell_height': option_record.matrix_cell_height,
            'matrix_cell_width': option_record.matrix_cell_width,
            'matrix_arrow_path': option_record.matrix_arrow_path,
        }

        if option_record.cut_plan_id:
            option_dict['cut_plan'] = option_record.cut_plan_id
            option_dict['byproducts'] = option_record.cut_plan_id.byproduct_ids

        # Update operation with selected mesh
        self._assign_mesh_source(option_dict)

        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    def action_view_cut_plan(self):
        """Open the selected cut plan for detailed view"""
        self.ensure_one()

        if not self.selected_cut_plan_id:
            raise UserError(_("No cut plan selected for this operation."))

        return {
            'name': _('Cut Plan Details'),
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.plan',
            'view_mode': 'form',
            'res_id': self.selected_cut_plan_id.id,
            'target': 'new',
        }

    def action_view_cutting_diagram(self):
        """Open the cutting layout diagram for the selected cut plan"""
        self.ensure_one()

        if not self.selected_cut_plan_id:
            raise UserError(_("No cut plan selected for this operation."))

        # Create a custom view context to show the diagram tab
        return {
            'name': _('Cutting Layout Diagram'),
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.plan',
            'view_mode': 'form',
            'res_id': self.selected_cut_plan_id.id,
            'target': 'new',
            'context': {'default_active_tab': 'diagram'},
        }

    def action_confirm(self):
        """Confirm the cutting operation"""
        self.ensure_one()

        if self.state not in ['draft', 'auto_selected']:
            raise UserError(_("Only draft or auto-selected operations can be confirmed."))

        if not self.source_product_id:
            raise UserError(_("Please find a mesh source before confirming."))

        # Check stock availability
        if self.source_quant_id.quantity < self.required_qty:
            raise UserError(_(
                "Insufficient stock. Required: %s, Available: %s"
            ) % (self.required_qty, self.source_quant_id.quantity))

        self.state = 'confirmed'

        # Add mesh to BOM if linked to a configuration
        self._add_mesh_to_bom()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Operation Confirmed'),
                'message': _('Cutting operation has been confirmed and mesh added to BOM.'),
                'type': 'success',
            }
        }
    
    def action_start_cutting(self):
        """Start the cutting process"""
        self.ensure_one()
        
        if self.state != 'confirmed':
            raise UserError(_("Only confirmed operations can be started."))
        
        self.state = 'cutting'
    
    def action_complete_cutting(self):
        """Complete the cutting operation"""
        self.ensure_one()
        
        if self.state != 'cutting':
            raise UserError(_("Only operations in cutting state can be completed."))
        
        # Create stock moves
        self._create_stock_moves()
        
        self.state = 'done'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Cutting Complete'),
                'message': _('Cutting operation completed successfully. Stock moves have been created.'),
                'type': 'success',
            }
        }
    
    def action_cancel(self):
        """Cancel the operation"""
        self.ensure_one()
        
        if self.state == 'done':
            raise UserError(_("Cannot cancel completed operations."))
        
        self.state = 'cancelled'
    
    def _create_stock_moves(self):
        """Create stock moves for consumption and byproducts"""
        # Consume source material
        self._create_consumption_move()
        
        # Create byproducts if applicable
        if self.source_type == 'master' and self.byproduct_ids:
            self._create_byproduct_moves()
    
    def _create_consumption_move(self):
        """Create stock move for consuming source mesh"""
        # Implementation would create stock.move for consumption
        # This is a placeholder for the actual stock move creation
        pass

    def _add_mesh_to_bom(self):
        """Add the selected mesh product to the configuration's BOM"""
        self.ensure_one()

        if not self.source_product_id:
            _logger.warning(f"No source product selected for mesh operation {self.id}")
            return

        # Find the configuration BOM
        bom = None
        if self.config_id and self.config_id.bom_id:
            bom = self.config_id.bom_id
        elif self.sale_order_line_id and self.sale_order_line_id.config_id and self.sale_order_line_id.config_id.bom_id:
            bom = self.sale_order_line_id.config_id.bom_id

        if not bom:
            _logger.warning(f"No BOM found for mesh operation {self.id}")
            return

        # Check if mesh component already exists in BOM
        existing_line = bom.bom_line_ids.filtered(
            lambda l: l.product_id.id == self.source_product_id.id
        )

        if existing_line:
            # Update existing quantity
            existing_line[0].product_qty += self.required_qty
            _logger.info(f"Updated existing BOM line for {self.source_product_id.name}")
        else:
            # Create new BOM line
            self.env['mrp.bom.line'].create({
                'bom_id': bom.id,
                'product_id': self.source_product_id.id,
                'product_qty': self.required_qty,
            })
            _logger.info(f"Added mesh product {self.source_product_id.name} to BOM {bom.code}")

        return True
    
    def _create_byproduct_moves(self):
        """Create stock moves for byproducts"""
        # Implementation would create stock.move for each byproduct
        # This is a placeholder for the actual stock move creation
        pass


class MeshCutByproductLine(models.Model):
    _name = 'mesh.cut.byproduct.line'
    _description = 'Mesh Cut Byproduct Line'
    _order = 'width desc, height desc'
    
    name = fields.Char('Byproduct Name', compute='_compute_name', store=True)
    cut_operation_id = fields.Many2one('mesh.cut.operation', 'Cut Operation', 
                                     required=True, ondelete='cascade')
    
    # Byproduct details
    product_id = fields.Many2one('product.product', 'Byproduct Product')
    quantity = fields.Float('Quantity', default=1.0, required=True)
    width = fields.Float('Width (mm)', required=True)
    height = fields.Float('Height (mm)', required=True)
    
    # Computed fields
    area = fields.Float('Area (mm²)', compute='_compute_area', store=True)
    total_area = fields.Float('Total Area (mm²)', compute='_compute_total_area', store=True)
    
    # Stock tracking
    stock_move_id = fields.Many2one('stock.move', 'Stock Move', readonly=True)
    lot_id = fields.Many2one('stock.lot', 'Created Lot', readonly=True)
    
    @api.depends('width', 'height')
    def _compute_name(self):
        for line in self:
            line.name = f"{line.width}x{line.height}mm"
    
    @api.depends('width', 'height')
    def _compute_area(self):
        for line in self:
            line.area = line.width * line.height
    
    @api.depends('area', 'quantity')
    def _compute_total_area(self):
        for line in self:
            line.total_area = line.area * line.quantity

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to auto-create byproduct products"""
        for vals in vals_list:
            if not vals.get('product_id') and vals.get('width') and vals.get('height'):
                # Auto-create byproduct product
                vals['product_id'] = self._create_byproduct_product(vals).id

        return super().create(vals_list)

    def _create_byproduct_product(self, vals):
        """Create a byproduct product for this offcut"""
        cut_operation = self.env['mesh.cut.operation'].browse(vals['cut_operation_id'])
        width = vals['width']
        height = vals['height']

        # Determine mesh series from the cut operation
        mesh_series = cut_operation.mesh_series

        # Get the cut matrix for this series
        cut_matrix = self.env['mesh.cut.matrix'].search([
            ('mesh_series', '=', mesh_series)
        ], limit=1)

        # Determine if this is planned or unplanned based on cut plan
        mesh_type = 'planned' if cut_operation.cut_plan_id else 'unplanned'

        product_name = f"{mesh_series.title()} Mesh Off-cut {width}x{height}mm"

        # Create product with automatic code generation
        product_vals = {
            'name': product_name,
            'type': 'product',
            'is_mesh_product': True,
            'mesh_type': mesh_type,
            'mesh_width': width,
            'mesh_height': height,
            'mesh_series': mesh_series,
            'cut_matrix_id': cut_matrix.id if cut_matrix else False,
            'categ_id': self.env.ref('canbrax_configmatrix.product_category_mesh_offcuts').id,
        }



        product = self.env['product.product'].create(product_vals)

        return product


class MeshCutOptionAnalysis(models.Model):
    _name = 'mesh.cut.option.analysis'
    _description = 'Mesh Cut Option Analysis'
    _order = 'efficiency desc, sequence'

    operation_id = fields.Many2one('mesh.cut.operation', 'Cut Operation', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10)

    # Option details
    option_type = fields.Selection([
        ('unplanned', 'Unplanned Off-cut'),
        ('planned', 'Planned Off-cut'),
        ('master', 'Master Sheet'),
    ], string='Option Type', required=True)

    product_id = fields.Many2one('product.product', 'Product', required=True)
    lot_id = fields.Many2one('stock.lot', 'Lot/Serial')
    quant_id = fields.Many2one('stock.quant', 'Stock Quant')

    # Dimensions
    available_width = fields.Float('Available Width (mm)', required=True)
    available_height = fields.Float('Available Height (mm)', required=True)

    # Calculations
    efficiency = fields.Float('Efficiency', help="Material efficiency as decimal (0.64 = 64%)")
    efficiency_percentage = fields.Float('Efficiency %', compute='_compute_efficiency_percentage', store=True)
    waste_width = fields.Float('Waste Width (mm)')
    waste_height = fields.Float('Waste Height (mm)')

    # Status
    is_selected = fields.Boolean('Selected Option', default=False)
    is_available = fields.Boolean('Available in Stock', default=True)
    available_qty = fields.Float('Available Quantity')
    selection_status = fields.Char('Status', compute='_compute_selection_status')

    # Additional info for master sheets
    cut_plan_id = fields.Many2one('mesh.cut.plan', 'Cut Plan')
    byproduct_count = fields.Integer('Byproducts Count')

    # Matrix lookup results
    matrix_cell_height = fields.Float('Matrix Cell Height (mm)')
    matrix_cell_width = fields.Float('Matrix Cell Width (mm)')
    matrix_cell_reference = fields.Char('Matrix Cell Reference', compute='_compute_matrix_cell_reference')
    matrix_arrow_path = fields.Char('Matrix Arrow Path')

    @api.depends('efficiency')
    def _compute_efficiency_percentage(self):
        for record in self:
            record.efficiency_percentage = record.efficiency * 100

    @api.depends('is_selected')
    def _compute_selection_status(self):
        for record in self:
            record.selection_status = "✓ SELECTED" if record.is_selected else ""

    @api.depends('matrix_cell_height', 'matrix_cell_width')
    def _compute_matrix_cell_reference(self):
        for record in self:
            if record.matrix_cell_height and record.matrix_cell_width and record.operation_id:
                # Get the matrix to access height/width values
                matrix = self.env['mesh.cut.matrix'].search([
                    ('mesh_series', '=', record.operation_id.mesh_series),
                    ('active', '=', True)
                ], limit=1)

                if matrix and matrix.height_values and matrix.width_values:
                    try:
                        height_values = [h.strip() for h in matrix.height_values.split(',') if h.strip()]
                        width_values = [w.strip() for w in matrix.width_values.split(',') if w.strip()]

                        # Convert dimensions to Excel-style coordinates
                        height_str = str(int(record.matrix_cell_height))
                        width_str = str(int(record.matrix_cell_width))

                        # Find indices in the value lists
                        height_index = height_values.index(height_str) if height_str in height_values else -1
                        width_index = width_values.index(width_str) if width_str in width_values else -1

                        if height_index >= 0 and width_index >= 0:
                            # Convert to Excel-style coordinates (A1, B2, etc.)
                            column = chr(ord('A') + width_index) if width_index < 26 else f"A{chr(ord('A') + width_index - 26)}"
                            row = height_index + 1
                            record.matrix_cell_reference = f"{column}{row}"
                        else:
                            record.matrix_cell_reference = f"{width_str}×{height_str}"
                    except (ValueError, IndexError):
                        record.matrix_cell_reference = f"{int(record.matrix_cell_width)}×{int(record.matrix_cell_height)}"
                else:
                    record.matrix_cell_reference = f"{int(record.matrix_cell_width)}×{int(record.matrix_cell_height)}"
            else:
                record.matrix_cell_reference = ""

    def name_get(self):
        result = []
        for record in self:
            name = f"{record.product_id.name} ({record.efficiency_percentage:.1f}%)"
            if record.lot_id:
                name += f" - {record.lot_id.name}"
            result.append((record.id, name))
        return result

    def action_select_this_option(self):
        """Select this option for the cut operation"""
        self.ensure_one()
        return self.operation_id.action_select_option(self.id)
