# -*- coding: utf-8 -*-

from odoo import models, fields, api

class StockLot(models.Model):
    _inherit = 'stock.lot'
    
    # Dimensions for unplanned off-cuts
    mesh_width = fields.Float('Mesh Width (mm)', 
                            help="Width of the mesh piece in millimeters")
    mesh_height = fields.Float('Mesh Height (mm)', 
                             help="Height of the mesh piece in millimeters")
    
    # Mesh type flags for easy filtering
    is_master_sheet = fields.<PERSON><PERSON><PERSON>('Is Master Sheet', 
                                   help="This lot represents a master sheet")
    is_planned_offcut = fields.<PERSON><PERSON><PERSON>('Is Planned Off-cut', 
                                     help="This lot represents a planned off-cut")
    is_unplanned_offcut = fields.<PERSON><PERSON><PERSON>('Is Unplanned Off-cut', 
                                       help="This lot represents an unplanned off-cut")
    
    # Reference to original master if this is an off-cut
    source_master_lot_id = fields.Many2one('stock.lot', 'Source Master Lot',
                                         help="The master sheet lot this off-cut came from")
    
    # Computed field for display
    display_name_with_size = fields.Char('Display Name with Size', 
                                       compute='_compute_display_name_with_size',
                                       store=True)
    
    @api.depends('name', 'mesh_width', 'mesh_height')
    def _compute_display_name_with_size(self):
        """Compute display name including dimensions"""
        for lot in self:
            if lot.mesh_width and lot.mesh_height:
                lot.display_name_with_size = f"{lot.name} ({lot.mesh_width}x{lot.mesh_height}mm)"
            else:
                lot.display_name_with_size = lot.name
    
    @api.model
    def create_unplanned_offcut(self, product_id, width, height, source_lot_id=None, quantity=1.0):
        """Create a new unplanned off-cut lot
        
        Args:
            product_id (int): Product ID for the off-cut
            width (float): Width in mm
            height (float): Height in mm
            source_lot_id (int, optional): Source master lot ID
            quantity (float): Quantity of the off-cut
            
        Returns:
            stock.lot: Created lot record
        """
        # Generate lot name
        product = self.env['product.product'].browse(product_id)
        lot_name = f"{product.default_code or product.name}_{width}x{height}_{fields.Datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create the lot
        lot = self.create({
            'name': lot_name,
            'product_id': product_id,
            'mesh_width': width,
            'mesh_height': height,
            'is_unplanned_offcut': True,
            'source_master_lot_id': source_lot_id,
        })
        
        return lot
    
    def action_view_source_master(self):
        """View the source master sheet"""
        self.ensure_one()
        if not self.source_master_lot_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Source Master',
                    'message': 'This lot does not have a source master sheet.',
                    'type': 'warning',
                }
            }
        
        return {
            'name': 'Source Master Sheet',
            'type': 'ir.actions.act_window',
            'res_model': 'stock.lot',
            'view_mode': 'form',
            'res_id': self.source_master_lot_id.id,
        }
    
    def action_view_related_offcuts(self):
        """View all off-cuts created from this master sheet"""
        self.ensure_one()
        
        offcuts = self.search([('source_master_lot_id', '=', self.id)])
        
        return {
            'name': f'Off-cuts from {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'stock.lot',
            'view_mode': 'list,form',
            'domain': [('id', 'in', offcuts.ids)],
            'context': {'default_source_master_lot_id': self.id},
        }
