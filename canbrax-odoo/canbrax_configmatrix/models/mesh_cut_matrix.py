# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import json
import logging
from markupsafe import Markup
from .mesh_constants import MESH_SERIES_SELECTION, DEFAULT_MESH_SERIES

_logger = logging.getLogger(__name__)

class MeshCutMatrix(models.Model):
    _name = 'mesh.cut.matrix'
    _description = 'Mesh Cutting Matrix Configuration'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'mesh_series, name'

    name = fields.Char('Matrix Name', required=True, tracking=True)
    description = fields.Text('Description')
    mesh_series = fields.Selection(MESH_SERIES_SELECTION, required=True, string='Mesh Series', tracking=True)

    active = fields.Boolean('Active', default=True, tracking=True)

    # Visual Matrix Configuration
    height_values = fields.Text('Height Values',
                               help="Comma-separated list of height values (e.g., 600,700,800,900,1000)",
                               default="600,700,800,900,1000,1100,1200,1300,1400,1500")
    width_values = fields.Text('Width Values',
                              help="Comma-separated list of width values (e.g., 325,375,425,475,525)",
                              default="325,375,425,475,525,575,625,675,725,775,825,875,925,975")

    # Matrix data with arrows and cut templates
    matrix_data = fields.Text('Matrix Data JSON',
                             help="JSON object containing matrix cells with arrows and cut templates")

    # Visual matrix HTML for display
    matrix_html = fields.Html('Matrix Visual Display', compute='_compute_matrix_html')

    # Interactive matrix editor HTML
    interactive_matrix_html = fields.Html('Interactive Matrix Editor', compute='_compute_interactive_matrix_html')

    # Interactive matrix widget field
    interactive_matrix_widget = fields.Char('Interactive Matrix Widget', default='widget_placeholder')

    # Matrix rules stored as JSON (legacy)
    cut_rules = fields.Text('Cut Rules JSON',
                          help="JSON structure containing cutting rules and master sheet configurations")

    # Cut plans for different master sheet sizes
    cut_plan_ids = fields.One2many('mesh.cut.plan', 'matrix_id', 'Cut Plans')

    # Statistics
    plan_count = fields.Integer('Number of Plans', compute='_compute_plan_count')
    
    @api.depends('cut_plan_ids')
    def _compute_plan_count(self):
        for matrix in self:
            matrix.plan_count = len(matrix.cut_plan_ids)

    @api.depends('matrix_data', 'height_values', 'width_values')
    def _compute_matrix_html(self):
        """Generate HTML representation of the cutting matrix"""
        for matrix in self:
            matrix.matrix_html = matrix._generate_matrix_html()

    @api.depends('matrix_data', 'height_values', 'width_values')
    def _compute_interactive_matrix_html(self):
        """Generate interactive Excel-style HTML for the cutting matrix"""
        for matrix in self:
            matrix.interactive_matrix_html = matrix._generate_interactive_matrix_html()

    def _generate_matrix_html(self):
        """Generate HTML table for the cutting matrix with arrows and cut templates"""
        if not self.height_values or not self.width_values:
            return "<p>Please configure height and width values first.</p>"

        try:
            heights = [int(h.strip()) for h in self.height_values.split(',') if h.strip()]
            widths = [int(w.strip()) for w in self.width_values.split(',') if w.strip()]
            matrix_data = json.loads(self.matrix_data or '{}')
        except (ValueError, json.JSONDecodeError):
            return "<p>Invalid height/width values or matrix data format.</p>"

        if not heights or not widths:
            return "<p>No valid height or width values found.</p>"

        # Generate HTML table
        html = ['<div class="mesh-cut-matrix-container">']
        html.append('<table class="mesh-cut-matrix-table table table-bordered">')

        # Header row
        html.append('<thead><tr>')
        html.append('<th class="matrix-corner">H\\W</th>')
        for width in widths:
            html.append(f'<th class="matrix-width-header">{width}</th>')
        html.append('</tr></thead>')

        # Data rows
        html.append('<tbody>')
        for height in heights:
            html.append('<tr>')
            html.append(f'<th class="matrix-height-header">{height}</th>')

            for width in widths:
                cell_key = f"{height}_{width}"
                cell_data = matrix_data.get(cell_key, {})

                # Determine cell content
                cell_content = self._generate_cell_content(cell_data, height, width)
                cell_class = self._get_cell_class(cell_data)

                html.append(f'<td class="matrix-cell {cell_class}" data-height="{height}" data-width="{width}">')
                html.append(cell_content)
                html.append('</td>')

            html.append('</tr>')

        html.append('</tbody>')
        html.append('</table>')
        html.append('</div>')

        # Add CSS styles
        html.append(self._get_matrix_css())

        return ''.join(html)

    def _generate_interactive_matrix_html(self):
        """Generate interactive Excel-style HTML for the cutting matrix editor"""
        if not self.height_values or not self.width_values:
            return "<p>Please configure height and width values first.</p>"

        try:
            heights = [int(h.strip()) for h in self.height_values.split(',') if h.strip()]
            widths = [int(w.strip()) for w in self.width_values.split(',') if w.strip()]
            matrix_data = json.loads(self.matrix_data or '{}')
        except (ValueError, json.JSONDecodeError):
            return "<p>Invalid height/width values or matrix data format.</p>"

        if not heights or not widths:
            return "<p>No valid height or width values found.</p>"

        # Generate Excel-style HTML interface
        html = ['<div class="mesh-cut-matrix-excel-editor">']

        # Add instructions and toolbar
        html.append('''
        <div class="editor-header">
            <h4>Interactive Mesh Cut Matrix Editor</h4>
            <p class="text-muted">Click on any cell to edit. Use the toolbar below to add arrows and configure cuts.</p>

            <div class="editor-toolbar">
                <div class="btn-group me-2" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setArrowMode('right')" id="btnArrowRight">
                        → Right Arrow
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setArrowMode('down')" id="btnArrowDown">
                        ↓ Down Arrow
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setArrowMode('none')" id="btnNoArrow">
                        ✕ No Arrow
                    </button>
                </div>

                <div class="btn-group me-2" role="group">
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="toggleMasterMode()" id="btnMaster">
                        ⬛ Master Sheet
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="toggleOffcutMode()" id="btnOffcut">
                        📦 Offcut
                    </button>
                </div>

                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="clearSelectedCells()">
                        🗑️ Clear
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" onclick="saveMatrix()">
                        💾 Save Matrix
                    </button>
                </div>
            </div>

            <div class="current-mode-indicator">
                <span id="currentMode">Mode: Select cells to edit</span>
            </div>
        </div>
        ''')

        # Generate the matrix table
        html.append('<div class="matrix-container">')
        html.append('<table class="excel-matrix-table">')

        # Header row
        html.append('<thead><tr>')
        html.append('<th class="corner-cell">H\\W</th>')
        for width in widths:
            html.append(f'<th class="width-header">{width}</th>')
        html.append('</tr></thead>')

        # Data rows
        html.append('<tbody>')
        for height in heights:
            html.append('<tr>')
            html.append(f'<th class="height-header">{height}</th>')

            for width in widths:
                cell_key = f"{height}_{width}"
                cell_data = matrix_data.get(cell_key, {})

                # Generate Excel-style cell
                cell_content = self._generate_excel_cell_content(cell_data, height, width)
                cell_class = self._get_excel_cell_class(cell_data)

                html.append(f'''
                <td class="excel-cell {cell_class}"
                    data-height="{height}"
                    data-width="{width}"
                    data-key="{cell_key}"
                    onclick="selectCell(this, {height}, {width})"
                    title="Click to select {height}x{width}mm">
                    {cell_content}
                </td>
                ''')

            html.append('</tr>')

        html.append('</tbody>')
        html.append('</table>')
        html.append('</div>')

        # Add quick edit panel
        html.append('''
        <div class="quick-edit-panel" id="quickEditPanel" style="display: none;">
            <div class="panel-header">
                <h6>Quick Edit: <span id="selectedCellInfo"></span></h6>
                <button type="button" class="btn-close" onclick="closeQuickEdit()">×</button>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-4">
                        <label>Cut Template:</label>
                        <input type="text" class="form-control form-control-sm" id="cutTemplate"
                               placeholder="e.g., 1100x620">
                    </div>
                    <div class="col-md-4">
                        <label>Target Size:</label>
                        <input type="text" class="form-control form-control-sm" id="targetSize"
                               placeholder="e.g., 1100x620">
                    </div>
                    <div class="col-md-4">
                        <label>Notes:</label>
                        <input type="text" class="form-control form-control-sm" id="cellNotes"
                               placeholder="Optional notes">
                    </div>
                </div>
                <div class="mt-2">
                    <button type="button" class="btn btn-primary btn-sm" onclick="applyQuickEdit()">Apply</button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="closeQuickEdit()">Cancel</button>
                </div>
            </div>
        </div>
        ''')

        html.append('</div>')

        # Add CSS and JavaScript
        html.append(self._get_excel_editor_css())
        html.append(self._get_excel_editor_js())

        return ''.join(html)

    def _generate_cell_content(self, cell_data, height, width):
        """Generate content for a matrix cell"""
        if not cell_data:
            return '<span class="cell-empty">-</span>'

        content = []

        # Show cut plan reference (main purpose of the matrix)
        cut_plan = cell_data.get('cut_plan')
        if cut_plan:
            content.append(f'<div class="cut-plan"><strong>{cut_plan}</strong></div>')

            # Show master sheet size
            master_size = cell_data.get('master_size')
            if master_size:
                content.append(f'<div class="master-size">Master: {master_size}</div>')

            # Show efficiency
            efficiency = cell_data.get('efficiency')
            if efficiency:
                content.append(f'<div class="efficiency">{efficiency:.0%} efficient</div>')

            # Show byproduct count
            byproduct_count = cell_data.get('byproduct_count', 0)
            if byproduct_count > 0:
                content.append(f'<div class="byproducts">{byproduct_count} byproducts</div>')

        # Show if cut plan is needed
        elif cell_data.get('needs_cut_plan'):
            content.append('<div class="needs-plan">⚠️ Needs Cut Plan</div>')
            suggested = cell_data.get('suggested_master')
            if suggested:
                content.append(f'<div class="suggested">Suggest: {suggested}</div>')

        # Legacy support for old arrow/template format
        arrow = cell_data.get('arrow')
        if arrow == 'right':
            content.append('<span class="arrow-right">→</span>')
        elif arrow == 'down':
            content.append('<span class="arrow-down">↓</span>')

        cut_template = cell_data.get('cut_template')
        if cut_template and not cut_plan:  # Only show if no cut_plan already shown
            content.append(f'<div class="cut-template">{cut_template}</div>')

        # Add size info for legacy master sheets
        if cell_data.get('is_master') and not cut_plan:
            content.append(f'<div class="master-size">{height}×{width}</div>')
        elif cell_data.get('target_size'):
            target = cell_data['target_size']
            content.append(f'<div class="target-size">{target}</div>')

        return ''.join(content) if content else '<span class="cell-empty">-</span>'

    def _get_cell_class(self, cell_data):
        """Get CSS class for cell based on its data"""
        if not cell_data:
            return 'cell-empty'

        classes = []

        # Priority: cut plan > needs cut plan > legacy formats
        if cell_data.get('cut_plan'):
            classes.append('cell-has-cut-plan')
        elif cell_data.get('needs_cut_plan'):
            classes.append('cell-needs-cut-plan')
        elif cell_data.get('is_master'):
            classes.append('cell-master')
        elif cell_data.get('arrow'):
            classes.append('cell-arrow')
        elif cell_data.get('cut_template'):
            classes.append('cell-template')

        return ' '.join(classes)

    def _get_matrix_css(self):
        """Return CSS styles for the matrix"""
        return '''
        <style>
        .mesh-cut-matrix-container {
            overflow-x: auto;
            margin: 10px 0;
        }
        .mesh-cut-matrix-table {
            min-width: 100%;
            font-size: 12px;
        }
        .mesh-cut-matrix-table th,
        .mesh-cut-matrix-table td {
            text-align: center;
            vertical-align: middle;
            padding: 4px;
            min-width: 60px;
            height: 40px;
        }
        .matrix-corner {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .matrix-width-header,
        .matrix-height-header {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .matrix-cell {
            position: relative;
            cursor: pointer;
        }
        .cell-empty {
            color: #6c757d;
        }
        .cell-master {
            background-color: #d4edda;
            border: 2px solid #28a745;
        }
        .cell-arrow {
            background-color: #fff3cd;
        }
        .cell-template {
            background-color: #cce5ff;
        }
        .cell-has-cut-plan {
            background-color: #e8f5e8;
            border: 2px solid #4caf50;
        }
        .cell-needs-cut-plan {
            background-color: #fff3e0;
            border: 2px solid #ff9800;
        }
        .arrow-right,
        .arrow-down {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
        }
        .cut-template {
            font-size: 10px;
            color: #495057;
        }
        .cut-plan {
            font-weight: bold;
            color: #2e7d32;
            font-size: 12px;
        }
        .master-size {
            font-size: 10px;
            color: #666;
        }
        .efficiency {
            font-size: 10px;
            color: #1976d2;
        }
        .byproducts {
            font-size: 10px;
            color: #7b1fa2;
        }
        .needs-plan {
            font-size: 11px;
            color: #f57c00;
            font-weight: bold;
        }
        .suggested {
            font-size: 10px;
            color: #666;
            margin-top: 2px;
        }
        .master-size,
        .target-size {
            font-size: 10px;
            font-weight: bold;
        }
        .master-size {
            color: #28a745;
        }
        .target-size {
            color: #007bff;
        }
        </style>
        '''

    def _generate_excel_cell_content(self, cell_data, height, width):
        """Generate Excel-style content for a matrix cell"""
        if not cell_data:
            return f'<div class="cell-dimensions">{height}×{width}</div>'

        content = []

        # Add arrow with proper styling
        arrow = cell_data.get('arrow', '')
        if arrow == 'right':
            content.append('<div class="cell-arrow arrow-right">→</div>')
        elif arrow == 'down':
            content.append('<div class="cell-arrow arrow-down">↓</div>')

        # Add cut template or size info
        if cell_data.get('cut_template'):
            template = cell_data['cut_template']
            content.append(f'<div class="cell-template">{template}</div>')
        elif cell_data.get('target_size'):
            size = cell_data['target_size']
            content.append(f'<div class="cell-size">{size}</div>')

        # Add master/offcut indicators
        if cell_data.get('is_master'):
            content.append('<div class="cell-type master">MASTER</div>')
        elif cell_data.get('is_offcut'):
            content.append('<div class="cell-type offcut">OFFCUT</div>')

        # Add notes if present
        if cell_data.get('notes'):
            notes = cell_data['notes'][:15] + ('...' if len(cell_data['notes']) > 15 else '')
            content.append(f'<div class="cell-notes">{notes}</div>')

        # If no specific content, show dimensions
        if not content:
            content.append(f'<div class="cell-dimensions">{height}×{width}</div>')

        return ''.join(content)

    def _get_excel_cell_class(self, cell_data):
        """Get CSS class for Excel-style matrix cell"""
        classes = []

        if not cell_data:
            classes.append('cell-empty')
        else:
            if cell_data.get('is_master'):
                classes.append('cell-master')
            elif cell_data.get('is_offcut'):
                classes.append('cell-offcut')
            elif cell_data.get('cut_template'):
                classes.append('cell-template')
            elif cell_data.get('arrow'):
                classes.append('cell-arrow')
            else:
                classes.append('cell-configured')

        return ' '.join(classes)

    def _get_excel_editor_css(self):
        """Return Excel-style CSS for the matrix editor"""
        return '''
        <style>
        .mesh-cut-matrix-excel-editor {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px 0;
        }

        .editor-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }

        .editor-header h4 {
            margin: 0 0 10px 0;
            font-weight: 600;
        }

        .editor-toolbar {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }

        .current-mode-indicator {
            margin-top: 10px;
            padding: 8px 12px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            font-weight: 500;
        }

        .matrix-container {
            background: white;
            border-radius: 0 0 8px 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .excel-matrix-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            background: white;
        }

        .corner-cell {
            background: #f1f3f4;
            border: 1px solid #dadce0;
            padding: 8px;
            font-weight: bold;
            text-align: center;
            min-width: 60px;
        }

        .width-header, .height-header {
            background: #f8f9fa;
            border: 1px solid #dadce0;
            padding: 8px;
            font-weight: 600;
            text-align: center;
            min-width: 80px;
            color: #5f6368;
        }

        .excel-cell {
            border: 1px solid #dadce0;
            padding: 4px;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            position: relative;
            min-width: 80px;
            height: 50px;
            transition: all 0.2s ease;
            background: white;
        }

        .excel-cell:hover {
            background: #e8f0fe;
            border-color: #4285f4;
            box-shadow: 0 1px 3px rgba(66,133,244,0.3);
        }

        .excel-cell.selected {
            background: #1a73e8;
            color: white;
            border-color: #1a73e8;
            box-shadow: 0 2px 8px rgba(26,115,232,0.4);
        }

        /* Cell type styling - like Excel colors */
        .excel-cell.cell-empty {
            background: #ffffff;
        }

        .excel-cell.cell-master {
            background: #d4edda;
            border-color: #28a745;
        }

        .excel-cell.cell-offcut {
            background: #fff3cd;
            border-color: #ffc107;
        }

        .excel-cell.cell-template {
            background: #cce5ff;
            border-color: #0066cc;
        }

        .excel-cell.cell-arrow {
            background: #e1f5fe;
            border-color: #00bcd4;
        }
        </style>
        '''

    def _get_excel_editor_js(self):
        """Return basic JavaScript for Excel-style matrix editor"""
        return '''
        <script>
        // Basic Excel-style editor functionality
        let currentMode = 'select';
        let matrixData = {};

        function setArrowMode(direction) {
            currentMode = 'arrow_' + direction;
            updateModeIndicator();
        }

        function toggleMasterMode() {
            currentMode = currentMode === 'master' ? 'select' : 'master';
            updateModeIndicator();
        }

        function toggleOffcutMode() {
            currentMode = currentMode === 'offcut' ? 'select' : 'offcut';
            updateModeIndicator();
        }

        function updateModeIndicator() {
            const indicator = document.getElementById('currentMode');
            if (!indicator) return;

            let modeText = 'Mode: ';
            switch(currentMode) {
                case 'arrow_right': modeText += 'Adding Right Arrows →'; break;
                case 'arrow_down': modeText += 'Adding Down Arrows ↓'; break;
                case 'arrow_none': modeText += 'Removing Arrows'; break;
                case 'master': modeText += 'Marking Master Sheets ⬛'; break;
                case 'offcut': modeText += 'Marking Offcuts 📦'; break;
                default: modeText += 'Select cells to edit';
            }
            indicator.textContent = modeText;
        }

        function selectCell(cellElement, height, width) {
            // Apply current mode to cell
            console.log('Cell clicked:', height, width, 'Mode:', currentMode);
            // TODO: Implement cell editing based on mode
        }

        function clearSelectedCells() {
            console.log('Clear selected cells');
            // TODO: Implement clear functionality
        }

        function saveMatrix() {
            console.log('Save matrix');
            // TODO: Implement save functionality
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateModeIndicator();
        });
        </script>
        '''

    def get_best_fit(self, required_width, required_height):
        """Find the best mesh option for required dimensions

        First checks if there's a specific cut plan in the matrix for these dimensions,
        then falls back to the general search.

        Args:
            required_width (float): Required width in mm
            required_height (float): Required height in mm

        Returns:
            dict: Best fit option with details
        """
        # First check if the matrix has a specific cut plan for these dimensions
        matrix_result = self._get_matrix_cut_plan(required_width, required_height)
        if matrix_result:
            return matrix_result

        # Fall back to general search: unplanned -> planned -> master
        search_order = ['unplanned', 'planned', 'master']

        for mesh_type in search_order:
            result = self._search_mesh_type(mesh_type, required_width, required_height)
            if result:
                return result

        return None

    def _get_matrix_cut_plan(self, required_width, required_height):
        """Check if the matrix has a specific cut plan for these dimensions

        Args:
            required_width (float): Required width in mm
            required_height (float): Required height in mm

        Returns:
            dict: Cut plan result or None
        """
        if not self.matrix_data:
            return None

        try:
            matrix_data = json.loads(self.matrix_data)
        except (json.JSONDecodeError, TypeError):
            return None

        # Find the closest matrix cell that can accommodate these dimensions
        closest_height = self._find_closest_matrix_height(required_height)
        closest_width = self._find_closest_matrix_width(required_width)

        if not closest_height or not closest_width:
            return None

        cell_key = f"{closest_height}_{closest_width}"
        cell_data = matrix_data.get(cell_key)

        if not cell_data:
            return None

        # Check if this cell references a specific cut plan
        cut_plan_name = cell_data.get('cut_plan')
        if cut_plan_name:
            # Find the cut plan by name
            cut_plan = self.env['mesh.cut.plan'].search([
                ('matrix_id', '=', self.id),
                ('name', '=', cut_plan_name),
            ], limit=1)

            if cut_plan:
                # Check if we have the master sheet in stock
                master_products = self.env['product.product'].search([
                    ('mesh_series', '=', self.mesh_series),
                    ('mesh_type', '=', 'master'),
                    ('mesh_width', '=', cut_plan.master_width),
                    ('mesh_height', '=', cut_plan.master_height),
                ])

                for master_product in master_products:
                    quants = self.env['stock.quant'].search([
                        ('product_id', '=', master_product.id),
                        ('quantity', '>', 0)
                    ], limit=1)

                    if quants:
                        return {
                            'type': 'matrix_plan',
                            'product': master_product,
                            'quant': quants[0],
                            'cut_plan': cut_plan,
                            'byproducts': cut_plan.byproduct_ids,
                            'source': 'matrix_lookup',
                            'efficiency': self._calculate_efficiency(
                                cut_plan.master_width,
                                cut_plan.master_height,
                                required_width,
                                required_height
                            ),
                            'matrix_cell_height': closest_height,
                            'matrix_cell_width': closest_width,
                            'matrix_arrow_path': cell_data.get('arrow_direction', '')
                        }

        return None

    def _find_closest_matrix_height(self, required_height):
        """Find the closest height in the matrix that is >= required_height"""
        if not self.height_values:
            return None

        heights = [int(h.strip()) for h in self.height_values.split(',') if h.strip().isdigit()]
        heights.sort()

        # Find the smallest height that is >= required_height
        for height in heights:
            if height >= required_height:
                return height

        # If no height is large enough, return the largest available
        return heights[-1] if heights else None

    def _find_closest_matrix_width(self, required_width):
        """Find the closest width in the matrix that is >= required_width"""
        if not self.width_values:
            return None

        widths = [int(w.strip()) for w in self.width_values.split(',') if w.strip().isdigit()]
        widths.sort()

        # Find the smallest width that is >= required_width
        for width in widths:
            if width >= required_width:
                return width

        # If no width is large enough, return the largest available
        return widths[-1] if widths else None

    def get_all_suitable_options(self, required_width, required_height):
        """Find all suitable mesh options for required dimensions

        Args:
            required_width (float): Required width in mm
            required_height (float): Required height in mm

        Returns:
            list: All suitable options sorted by efficiency (best first)
        """
        all_options = []

        # Search all types and collect options
        search_order = ['unplanned', 'planned', 'master']

        for mesh_type in search_order:
            options = self._search_all_mesh_type(mesh_type, required_width, required_height)
            all_options.extend(options)

        # Sort by efficiency (highest first)
        all_options.sort(key=lambda x: x.get('efficiency', 0), reverse=True)

        return all_options

    def _search_all_mesh_type(self, mesh_type, width, height):
        """Search for ALL available mesh in specific category

        Args:
            mesh_type (str): Type of mesh to search ('unplanned', 'planned', 'master')
            width (float): Required width
            height (float): Required height

        Returns:
            list: All available mesh options
        """
        if mesh_type == 'unplanned':
            return self._search_all_unplanned_offcuts(width, height)
        elif mesh_type == 'planned':
            return self._search_all_planned_offcuts(width, height)
        elif mesh_type == 'master':
            return self._search_all_master_sheets(width, height)

        return []

    def _search_mesh_type(self, mesh_type, width, height):
        """Search for available mesh in specific category
        
        Args:
            mesh_type (str): Type of mesh to search ('unplanned', 'planned', 'master')
            width (float): Required width
            height (float): Required height
            
        Returns:
            dict: Available mesh details or None
        """
        if mesh_type == 'unplanned':
            return self._search_unplanned_offcuts(width, height)
        elif mesh_type == 'planned':
            return self._search_planned_offcuts(width, height)
        elif mesh_type == 'master':
            return self._search_master_sheets(width, height)
        
        return None
    
    def _search_unplanned_offcuts(self, width, height):
        """Search for suitable unplanned off-cuts"""
        # First find suitable lots with dimensions
        lot_domain = [
            ('product_id.mesh_series', '=', self.mesh_series),
            ('product_id.mesh_type', '=', 'unplanned'),
            ('mesh_width', '>=', width),
            ('mesh_height', '>=', height),
        ]

        lots = self.env['stock.lot'].search(lot_domain, order='mesh_width, mesh_height')

        # Find lots that have available quantity
        for lot in lots:
            quant_domain = [
                ('lot_id', '=', lot.id),
                ('quantity', '>', 0),
            ]
            quants = self.env['stock.quant'].search(quant_domain, limit=1)

            if quants:
                best_quant = quants[0]

                # Find matrix cell assignment for this unplanned off-cut
                matrix_info = self._find_matrix_cell_for_dimensions(lot.mesh_width, lot.mesh_height)

                result = {
                    'type': 'unplanned',
                    'product': best_quant.product_id,
                    'lot': lot,
                    'quant': best_quant,
                    'waste_width': lot.mesh_width - width,
                    'waste_height': lot.mesh_height - height,
                    'efficiency': self._calculate_efficiency(
                        lot.mesh_width,
                        lot.mesh_height,
                        width,
                        height
                    )
                }

                # Add matrix information if found
                if matrix_info:
                    result.update({
                        'matrix_cell_height': matrix_info.get('height', 0),
                        'matrix_cell_width': matrix_info.get('width', 0),
                        'matrix_cell_reference': matrix_info.get('cell_reference', ''),
                        'matrix_arrow_path': matrix_info.get('arrow_direction', ''),
                    })

                return result
        return None

    def _search_all_unplanned_offcuts(self, width, height):
        """Search for ALL suitable unplanned off-cuts"""
        options = []

        # Find all suitable lots with dimensions
        lot_domain = [
            ('product_id.mesh_series', '=', self.mesh_series),
            ('product_id.mesh_type', '=', 'unplanned'),
            ('mesh_width', '>=', width),
            ('mesh_height', '>=', height),
        ]

        lots = self.env['stock.lot'].search(lot_domain, order='mesh_width, mesh_height')

        # Find all lots that have available quantity
        for lot in lots:
            quant_domain = [
                ('lot_id', '=', lot.id),
                ('quantity', '>', 0),
            ]
            quants = self.env['stock.quant'].search(quant_domain, limit=1)

            if quants:
                best_quant = quants[0]

                # Find matrix cell assignment for this unplanned off-cut
                matrix_info = self._find_matrix_cell_for_dimensions(lot.mesh_width, lot.mesh_height)

                option = {
                    'type': 'unplanned',
                    'product': best_quant.product_id,
                    'lot': lot,
                    'quant': best_quant,
                    'waste_width': lot.mesh_width - width,
                    'waste_height': lot.mesh_height - height,
                    'efficiency': self._calculate_efficiency(
                        lot.mesh_width,
                        lot.mesh_height,
                        width,
                        height
                    )
                }

                # Add matrix information if found
                if matrix_info:
                    option.update({
                        'matrix_cell_height': matrix_info.get('height', 0),
                        'matrix_cell_width': matrix_info.get('width', 0),
                        'matrix_cell_reference': matrix_info.get('cell_reference', ''),
                        'matrix_arrow_path': matrix_info.get('arrow_direction', ''),
                    })

                options.append(option)

        return options

    def _search_planned_offcuts(self, width, height):
        """Search for suitable planned off-cuts"""
        domain = [
            ('mesh_series', '=', self.mesh_series),
            ('mesh_type', '=', 'planned'),
            ('mesh_width', '>=', width),
            ('mesh_height', '>=', height),
        ]

        products = self.env['product.product'].search(domain, order='mesh_width, mesh_height')

        for product in products:
            quants = self.env['stock.quant'].search([
                ('product_id', '=', product.id),
                ('quantity', '>', 0)
            ])
            if quants:
                # Find matrix cell assignment for this planned off-cut
                matrix_info = self._find_matrix_cell_for_dimensions(product.mesh_width, product.mesh_height)

                result = {
                    'type': 'planned',
                    'product': product,
                    'quant': quants[0],
                    'waste_width': product.mesh_width - width,
                    'waste_height': product.mesh_height - height,
                    'efficiency': self._calculate_efficiency(
                        product.mesh_width,
                        product.mesh_height,
                        width,
                        height
                    )
                }

                # Add matrix information if found
                if matrix_info:
                    result.update({
                        'matrix_cell_height': matrix_info.get('height', 0),
                        'matrix_cell_width': matrix_info.get('width', 0),
                        'matrix_cell_reference': matrix_info.get('cell_reference', ''),
                        'matrix_arrow_path': matrix_info.get('arrow_direction', ''),
                    })

                    # Add cut plan information if available in matrix
                    if matrix_info.get('cut_plan_id'):
                        cut_plan = self.env['mesh.cut.plan'].browse(matrix_info.get('cut_plan_id'))
                        if cut_plan.exists():
                            result['cut_plan'] = cut_plan

                return result
        return None

    def _search_all_planned_offcuts(self, width, height):
        """Search for ALL suitable planned off-cuts"""
        options = []

        domain = [
            ('mesh_series', '=', self.mesh_series),
            ('mesh_type', '=', 'planned'),
            ('mesh_width', '>=', width),
            ('mesh_height', '>=', height),
        ]

        products = self.env['product.product'].search(domain, order='mesh_width, mesh_height')

        for product in products:
            quants = self.env['stock.quant'].search([
                ('product_id', '=', product.id),
                ('quantity', '>', 0)
            ])
            if quants:
                # Find matrix cell assignment for this planned off-cut
                matrix_info = self._find_matrix_cell_for_dimensions(product.mesh_width, product.mesh_height)

                option = {
                    'type': 'planned',
                    'product': product,
                    'quant': quants[0],
                    'waste_width': product.mesh_width - width,
                    'waste_height': product.mesh_height - height,
                    'efficiency': self._calculate_efficiency(
                        product.mesh_width,
                        product.mesh_height,
                        width,
                        height
                    )
                }

                # Add matrix information if found
                if matrix_info:
                    option.update({
                        'matrix_cell_height': matrix_info.get('height', 0),
                        'matrix_cell_width': matrix_info.get('width', 0),
                        'matrix_cell_reference': matrix_info.get('cell_reference', ''),
                        'matrix_arrow_path': matrix_info.get('arrow_direction', ''),
                    })

                    # Add cut plan information if available in matrix
                    if matrix_info.get('cut_plan_id'):
                        try:
                            cut_plan_id = int(matrix_info.get('cut_plan_id'))
                            cut_plan = self.env['mesh.cut.plan'].browse(cut_plan_id)
                            if cut_plan.exists():
                                option['cut_plan'] = cut_plan
                        except (ValueError, TypeError):
                            # Skip invalid cut_plan_id
                            pass

                options.append(option)

        return options

    def _find_matrix_cell_for_dimensions(self, product_width, product_height):
        """Find the matrix cell that corresponds to given dimensions"""
        try:
            # Parse matrix data
            matrix_data = {}
            if self.matrix_data:
                try:
                    matrix_data = json.loads(self.matrix_data)
                except (json.JSONDecodeError, TypeError):
                    matrix_data = {}

            # Look for exact match first using the key format "height_width"
            exact_key = f"{product_height}_{product_width}"
            if exact_key in matrix_data:
                cell_data = matrix_data[exact_key]
                result = {
                    'height': product_height,
                    'width': product_width,
                    'cell_reference': self._get_cell_reference(product_height, product_width),
                    'arrow_direction': cell_data.get('arrow_direction', cell_data.get('arrow', '')),
                }

                # Include cut plan information if available
                if cell_data.get('cut_plan_id'):
                    result['cut_plan_id'] = cell_data.get('cut_plan_id')
                    result['cut_plan_name'] = cell_data.get('cut_plan_name')
                    result['has_cut_plan'] = cell_data.get('has_cut_plan', False)

                return result

            # If no exact match, find the closest suitable cell
            best_match = None
            best_efficiency = 0

            for cell_key, cell_data in matrix_data.items():
                if isinstance(cell_data, dict) and '_' in cell_key:
                    try:
                        # Parse dimensions from key "height_width"
                        height_str, width_str = cell_key.split('_', 1)
                        cell_height = int(height_str)
                        cell_width = int(width_str)

                        # Check if this cell could produce the product dimensions
                        if cell_width >= product_width and cell_height >= product_height:
                            efficiency = self._calculate_efficiency(
                                cell_width, cell_height, product_width, product_height
                            )

                            if efficiency > best_efficiency:
                                best_efficiency = efficiency
                                best_match = {
                                    'height': cell_height,
                                    'width': cell_width,
                                    'cell_reference': self._get_cell_reference(cell_height, cell_width),
                                    'arrow_direction': cell_data.get('arrow_direction', cell_data.get('arrow', '')),
                                }

                                # Include cut plan information if available
                                if cell_data.get('cut_plan_id'):
                                    best_match['cut_plan_id'] = cell_data.get('cut_plan_id')
                                    best_match['cut_plan_name'] = cell_data.get('cut_plan_name')
                                    best_match['has_cut_plan'] = cell_data.get('has_cut_plan', False)
                    except (ValueError, IndexError):
                        continue

            return best_match

        except Exception as e:
            _logger.warning(f"Error finding matrix cell for {product_width}x{product_height}: {e}")
            return None

    def _get_cell_reference(self, height, width):
        """Convert height/width to cell reference like 'I5'"""
        try:
            # Parse height and width values from the matrix
            height_values = []
            width_values = []

            if self.height_values:
                height_values = [int(h.strip()) for h in self.height_values.split(',') if h.strip()]
            if self.width_values:
                width_values = [int(w.strip()) for w in self.width_values.split(',') if w.strip()]

            # Find the indices
            height_index = -1
            width_index = -1

            if height in height_values:
                height_index = height_values.index(height)
            if width in width_values:
                width_index = width_values.index(width)

            if height_index >= 0 and width_index >= 0:
                # Convert to Excel-style reference (A=0, B=1, etc.)
                col_letter = chr(ord('A') + width_index) if width_index < 26 else f"A{chr(ord('A') + width_index - 26)}"
                row_number = height_index + 1
                return f"{col_letter}{row_number}"
            else:
                # Fallback to dimensions
                return f"{height}x{width}"

        except Exception as e:
            _logger.warning(f"Error generating cell reference for {height}x{width}: {e}")
            return f"{height}x{width}"

    def _search_master_sheets(self, width, height):
        """Search master sheets and create cutting plan"""
        # Find suitable cut plan
        suitable_plans = self.cut_plan_ids.filtered(
            lambda p: p.master_width >= width and p.master_height >= height
        ).sorted(lambda p: p.master_width * p.master_height)  # Smallest first
        
        for plan in suitable_plans:
            # Check if we have the required master sheet in stock
            master_products = self.env['product.product'].search([
                ('mesh_series', '=', self.mesh_series),
                ('mesh_type', '=', 'master'),
                ('mesh_width', '=', plan.master_width),
                ('mesh_height', '=', plan.master_height),
            ])
            
            for master_product in master_products:
                quants = self.env['stock.quant'].search([
                    ('product_id', '=', master_product.id),
                    ('quantity', '>', 0)
                ])
                
                if quants:
                    # Find matrix cell assignment for this master sheet
                    matrix_info = self._find_matrix_cell_for_dimensions(plan.master_width, plan.master_height)

                    result = {
                        'type': 'master',
                        'product': master_product,
                        'quant': quants[0],
                        'cut_plan': plan,
                        'byproducts': plan.byproduct_ids,
                        'efficiency': self._calculate_efficiency(
                            plan.master_width,
                            plan.master_height,
                            width,
                            height
                        )
                    }

                    # Add matrix information if found
                    if matrix_info:
                        result.update({
                            'matrix_cell_height': matrix_info.get('height', 0),
                            'matrix_cell_width': matrix_info.get('width', 0),
                            'matrix_cell_reference': matrix_info.get('cell_reference', ''),
                            'matrix_arrow_path': matrix_info.get('arrow_direction', ''),
                        })

                    return result
        
        return None

    def _search_all_master_sheets(self, width, height):
        """Search ALL master sheets and create cutting plans"""
        options = []

        # Find all suitable cut plans
        suitable_plans = self.cut_plan_ids.filtered(
            lambda p: p.master_width >= width and p.master_height >= height
        ).sorted(lambda p: p.master_width * p.master_height)  # Smallest first

        for plan in suitable_plans:
            # Check if we have the required master sheet in stock
            master_products = self.env['product.product'].search([
                ('mesh_series', '=', self.mesh_series),
                ('mesh_type', '=', 'master'),
                ('mesh_width', '=', plan.master_width),
                ('mesh_height', '=', plan.master_height),
            ])

            for master_product in master_products:
                quants = self.env['stock.quant'].search([
                    ('product_id', '=', master_product.id),
                    ('quantity', '>', 0)
                ])

                if quants:
                    # Find matrix cell assignment for this master sheet
                    matrix_info = self._find_matrix_cell_for_dimensions(plan.master_width, plan.master_height)

                    option = {
                        'type': 'master',
                        'product': master_product,
                        'quant': quants[0],
                        'cut_plan': plan,
                        'byproducts': plan.byproduct_ids,
                        'efficiency': self._calculate_efficiency(
                            plan.master_width,
                            plan.master_height,
                            width,
                            height
                        )
                    }

                    # Add matrix information if found
                    if matrix_info:
                        option.update({
                            'matrix_cell_height': matrix_info.get('height', 0),
                            'matrix_cell_width': matrix_info.get('width', 0),
                            'matrix_cell_reference': matrix_info.get('cell_reference', ''),
                            'matrix_arrow_path': matrix_info.get('arrow_direction', ''),
                        })

                    options.append(option)

        return options

    def _calculate_efficiency(self, available_width, available_height, required_width, required_height):
        """Calculate material efficiency as decimal (0.64 = 64%)"""
        if available_width <= 0 or available_height <= 0:
            return 0.0

        available_area = available_width * available_height
        required_area = required_width * required_height

        return required_area / available_area
    
    def action_view_cut_plans(self):
        """View cut plans for this matrix"""
        return {
            'name': f'Cut Plans - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.plan',
            'view_mode': 'list,form',
            'domain': [('matrix_id', '=', self.id)],
            'context': {'default_matrix_id': self.id},
        }

    def update_matrix_data(self, matrix_data):
        """Update matrix data from visual editor"""
        self.ensure_one()
        self.matrix_data = json.dumps(matrix_data) if isinstance(matrix_data, dict) else matrix_data
        return True

    def get_matrix_data_dict(self):
        """Get matrix data as dictionary"""
        try:
            return json.loads(self.matrix_data or '{}')
        except json.JSONDecodeError:
            return {}

    def set_cell_data(self, height, width, cell_data):
        """Set data for a specific cell"""
        matrix_data = self.get_matrix_data_dict()
        cell_key = f"{height}_{width}"

        if cell_data:
            matrix_data[cell_key] = cell_data
        else:
            matrix_data.pop(cell_key, None)

        self.matrix_data = json.dumps(matrix_data)

    def get_cell_data(self, height, width):
        """Get data for a specific cell"""
        matrix_data = self.get_matrix_data_dict()
        cell_key = f"{height}_{width}"
        return matrix_data.get(cell_key, {})

    def assign_cut_plan_to_cell(self, height, width, cut_plan_id):
        """Assign a cut plan to a specific matrix cell (legacy - single plan)"""
        return self.assign_cut_plans_to_cell(height, width, [cut_plan_id])

    def assign_cut_plans_to_cell(self, height, width, cut_plan_ids):
        """Assign multiple cut plans to a specific matrix cell"""
        self.ensure_one()

        # Ensure cut_plan_ids is a list
        if not isinstance(cut_plan_ids, list):
            cut_plan_ids = [cut_plan_ids]

        if len(cut_plan_ids) > 2:
            raise ValidationError(_("Maximum 2 cut plans allowed per cell"))

        # Validate all cut plans belong to this matrix
        cut_plans = self.env['mesh.cut.plan'].browse(cut_plan_ids)
        for cut_plan in cut_plans:
            if not cut_plan.exists() or cut_plan.matrix_id != self:
                raise ValidationError(_("Cut plan %s does not belong to this matrix") % cut_plan.name)

        # Get current cell data
        cell_data = self.get_cell_data(height, width)

        # Update with cut plan assignment(s)
        if len(cut_plans) == 1:
            # Single cut plan (legacy format)
            cut_plan = cut_plans[0]
            cell_data.update({
                'cut_plan_id': cut_plan.id,
                'cut_plan_name': cut_plan.name,
                'master_size': f'{cut_plan.master_width}x{cut_plan.master_height}',
                'efficiency': self._calculate_efficiency(
                    cut_plan.master_width, cut_plan.master_height, width, height
                ),
                'byproduct_count': cut_plan.byproduct_count,
                'has_cut_plan': True
            })
        else:
            # Multiple cut plans (new format)
            cut_plans_data = []
            master_sizes = []
            total_byproducts = 0

            for cut_plan in cut_plans:
                plan_data = {
                    'id': cut_plan.id,
                    'name': cut_plan.name,
                    'master_width': cut_plan.master_width,
                    'master_height': cut_plan.master_height,
                    'efficiency': self._calculate_efficiency(
                        cut_plan.master_width, cut_plan.master_height, width, height
                    ),
                    'byproduct_count': cut_plan.byproduct_count
                }
                cut_plans_data.append(plan_data)
                master_sizes.append(f'{cut_plan.master_width}x{cut_plan.master_height}')
                total_byproducts += cut_plan.byproduct_count

            cell_data.update({
                'cut_plans': cut_plans_data,
                'master_size': ', '.join(master_sizes),
                'byproduct_count': total_byproducts,
                'has_cut_plan': True
            })

        # Save the updated cell data
        self.set_cell_data(height, width, cell_data)

        return True

    def remove_cut_plan_from_cell(self, height, width):
        """Remove cut plan assignment from a matrix cell"""
        self.ensure_one()

        cell_data = self.get_cell_data(height, width)

        # Remove cut plan related data (both single and multiple formats, plus cut to size)
        cut_plan_keys = [
            'cut_plan_id', 'cut_plan_name', 'cut_plans',
            'master_size', 'efficiency', 'byproduct_count', 'has_cut_plan',
            'cut_to_size'
        ]
        for key in cut_plan_keys:
            cell_data.pop(key, None)

        # Save the updated cell data
        self.set_cell_data(height, width, cell_data)

        return True

    def get_cell_cut_plan(self, height, width):
        """Get the cut plan assigned to a specific cell"""
        cell_data = self.get_cell_data(height, width)
        cut_plan_id = cell_data.get('cut_plan_id')

        if cut_plan_id:
            return self.env['mesh.cut.plan'].browse(cut_plan_id)

        return self.env['mesh.cut.plan']

    def get_available_cut_plans_for_cell(self, height, width):
        """Get all cut plans available for assignment to any cell (no dimension restrictions)"""
        self.ensure_one()

        # Return all cut plans - any cut plan can be assigned to any cell
        all_plans = self.cut_plan_ids

        # Sort by efficiency (smaller master sheets first for better efficiency)
        sorted_plans = all_plans.sorted(
            key=lambda plan: plan.master_width * plan.master_height
        )

        return sorted_plans

    @api.model
    def get_cut_plans_for_matrix_cell(self, matrix_id, height, width):
        """API method to get available cut plans for a matrix cell"""
        matrix = self.browse(matrix_id)
        if not matrix.exists():
            return {'error': 'Matrix not found'}

        plans = matrix.get_available_cut_plans_for_cell(height, width)

        return {
            'success': True,
            'plans': [{
                'id': plan.id,
                'name': plan.name,
                'master_width': plan.master_width,
                'master_height': plan.master_height,
                'byproduct_count': plan.byproduct_count,
                'efficiency': matrix._calculate_efficiency(
                    plan.master_width, plan.master_height, width, height
                )
            } for plan in plans]
        }

    @api.model
    def api_assign_cut_plan_to_cell(self, matrix_id, height, width, cut_plan_id):
        """API method to assign cut plan to matrix cell (legacy - single plan)"""
        return self.api_assign_cut_plans_to_cell(matrix_id, height, width, [cut_plan_id])

    @api.model
    def api_assign_cut_plans_to_cell(self, matrix_id, height, width, cut_plan_ids):
        """API method to assign multiple cut plans to matrix cell"""
        try:
            matrix = self.browse(matrix_id)
            if not matrix.exists():
                return {'success': False, 'error': 'Matrix not found'}

            # Validate cut plan IDs
            if not isinstance(cut_plan_ids, list):
                cut_plan_ids = [cut_plan_ids]

            if len(cut_plan_ids) > 2:
                return {'success': False, 'error': 'Maximum 2 cut plans allowed per cell'}

            matrix.assign_cut_plans_to_cell(height, width, cut_plan_ids)
            return {'success': True}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def assign_cut_to_size_to_cell(self, height, width):
        """Assign cut to size to a specific matrix cell"""
        self.ensure_one()

        # Get current cell data
        cell_data = self.get_cell_data(height, width)

        # Update with cut to size assignment
        cell_data.update({
            'cut_to_size': True,
            'has_cut_plan': False,
            'arrow_direction': None,
            # Clear any existing cut plan data
            'cut_plan_id': None,
            'cut_plan_name': None,
            'cut_plans': None,
            'master_size': None,
            'efficiency': None,
            'byproduct_count': None
        })

        # Save the updated cell data
        self.set_cell_data(height, width, cell_data)

        return True

    @api.model
    def api_assign_cut_to_size_to_cell(self, matrix_id, height, width):
        """API method to assign cut to size to matrix cell"""
        try:
            matrix = self.browse(matrix_id)
            if not matrix.exists():
                return {'success': False, 'error': 'Matrix not found'}

            matrix.assign_cut_to_size_to_cell(height, width)
            return {'success': True}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def update_matrix_dimensions(self, heights, widths):
        """Update matrix dimensions with new height and width values"""
        self.ensure_one()

        # Convert lists to comma-separated strings
        if isinstance(heights, list):
            self.height_values = ','.join(map(str, heights))
        else:
            self.height_values = heights

        if isinstance(widths, list):
            self.width_values = ','.join(map(str, widths))
        else:
            self.width_values = widths

        return True

    @api.model
    def api_update_matrix_dimensions(self, matrix_id, heights, widths):
        """API method to update matrix dimensions"""
        try:
            matrix = self.browse(matrix_id)
            if not matrix.exists():
                return {'success': False, 'error': 'Matrix not found'}

            matrix.update_matrix_dimensions(heights, widths)
            return {'success': True}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @api.model
    def api_remove_cut_plan_from_cell(self, matrix_id, height, width):
        """API method to remove cut plan from matrix cell"""
        try:
            matrix = self.browse(matrix_id)
            if not matrix.exists():
                return {'success': False, 'error': 'Matrix not found'}

            matrix.remove_cut_plan_from_cell(height, width)
            return {'success': True}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @api.model
    def api_assign_arrow_to_cell(self, matrix_id, height, width, direction):
        """API method to assign arrow direction to matrix cell"""
        try:
            matrix = self.browse(matrix_id)
            if not matrix.exists():
                return {'success': False, 'error': 'Matrix not found'}

            # Parse existing matrix data
            matrix_data = {}
            if matrix.matrix_data:
                try:
                    matrix_data = json.loads(matrix.matrix_data)
                except (json.JSONDecodeError, TypeError):
                    matrix_data = {}

            # Update cell with arrow direction
            key = f"{height}_{width}"
            matrix_data[key] = {
                'has_cut_plan': False,
                'arrow_direction': direction,
                'direction_type': 'horizontal' if direction == 'right' else 'vertical'
            }

            # Save updated matrix data
            matrix.matrix_data = json.dumps(matrix_data)

            _logger.info(f"Arrow direction '{direction}' assigned to cell {height}x{width} in matrix {matrix_id}")
            return {'success': True, 'message': 'Arrow direction assigned successfully'}
        except Exception as e:
            _logger.error(f"Error assigning arrow to cell: {e}")
            return {'success': False, 'error': str(e)}

    def action_fill_sample_data(self):
        """Fill matrix with sample cutting data"""
        if not self.height_values or not self.width_values:
            return

        try:
            heights = [int(h.strip()) for h in self.height_values.split(',') if h.strip()]
            widths = [int(w.strip()) for w in self.width_values.split(',') if w.strip()]
        except ValueError:
            return

        matrix_data = {}

        # Sample data: link specific dimensions to cut plans
        # This creates a lookup table: dimensions -> cut plan

        # Find existing cut plans for this matrix
        cut_plans = self.cut_plan_ids

        for height in heights:
            for width in widths:
                cell_key = f"{height}_{width}"

                # Link specific dimensions to cut plans
                for cut_plan in cut_plans:
                    # If the required dimensions fit within this cut plan's master sheet
                    if (width <= cut_plan.master_width and
                        height <= cut_plan.master_height):

                        # Calculate efficiency to see if this is a good fit
                        efficiency = self._calculate_efficiency(
                            cut_plan.master_width, cut_plan.master_height, width, height
                        )

                        # Only use this cut plan if efficiency is reasonable (>30%)
                        if efficiency > 0.3:
                            matrix_data[cell_key] = {
                                'cut_plan_id': cut_plan.id,
                                'cut_plan_name': cut_plan.name,
                                'master_size': f'{cut_plan.master_width}x{cut_plan.master_height}',
                                'efficiency': efficiency,
                                'byproduct_count': len(cut_plan.byproduct_ids),
                                'has_cut_plan': True
                            }
                            break  # Use the first suitable cut plan

                # If no cut plan found, mark as needing a cut plan
                if cell_key not in matrix_data:
                    matrix_data[cell_key] = {
                        'needs_cut_plan': True,
                        'suggested_master': f'{width + 100}x{height + 100}',
                        'has_cut_plan': False
                    }

        self.matrix_data = json.dumps(matrix_data)
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    def action_create_sample_cut_plans(self):
        """Create sample cut plans for demonstration"""
        self.ensure_one()

        # Create sample cut plans if none exist
        if not self.cut_plan_ids:
            self._create_sample_cut_plans()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Sample Cut Plans Created',
                'message': f'Created {len(self.cut_plan_ids)} sample cut plans for testing.',
                'type': 'success',
            }
        }

    def _create_sample_cut_plans(self):
        """Create sample cut plans for demonstration"""
        sample_plans = [
            {
                'master_width': 1100,
                'master_height': 620,
                'instructions': 'Standard 1100x620 master sheet cutting plan',
                'byproducts': [
                    {'width': 550, 'height': 620, 'quantity': 1},
                    {'width': 550, 'height': 310, 'quantity': 1},
                ]
            },
            {
                'master_width': 1250,
                'master_height': 1000,
                'instructions': 'Large 1250x1000 master sheet cutting plan',
                'byproducts': [
                    {'width': 625, 'height': 1000, 'quantity': 1},
                    {'width': 625, 'height': 500, 'quantity': 1},
                ]
            },
            {
                'master_width': 2000,
                'master_height': 750,
                'instructions': 'Wide 2000x750 master sheet cutting plan',
                'byproducts': [
                    {'width': 1000, 'height': 750, 'quantity': 1},
                    {'width': 1000, 'height': 375, 'quantity': 1},
                ]
            }
        ]

        for plan_data in sample_plans:
            # Create the cut plan
            cut_plan = self.env['mesh.cut.plan'].create({
                'matrix_id': self.id,
                'master_width': plan_data['master_width'],
                'master_height': plan_data['master_height'],
                'cut_instructions': plan_data['instructions'],
            })

            # Create byproducts
            for byproduct_data in plan_data['byproducts']:
                self.env['mesh.cut.byproduct'].create({
                    'cut_plan_id': cut_plan.id,
                    'width': byproduct_data['width'],
                    'height': byproduct_data['height'],
                    'quantity': byproduct_data['quantity'],
                })

    def action_test_matrix_lookup(self):
        """Test the matrix lookup functionality with sample dimensions"""
        self.ensure_one()

        # Test with some sample dimensions
        test_cases = [
            (600, 400),   # Small size
            (800, 600),   # Medium size
            (1000, 800),  # Large size that should find a cut plan
            (1300, 1600), # Your original example
        ]

        results = []
        for width, height in test_cases:
            # Test the matrix lookup
            matrix_result = self._get_matrix_cut_plan(width, height)

            # Test the general best fit
            best_fit = self.get_best_fit(width, height)

            result_info = {
                'dimensions': f'{width}x{height}mm',
                'matrix_result': 'Found' if matrix_result else 'Not found',
                'best_fit_type': best_fit.get('type') if best_fit else 'None',
                'source': best_fit.get('source') if best_fit else 'None',
            }

            if matrix_result:
                result_info['cut_plan'] = matrix_result['cut_plan'].name
                result_info['master_sheet'] = f"{matrix_result['cut_plan'].master_width}x{matrix_result['cut_plan'].master_height}mm"
                result_info['byproducts'] = len(matrix_result['byproducts'])

            results.append(result_info)

        # Format the results message
        message_lines = [
            "<h3>Matrix Lookup Test Results</h3>",
            "<table class='table table-sm table-bordered'>",
            "<thead><tr><th>Dimensions</th><th>Matrix</th><th>Cut Plan</th><th>Master Sheet</th><th>Byproducts</th><th>Best Fit Type</th></tr></thead>",
            "<tbody>"
        ]

        for result in results:
            cut_plan = result.get('cut_plan', '-')
            master_sheet = result.get('master_sheet', '-')
            byproducts = result.get('byproducts', '-')

            message_lines.append(
                f"<tr>"
                f"<td>{result['dimensions']}</td>"
                f"<td>{result['matrix_result']}</td>"
                f"<td>{cut_plan}</td>"
                f"<td>{master_sheet}</td>"
                f"<td>{byproducts}</td>"
                f"<td>{result['best_fit_type']}</td>"
                f"</tr>"
            )

        message_lines.extend(["</tbody></table>"])

        # Add explanation
        message_lines.extend([
            "<div class='alert alert-info mt-3'>",
            "<h4>How It Works:</h4>",
            "<ul>",
            "<li><strong>Matrix Lookup:</strong> Checks if there's a specific cut plan defined for these dimensions</li>",
            "<li><strong>Cut Plan:</strong> Defines how to cut a master sheet and what byproducts are created</li>",
            "<li><strong>Byproducts:</strong> Off-cuts that are automatically created and put back into stock</li>",
            "<li><strong>Best Fit:</strong> Falls back to general search if no matrix entry exists</li>",
            "</ul>",
            "</div>"
        ])

        message = "".join(message_lines)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Matrix Lookup Test',
                'message': message,
                'type': 'info',
                'sticky': True,
            }
        }


class MeshCutPlan(models.Model):
    _name = 'mesh.cut.plan'
    _description = 'Individual Cut Plan for Master Sheets'
    _order = 'master_width, master_height'
    
    name = fields.Char('Plan Name', compute='_compute_name', store=True)
    matrix_id = fields.Many2one('mesh.cut.matrix', 'Cut Matrix', required=True, ondelete='cascade')
    
    # Master sheet dimensions
    master_width = fields.Float('Master Sheet Width (mm)', required=True)
    master_height = fields.Float('Master Sheet Height (mm)', required=True)
    
    # Cut diagram as SVG or JSON
    cut_diagram = fields.Text('Cut Diagram',
                            help="SVG or JSON representation of the cutting diagram")
    cut_diagram_display = fields.Html('Cut Diagram Display', compute='_compute_cut_diagram_display', sanitize=False)
    cut_instructions = fields.Text('Cut Instructions',
                                 help="Human-readable cutting instructions")
    
    # Byproducts created from this cut
    byproduct_ids = fields.One2many('mesh.cut.byproduct', 'cut_plan_id', 'Planned Byproducts')

    # PDF Diagram for visualization
    diagram_pdf = fields.Binary("Diagram PDF", help="Upload a PDF diagram showing the cutting layout")
    diagram_pdf_filename = fields.Char("PDF Filename")

    # Statistics
    byproduct_count = fields.Integer('Byproduct Count', compute='_compute_byproduct_count')
    total_byproduct_area = fields.Float('Total Byproduct Area (mm²)', compute='_compute_byproduct_stats')
    waste_percentage = fields.Float('Waste Percentage', compute='_compute_byproduct_stats')

    # Inverse relationship - which cells use this cut plan
    assigned_cells = fields.Text('Assigned Matrix Cells', compute='_compute_assigned_cells',
                                help="List of matrix cells that use this cut plan")
    assigned_cells_count = fields.Integer('Assigned Cells Count', compute='_compute_assigned_cells')
    assigned_cells_display = fields.Html('Assigned Cells Display', compute='_compute_assigned_cells',
                                        help="HTML display of assigned cells as tags")
    
    @api.depends('master_width', 'master_height')
    def _compute_name(self):
        for plan in self:
            plan.name = f"{plan.master_width}x{plan.master_height}mm"



    def action_create_base_svg(self):
        """Action to create a base SVG component"""
        self.ensure_one()

        # Create a base SVG component with a template
        base_svg_content = '''<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600" width="100%" height="100%">
  <!-- Grid background -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)"/>

  <!-- Title -->
  <text x="400" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#333">
    ${name} Cut Plan
  </text>

  <!-- Master sheet outline -->
  <rect x="100" y="80" width="600" height="400"
        fill="none" stroke="#333" stroke-width="2"/>

  <!-- Dimensions -->
  <text x="400" y="500" text-anchor="middle" font-size="16" fill="#666">
    ${master_width} x ${master_height} mm
  </text>
</svg>'''

        component = self.env['mesh.cut.plan.svg.component'].create({
            'name': f'Base - {self.name}',
            'cut_plan_id': self.id,
            'component_type': 'base',
            'z_index': 1,
            'svg_content': base_svg_content,
        })

        return {
            'name': 'Base SVG Component',
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.plan.svg.component',
            'view_mode': 'form',
            'res_id': component.id,
        }

    def action_add_svg_layer(self):
        """Action to add a new SVG layer"""
        self.ensure_one()

        # Create a new layer component
        layer_svg_content = '''<!-- Add your layer content here -->
<g id="layer">
  <!-- Example: Cut lines -->
  <line x1="100" y1="280" x2="700" y2="280" stroke="#ff0000" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="400" y="270" text-anchor="middle" font-size="12" fill="#ff0000">Cut Line</text>
</g>'''

        component = self.env['mesh.cut.plan.svg.component'].create({
            'name': f'Layer - {self.name}',
            'cut_plan_id': self.id,
            'component_type': 'layer',
            'z_index': 10,
            'svg_content': layer_svg_content,
        })

        return {
            'name': 'SVG Layer Component',
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.plan.svg.component',
            'view_mode': 'form',
            'res_id': component.id,
        }

    def get_svg_components(self):
        """Get SVG components and plan values for rendering"""
        self.ensure_one()

        try:
            # Get all components ordered by z_index
            components = self.svg_component_ids.sorted('z_index')

            # Prepare component data
            component_data = []
            for component in components:
                component_data.append({
                    'id': component.id,
                    'name': component.name,
                    'component_type': component.component_type,
                    'svg_content': component.svg_content,
                    'condition': component.condition,
                    'z_index': component.z_index,
                })

            # Prepare plan values for template processing
            plan_values = {
                'name': self.name,
                'master_width': self.master_width,
                'master_height': self.master_height,
                'byproduct_count': self.byproduct_count,
                'total_byproduct_area': self.total_byproduct_area,
                'waste_percentage': self.waste_percentage,
            }

            return {
                'success': True,
                'components': component_data,
                'plan_values': plan_values,
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'components': [],
                'plan_values': {},
            }

    @api.depends('cut_diagram')
    def _compute_cut_diagram_display(self):
        for plan in self:
            if plan.cut_diagram:
                # Use Markup to prevent HTML escaping
                plan.cut_diagram_display = Markup(plan.cut_diagram)
            else:
                plan.cut_diagram_display = Markup('''
                <div class="text-center text-muted p-5">
                    <i class="fas fa-image fa-3x mb-3"></i>
                    <p>No cutting diagram available</p>
                    <p class="small">Add SVG content to the Cut Diagram field to see the visualization</p>
                </div>
                ''')
    
    @api.depends('byproduct_ids')
    def _compute_byproduct_count(self):
        for plan in self:
            plan.byproduct_count = len(plan.byproduct_ids)
    
    @api.depends('byproduct_ids.width', 'byproduct_ids.height', 'byproduct_ids.quantity')
    def _compute_byproduct_stats(self):
        for plan in self:
            total_area = 0
            for byproduct in plan.byproduct_ids:
                total_area += (byproduct.width * byproduct.height * byproduct.quantity)

            plan.total_byproduct_area = total_area

            master_area = plan.master_width * plan.master_height
            if master_area > 0:
                plan.waste_percentage = ((master_area - total_area) / master_area) * 100
            else:
                plan.waste_percentage = 0.0

    @api.depends('matrix_id', 'matrix_id.matrix_data', 'matrix_id.height_values', 'matrix_id.width_values')
    def _compute_assigned_cells(self):
        """Compute which matrix cells are assigned to this cut plan"""
        for plan in self:
            if not plan.matrix_id:
                plan.assigned_cells = ""
                plan.assigned_cells_count = 0
                plan.assigned_cells_display = Markup('<span style="color: #6c757d; font-style: italic;">No matrix assigned</span>')
                continue

            try:
                # Get matrix data from the parent matrix
                matrix_data = {}
                if plan.matrix_id.matrix_data:
                    if isinstance(plan.matrix_id.matrix_data, str):
                        matrix_data = json.loads(plan.matrix_id.matrix_data)
                    elif isinstance(plan.matrix_id.matrix_data, dict):
                        matrix_data = plan.matrix_id.matrix_data

                assigned_cells = []

                # Get height and width values for coordinate conversion
                height_values = []
                width_values = []

                if plan.matrix_id.height_values:
                    height_values = [h.strip() for h in plan.matrix_id.height_values.split(',') if h.strip()]
                if plan.matrix_id.width_values:
                    width_values = [w.strip() for w in plan.matrix_id.width_values.split(',') if w.strip()]

                # Iterate through matrix data safely
                for cell_key, cell_data in matrix_data.items():
                    # Ensure cell_data is a dictionary
                    if isinstance(cell_data, dict):
                        cell_cut_plan_id = cell_data.get('cut_plan_id')

                        if cell_cut_plan_id == plan.id:
                            # Convert cell key to Excel-style coordinates
                            try:
                                height, width = cell_key.split('_')
                                excel_coord = plan._convert_to_excel_coordinate(height, width, height_values, width_values)
                                assigned_cells.append(excel_coord)
                            except (ValueError, IndexError):
                                # Fallback to dimension format if conversion fails
                                height, width = cell_key.split('_')
                                assigned_cells.append(f"{height}×{width}")

                plan.assigned_cells = ", ".join(assigned_cells) if assigned_cells else "No cells assigned"
                plan.assigned_cells_count = len(assigned_cells)

                # Generate HTML tags display
                if assigned_cells:
                    tags_html = []
                    for cell in assigned_cells:
                        tags_html.append(f'<span class="badge badge-info" style="margin: 2px; padding: 4px 8px; background-color: #17a2b8; color: white; border-radius: 12px; font-size: 11px;">{cell}</span>')
                    plan.assigned_cells_display = Markup(''.join(tags_html))
                else:
                    plan.assigned_cells_display = Markup('<span style="color: #6c757d; font-style: italic;">No cells assigned</span>')

            except Exception as e:
                plan.assigned_cells = f"Error: {str(e)}"
                plan.assigned_cells_count = 0
                plan.assigned_cells_display = Markup(f'<span style="color: #dc3545;">Error: {str(e)}</span>')

    def _convert_to_excel_coordinate(self, height, width, height_values, width_values):
        """Convert height/width dimensions to Excel-style coordinates like A1, B2, etc."""
        try:
            # Find the index of height and width in their respective lists
            height_index = height_values.index(height)
            width_index = width_values.index(width)

            # Convert to Excel-style coordinates
            # Column: A, B, C, ... (width index)
            column = chr(ord('A') + width_index) if width_index < 26 else f"A{chr(ord('A') + width_index - 26)}"
            # Row: 1, 2, 3, ... (height index + 1)
            row = height_index + 1

            return f"{column}{row}"
        except (ValueError, IndexError):
            # Fallback if conversion fails
            return f"{height}×{width}"

    def action_assign_sample_cell(self):
        """Assign a sample cell to this cut plan for testing"""
        self.ensure_one()

        if not self.matrix_id:
            return

        # Get the first available height/width combination that fits in this cut plan
        height_values = []
        width_values = []

        if self.matrix_id.height_values:
            height_values = [h.strip() for h in self.matrix_id.height_values.split(',') if h.strip()]
        if self.matrix_id.width_values:
            width_values = [w.strip() for w in self.matrix_id.width_values.split(',') if w.strip()]

        # Find a suitable cell size (smaller than master sheet)
        for height in height_values:
            for width in width_values:
                try:
                    h_val = float(height)
                    w_val = float(width)
                    if h_val <= self.master_height and w_val <= self.master_width:
                        # Assign this cell to the cut plan
                        self.matrix_id.assign_cut_plan_to_cell(height, width, self.id)
                        return {
                            'type': 'ir.actions.client',
                            'tag': 'display_notification',
                            'params': {
                                'title': 'Success',
                                'message': f'Assigned cell {height}×{width}mm to this cut plan',
                                'type': 'success',
                            }
                        }
                except (ValueError, ValidationError):
                    continue

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'No Suitable Cell',
                'message': 'No matrix cells are small enough to fit in this cut plan',
                'type': 'warning',
            }
        }

    def action_remove_all_assignments(self):
        """Remove all matrix cell assignments for this cut plan"""
        self.ensure_one()

        if not self.matrix_id:
            return

        # Get current matrix data
        matrix_data = self.matrix_id.get_matrix_data_dict()
        removed_count = 0

        # Remove all assignments for this cut plan
        for cell_key, cell_data in list(matrix_data.items()):
            if isinstance(cell_data, dict) and cell_data.get('cut_plan_id') == self.id:
                height, width = cell_key.split('_')
                self.matrix_id.remove_cut_plan_from_cell(height, width)
                removed_count += 1

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Assignments Removed',
                'message': f'Removed {removed_count} cell assignments from this cut plan',
                'type': 'success',
            }
        }



    def action_view_byproducts(self):
        """View byproducts for this cut plan"""
        self.ensure_one()

        return {
            'name': f'Byproducts - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.byproduct',
            'view_mode': 'list,form',
            'domain': [('cut_plan_id', '=', self.id)],
            'context': {'default_cut_plan_id': self.id},
        }


class MeshCutByproduct(models.Model):
    _name = 'mesh.cut.byproduct'
    _description = 'Planned Off-cuts Created from Cutting'
    _order = 'width desc, height desc'
    
    name = fields.Char('Byproduct Name', compute='_compute_name', store=True)
    cut_plan_id = fields.Many2one('mesh.cut.plan', 'Cut Plan', required=True, ondelete='cascade')
    
    # Byproduct details
    product_id = fields.Many2one('product.product', 'Product',
                               help="The product that will be created for this byproduct")
    quantity = fields.Float('Quantity Created', default=1.0, required=True)
    width = fields.Float('Width (mm)', required=True)
    height = fields.Float('Height (mm)', required=True)
    
    # Computed fields
    area = fields.Float('Area (mm²)', compute='_compute_area', store=True)
    total_area = fields.Float('Total Area (mm²)', compute='_compute_total_area', store=True)
    
    @api.depends('width', 'height')
    def _compute_name(self):
        for byproduct in self:
            byproduct.name = f"{byproduct.width}x{byproduct.height}mm"
    
    @api.depends('width', 'height')
    def _compute_area(self):
        for byproduct in self:
            byproduct.area = byproduct.width * byproduct.height
    
    @api.depends('area', 'quantity')
    def _compute_total_area(self):
        for byproduct in self:
            byproduct.total_area = byproduct.area * byproduct.quantity

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to auto-create planned offcut products"""
        for vals in vals_list:
            if not vals.get('product_id') and vals.get('width') and vals.get('height'):
                # Auto-create planned offcut product
                vals['product_id'] = self._create_planned_offcut_product(vals).id

        return super().create(vals_list)

    def _create_planned_offcut_product(self, vals):
        """Create a planned offcut product for this byproduct"""
        cut_plan = self.env['mesh.cut.plan'].browse(vals['cut_plan_id'])
        width = vals['width']
        height = vals['height']

        # Get mesh series from the cut plan's matrix
        mesh_series = cut_plan.mesh_series
        cut_matrix = cut_plan.cut_matrix_id

        product_name = f"{mesh_series.title()} Mesh Off-cut {width}x{height}mm"

        # Create product with automatic code generation
        product_vals = {
            'name': product_name,
            'type': 'product',
            'is_mesh_product': True,
            'mesh_type': 'planned',
            'mesh_width': width,
            'mesh_height': height,
            'mesh_series': mesh_series,
            'cut_matrix_id': cut_matrix.id if cut_matrix else False,
            'categ_id': self.env.ref('canbrax_configmatrix.product_category_mesh_offcuts').id,
        }

        # Find the master product for this cut plan
        master_products = self.env['product.product'].search([
            ('mesh_type', '=', 'master'),
            ('mesh_width', '=', cut_plan.master_width),
            ('mesh_height', '=', cut_plan.master_height),
            ('mesh_series', '=', mesh_series),
        ], limit=1)


        product = self.env['product.product'].create(product_vals)

        return product
