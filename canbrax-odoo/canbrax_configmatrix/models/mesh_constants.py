# -*- coding: utf-8 -*-

"""
Mesh-related constants and selections
Centralized definitions to avoid duplication
"""

# Mesh Series Selection - Define once, use everywhere
MESH_SERIES_SELECTION = [
    ('saltwaterseries', 'SaltwaterSeries'),
    ('xceed', 'Xceed'),
    ('basix', 'BasiX'),
    ('diamond_7mm', '7mm Diamond Grille'),
    ('flyscreen', 'Flyscreen'),
    ('petscreen', 'Petscreen'),
    ('sandfly', 'Sandfly Mesh'),
    ('bal_insect', 'BAL Rated Insect Screen'),
    ('diamond_small', 'Small Diamond Grille'),
    ('colonial_castings', 'Colonial Castings Design'),
]

# Default mesh series
DEFAULT_MESH_SERIES = 'saltwaterseries'

# Mesh source types
MESH_SOURCE_TYPES = [
    ('unplanned', 'Unplanned Off-cut'),
    ('planned', 'Planned Off-cut'),
    ('master', 'Master Sheet'),
]
