# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class ConfigMatrixTemplateMatrixAssignment(models.Model):
    _name = 'config.matrix.template.matrix.assignment'
    _description = 'Assignment of Matrices to Configuration Templates'
    _order = 'template_id, sequence, matrix_type'

    template_id = fields.Many2one(
        'config.matrix.template', 
        "Configuration Template", 
        required=True, 
        ondelete='cascade'
    )
    
    matrix_type = fields.Selection([
        ('price', 'Price Matrix'),
        ('labor', 'Labor Time Matrix')
    ], string="Matrix Type", required=True)
    
    # Reference to price matrix
    price_matrix_id = fields.Many2one(
        'config.matrix.price.matrix',
        "Price Matrix",
        domain="[('product_template_id', '=', product_template_id)]"
    )
    
    # Reference to labor time matrix
    labor_matrix_id = fields.Many2one(
        'config.matrix.labor.time.matrix',
        "Labor Time Matrix",
        domain="['|', ('product_template_id', '=', product_template_id), ('product_template_id', '=', False)]"
    )
    
    # Helper field for domain filtering
    product_template_id = fields.Many2one(
        related='template_id.product_template_id',
        string="Product Template",
        store=True
    )
    
    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)
    
    # Display name based on matrix type
    name = fields.Char("Name", compute='_compute_name', store=True)
    
    # Dimension field mappings
    height_field_id = fields.Many2one(
        'config.matrix.field',
        "Height Field",
        domain="[('matrix_id', '=', template_id), ('field_type', '=', 'number')]",
        help="Field that provides the height dimension"
    )
    
    width_field_id = fields.Many2one(
        'config.matrix.field',
        "Width Field", 
        domain="[('matrix_id', '=', template_id), ('field_type', '=', 'number')]",
        help="Field that provides the width dimension"
    )
    
    @api.depends('matrix_type', 'price_matrix_id', 'labor_matrix_id')
    def _compute_name(self):
        for assignment in self:
            if assignment.matrix_type == 'price' and assignment.price_matrix_id:
                assignment.name = assignment.price_matrix_id.name
            elif assignment.matrix_type == 'labor' and assignment.labor_matrix_id:
                assignment.name = assignment.labor_matrix_id.name
            elif assignment.matrix_type:
                assignment.name = f"Unassigned {assignment.matrix_type.title()} Matrix"
            else:
                assignment.name = "Unassigned Matrix"
    
    @api.constrains('matrix_type', 'price_matrix_id', 'labor_matrix_id')
    def _check_matrix_assignment(self):
        for assignment in self:
            if assignment.matrix_type == 'price' and not assignment.price_matrix_id:
                raise ValidationError(_("Price matrix must be selected for price matrix type"))
            elif assignment.matrix_type == 'labor' and not assignment.labor_matrix_id:
                raise ValidationError(_("Labor matrix must be selected for labor matrix type"))
            
            # Ensure only the correct matrix is set based on type
            if assignment.matrix_type == 'price' and assignment.labor_matrix_id:
                assignment.labor_matrix_id = False
            elif assignment.matrix_type == 'labor' and assignment.price_matrix_id:
                assignment.price_matrix_id = False
    
    def get_price_for_configuration(self, configuration_values):
        """Get price from assigned price matrix based on configuration values
        
        Args:
            configuration_values: Dict of field technical names to values
            
        Returns:
            float: Price or False if not found
        """
        self.ensure_one()
        
        if self.matrix_type != 'price' or not self.price_matrix_id:
            return False
        
        # Get height and width from configuration
        height = self._get_dimension_value(configuration_values, self.height_field_id)
        width = self._get_dimension_value(configuration_values, self.width_field_id)
        
        # If height/width fields are not configured, try to auto-detect from configuration values
        if height is False:
            height = self._auto_detect_dimension(configuration_values, 'height')
        
        if width is False:
            width = self._auto_detect_dimension(configuration_values, 'width')
        
        if height is False or width is False:
            return False
        
        return self.price_matrix_id.get_price_for_dimensions(height, width)
    
    def get_labor_time_for_configuration(self, configuration_values):
        """Get labor time from assigned labor matrix based on configuration values
        
        Args:
            configuration_values: Dict of field technical names to values
            
        Returns:
            float: Labor time in hours or False if not found
        """
        self.ensure_one()
        
        if self.matrix_type != 'labor' or not self.labor_matrix_id:
            return False
        
        # Get height and width from configuration
        height = self._get_dimension_value(configuration_values, self.height_field_id)
        width = self._get_dimension_value(configuration_values, self.width_field_id)
        
        if height is False or width is False:
            return False
        
        return self.labor_matrix_id.get_labor_time_in_hours(height, width)
    
    def _get_dimension_value(self, configuration_values, field):
        """Extract dimension value from configuration
        
        Args:
            configuration_values: Dict of field technical names to values
            field: Field record to get value for
            
        Returns:
            float: Dimension value or False if not found/invalid
        """
        if not field:
            return False
        
        value = configuration_values.get(field.technical_name)
        if value is None:
            return False
        
        try:
            return float(value)
        except (ValueError, TypeError):
            return False

    def _auto_detect_dimension(self, configuration_values, dimension_type):
        """Auto-detect dimension value from configuration values
        
        Args:
            configuration_values: Dict of field technical names to values
            dimension_type: 'height' or 'width'
            
        Returns:
            float: Dimension value or False if not found
        """
        # Get all fields for this template
        template_fields = self.env['config.matrix.field'].search([
            ('matrix_id', '=', self.template_id.id),
            ('field_type', '=', 'number')
        ])
        
        for field in template_fields:
            value = configuration_values.get(field.technical_name)
            if value is not None:
                try:
                    num_value = float(value)
                    if num_value > 0:
                        # Check if field name contains the dimension type
                        field_name_lower = field.name.lower()
                        if dimension_type in field_name_lower:
                            return num_value
                except (ValueError, TypeError):
                    continue
        
        return False
