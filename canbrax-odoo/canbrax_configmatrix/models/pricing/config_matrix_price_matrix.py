# -*- coding: utf-8 -*-

import json
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class ConfigMatrixPriceMatrix(models.Model):
    _name = 'config.matrix.price.matrix'
    _description = 'Price Matrix for Configurable Products'
    _order = 'name, sequence'

    name = fields.Char("Matrix Name", required=True)
    description = fields.Text("Description")
    product_template_id = fields.Many2one(
        'product.template',
        "Product Template",
        required=True,
        help="The configurable product this matrix applies to"
    )
    matrix_id = fields.Many2one(
        'config.matrix.template',
        "Matrix",
        related='product_template_id.matrix_id',
    )
    height_calculated_field_id = fields.Many2one(
        'config.matrix.calculated.field',
        "Height Calculated Field",
        domain="[('template_ids', 'in', matrix_id), ('name', 'ilike', 'height')]",
        help="The calculated field for height"
    )
    width_calculated_field_id = fields.Many2one(
        'config.matrix.calculated.field',
        "Width Calculated Field",
        domain="[('template_ids', 'in', matrix_id), ('name', 'ilike', 'width')]",
        help="The calculated field for width"
    )

    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean("Active", default=True)

    # Matrix configuration
    matrix_type = fields.Selection([
        ('price', 'Sale Price Matrix'),
        ('cost', 'Cost Matrix')
    ], string="Matrix Type", default='price', required=True)

    # NEW: Matrix Category
    category_id = fields.Many2one(
        'config.matrix.category',
        string="Matrix Category",
        help="Categorize this matrix for better organization"
    )

    # Dimension ranges
    height_ranges = fields.Text(
        "Height Ranges",
        help="JSON array of height ranges [{'min': 345, 'max': 495, 'label': '345-495'}, ...]",
        default="[]"
    )
    width_ranges = fields.Text(
        "Width Ranges",
        help="JSON array of width ranges [{'min': 345, 'max': 495, 'label': '345-495'}, ...]",
        default="[]"
    )

    # Matrix data
    matrix_data = fields.Text(
        "Matrix Data",
        help="JSON object containing the matrix values with keys like 'height_345_width_495': 211.48"
    )

    # Currency
    currency_id = fields.Many2one(
        'res.currency',
        "Currency",
        default=lambda self: self.env.company.currency_id
    )

    # Special conditions
    special_conditions = fields.Text(
        "Special Conditions",
        help="JSON object for special conditions like mid-rail requirements, cross-brace options, etc."
    )

    # Import/Export helpers
    last_imported = fields.Datetime("Last Imported")
    import_source = fields.Char("Import Source")

    def debug_ranges(self):
        """Debug method to check range data"""
        for record in self:
            _logger.info(f"Matrix: {record.name}")
            _logger.info(f"Height ranges raw: {record.height_ranges}")
            _logger.info(f"Width ranges raw: {record.width_ranges}")
            _logger.info(f"Matrix data raw: {record.matrix_data}")

            try:
                height_ranges = json.loads(record.height_ranges) if record.height_ranges else []
                width_ranges = json.loads(record.width_ranges) if record.width_ranges else []
                matrix_data = json.loads(record.matrix_data) if record.matrix_data else {}

                _logger.info(f"Height ranges parsed: {height_ranges}")
                _logger.info(f"Width ranges parsed: {width_ranges}")
                _logger.info(f"Matrix data parsed: {matrix_data}")
            except Exception as e:
                _logger.error(f"Error parsing JSON data: {e}")

        return True

    # Statistics fields for visual editor
    total_cells = fields.Integer("Total Cells", compute='_compute_matrix_stats')
    filled_cells = fields.Integer("Filled Cells", compute='_compute_matrix_stats')
    completion_rate = fields.Float("Completion Rate", compute='_compute_matrix_stats')

    # Dimension count fields
    height_count = fields.Integer("Height Count", compute='_compute_dimension_counts')
    width_count = fields.Integer("Width Count", compute='_compute_dimension_counts')

    @api.depends('height_ranges', 'width_ranges', 'matrix_data')
    def _compute_matrix_stats(self):
        """Compute matrix statistics for the visual editor"""
        import json

        for record in self:
            try:
                height_ranges = json.loads(record.height_ranges) if record.height_ranges else []
                width_ranges = json.loads(record.width_ranges) if record.width_ranges else []
                matrix_data = json.loads(record.matrix_data) if record.matrix_data else {}

                total = len(height_ranges) * len(width_ranges)
                filled = len([v for v in matrix_data.values() if v])

                record.total_cells = total
                record.filled_cells = filled
                record.completion_rate = round((filled / total * 100) if total > 0 else 0, 1)

            except json.JSONDecodeError:
                record.total_cells = 0
                record.filled_cells = 0
                record.completion_rate = 0.0

    @api.depends('height_ranges', 'width_ranges')
    def _compute_dimension_counts(self):
        """Compute dimension counts for the configuration tab"""
        import json

        for record in self:
            try:
                height_ranges = json.loads(record.height_ranges) if record.height_ranges else []
                width_ranges = json.loads(record.width_ranges) if record.width_ranges else []

                record.height_count = len(height_ranges)
                record.width_count = len(width_ranges)

            except json.JSONDecodeError:
                record.height_count = 0
                record.width_count = 0

    @api.model
    def create_from_excel_data(self, name, product_template_id, excel_data):
        """Create a price matrix from Excel data structure

        Args:
            name: Matrix name
            product_template_id: ID of the product template
            excel_data: Dict with structure:
                {
                    'heights': [345, 495, 645, 795, ...],
                    'widths': [345, 495, 645, 795, ...],
                    'prices': {
                        '1245_795': 211.48,  # height_width: price
                        ...
                    },
                    'special_conditions': {
                        '1845_1845': 'mid_rail_required',
                        ...
                    }
                }
        """
        # Convert heights to ranges
        heights = excel_data.get('heights', [])
        height_ranges = []
        for i, height in enumerate(heights):
            if i == 0:
                min_height = height
            else:
                min_height = heights[i-1] + 1

            height_ranges.append({
                'min': min_height if i > 0 else height,
                'max': height,
                'label': str(height)
            })

        # Convert widths to ranges
        widths = excel_data.get('widths', [])
        width_ranges = []
        for i, width in enumerate(widths):
            if i == 0:
                min_width = width
            else:
                min_width = widths[i-1] + 1

            width_ranges.append({
                'min': min_width if i > 0 else width,
                'max': width,
                'label': str(width)
            })

        # Create the matrix
        matrix = self.create({
            'name': name,
            'product_template_id': product_template_id,
            'matrix_type': 'price',
            'height_ranges': json.dumps(height_ranges),
            'width_ranges': json.dumps(width_ranges),
            'matrix_data': json.dumps(excel_data.get('prices', {})),
            'special_conditions': json.dumps(excel_data.get('special_conditions', {})),
            'last_imported': fields.Datetime.now(),
        })
        return matrix

    def action_add_height_range(self):
        """Add a new height range"""
        self.ensure_one()
        import json

        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
        except json.JSONDecodeError:
            height_ranges = []

        # Determine next range values
        if height_ranges:
            last_max = max(range_data['max'] for range_data in height_ranges)
            new_min = last_max + 1
            new_max = last_max + 200  # Default 200mm increment
        else:
            new_min = 300
            new_max = 500

        # Add new range
        new_range = {
            'min': new_min,
            'max': new_max,
            'label': str(new_max)
        }
        height_ranges.append(new_range)

        # Sort by max value
        height_ranges.sort(key=lambda x: x['max'])

        self.height_ranges = json.dumps(height_ranges)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Height Range Added',
                'message': f'Added height range {new_min}-{new_max}mm',
                'type': 'success',
            }
        }

    def action_add_width_range(self):
        """Add a new width range"""
        self.ensure_one()
        import json

        try:
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
        except json.JSONDecodeError:
            width_ranges = []

        # Determine next range values
        if width_ranges:
            last_max = max(range_data['max'] for range_data in width_ranges)
            new_min = last_max + 1
            new_max = last_max + 200  # Default 200mm increment
        else:
            new_min = 300
            new_max = 500

        # Add new range
        new_range = {
            'min': new_min,
            'max': new_max,
            'label': str(new_max)
        }
        width_ranges.append(new_range)

        # Sort by max value
        width_ranges.sort(key=lambda x: x['max'])

        self.width_ranges = json.dumps(width_ranges)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Width Range Added',
                'message': f'Added width range {new_min}-{new_max}mm',
                'type': 'success',
            }
        }

    def action_generate_standard_ranges(self):
        """Generate standard height and width ranges"""
        self.ensure_one()
        import json

        # Standard height ranges (300mm to 2400mm in 300mm increments)
        height_ranges = []
        for i, max_height in enumerate([600, 900, 1200, 1500, 1800, 2100, 2400]):
            min_height = 300 if i == 0 else height_ranges[i-1]['max'] + 1
            height_ranges.append({
                'min': min_height,
                'max': max_height,
                'label': str(max_height)
            })

        # Standard width ranges (300mm to 2400mm in 300mm increments)
        width_ranges = []
        for i, max_width in enumerate([600, 900, 1200, 1500, 1800, 2100, 2400]):
            min_width = 300 if i == 0 else width_ranges[i-1]['max'] + 1
            width_ranges.append({
                'min': min_width,
                'max': max_width,
                'label': str(max_width)
            })

        self.height_ranges = json.dumps(height_ranges)
        self.width_ranges = json.dumps(width_ranges)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Standard Ranges Generated',
                'message': f'Generated {len(height_ranges)} height ranges and {len(width_ranges)} width ranges',
                'type': 'success',
            }
        }

    def action_fill_sample_data(self):
        """Fill matrix with sample data for testing"""
        self.ensure_one()
        import json

        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
        except json.JSONDecodeError:
            raise UserError(_("Invalid height or width ranges format"))

        if not height_ranges or not width_ranges:
            raise UserError(_("Please set height and width ranges first in the Matrix Configuration tab"))

        # Generate sample prices
        sample_data = {}
        base_price = 150.0

        for i, h_range in enumerate(height_ranges):
            for j, w_range in enumerate(width_ranges):
                # Price increases with size
                height_val = h_range['max']
                width_val = w_range['max']
                price = base_price + (i * 25) + (j * 20) + (height_val * width_val * 0.0001)
                matrix_key = f"{h_range['label']}_{w_range['label']}"
                sample_data[matrix_key] = round(price, 2)

        self.matrix_data = json.dumps(sample_data)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Sample Data Generated'),
                'message': f'Generated {len(sample_data)} sample data points.',
                'type': 'success',
            }
        }

    def action_clear_matrix_data(self):
        """Clear all matrix data"""
        self.ensure_one()

        self.matrix_data = "{}"

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Matrix Cleared'),
                'message': 'All matrix data has been cleared.',
                'type': 'warning',
            }
        }

    def action_export_to_csv(self):
        """Export matrix to CSV and download"""
        self.ensure_one()

        csv_content = self.export_to_csv()

        # Create attachment for download
        import base64
        attachment = self.env['ir.attachment'].create({
            'name': f'{self.name}_matrix.csv',
            'type': 'binary',
            'datas': base64.b64encode(csv_content.encode('utf-8')),
            'res_model': self._name,
            'res_id': self.id,
            'mimetype': 'text/csv',
        })

        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'new',
        }

    def action_open_matrix_editor(self):
        """Open the visual matrix editor for this matrix"""
        self.ensure_one()

        return {
            'name': _('Visual Matrix Editor - %s') % self.name,
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.visual.editor',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_matrix_id': self.id,
                'default_matrix_type': 'price',
            },
        }
    def get_price_for_dimensions(self, height, width):
        """Get the sale price for given dimensions

        Args:
            height: Height in mm
            width: Width in mm

        Returns:
            float: Sale price or False if not found
        """
        self.ensure_one()

        import json

        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
            matrix_data = json.loads(self.matrix_data) if self.matrix_data else {}
        except json.JSONDecodeError as e:
            _logger.error(f"Invalid JSON in price matrix {self.id}")
            _logger.error(f"Error: {e}")
            return False

        # Find matching height range
        height_key = None
        for h_range in height_ranges:
            if h_range['min'] <= height <= h_range['max']:
                height_key = h_range['label']
                break

        # Find matching width range
        width_key = None
        for w_range in width_ranges:
            if w_range['min'] <= width <= w_range['max']:
                width_key = w_range['label']
                break

        if not height_key or not width_key:
            return False

        # Look up price in matrix data
        matrix_key = f"{height_key}_{width_key}"
        price = matrix_data.get(matrix_key)

        if price:
            return float(price)

        return False

    def get_special_conditions(self, height, width):
        """Get special conditions for given dimensions

        Args:
            height: Height in mm
            width: Width in mm

        Returns:
            list: List of special condition codes
        """
        self.ensure_one()

        import json

        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
            special_conditions = json.loads(self.special_conditions) if self.special_conditions else {}
        except json.JSONDecodeError:
            return []

        # Find matching height range
        height_key = None
        for h_range in height_ranges:
            if h_range['min'] <= height <= h_range['max']:
                height_key = h_range['label']
                break

        # Find matching width range
        width_key = None
        for w_range in width_ranges:
            if w_range['min'] <= width <= w_range['max']:
                width_key = w_range['label']
                break

        if not height_key or not width_key:
            return []

        # Look up conditions
        matrix_key = f"{height_key}_{width_key}"
        conditions = special_conditions.get(matrix_key, [])

        if isinstance(conditions, str):
            conditions = [conditions]

        return conditions

    def export_to_csv(self):
        """Export matrix to CSV format"""
        self.ensure_one()

        import json
        import csv
        import io

        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
            matrix_data = json.loads(self.matrix_data) if self.matrix_data else {}
        except json.JSONDecodeError:
            raise UserError(_("Invalid matrix data format"))

        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)

        # Header row
        header = ['Height\\Width'] + [w_range['label'] for w_range in width_ranges]
        writer.writerow(header)

        # Data rows
        for h_range in height_ranges:
            row = [h_range['label']]
            for w_range in width_ranges:
                matrix_key = f"{h_range['label']}_{w_range['label']}"
                price = matrix_data.get(matrix_key, '')
                row.append(price)
            writer.writerow(row)

        csv_content = output.getvalue()
        output.close()

        return csv_content

    @api.model
    def import_from_csv(self, name, product_template_id, csv_content):
        """Import matrix from CSV content

        Args:
            name: Matrix name
            product_template_id: Product template ID
            csv_content: CSV content as string
        """
        import csv
        import io

        reader = csv.reader(io.StringIO(csv_content))
        rows = list(reader)

        if len(rows) < 2:
            raise UserError(_("CSV must have at least header and one data row"))

        # Parse header
        header = rows[0]
        if len(header) < 2:
            raise UserError(_("CSV must have at least height and width columns"))

        width_labels = header[1:]  # Skip first column (Height\Width)

        # Parse data rows
        height_labels = []
        matrix_data = {}

        for row in rows[1:]:
            if len(row) < 2:
                continue

            height_label = row[0]
            height_labels.append(height_label)

            for i, price_str in enumerate(row[1:]):
                if price_str and price_str.strip():
                    try:
                        price = float(price_str.strip())
                        width_label = width_labels[i]
                        matrix_key = f"{height_label}_{width_label}"
                        matrix_data[matrix_key] = price
                    except ValueError:
                        continue

        # Create ranges (assuming labels are the max values)
        height_ranges = []
        for i, label in enumerate(height_labels):
            try:
                max_val = int(label)
                min_val = int(height_labels[i-1]) + 1 if i > 0 else max_val
                height_ranges.append({
                    'min': min_val,
                    'max': max_val,
                    'label': label
                })
            except ValueError:
                continue

        width_ranges = []
        for i, label in enumerate(width_labels):
            try:
                max_val = int(label)
                min_val = int(width_labels[i-1]) + 1 if i > 0 else max_val
                width_ranges.append({
                    'min': min_val,
                    'max': max_val,
                    'label': label
                })
            except ValueError:
                continue

        # Create matrix
        matrix = self.create({
            'name': name,
            'product_template_id': product_template_id,
            'matrix_type': 'price',
            'height_ranges': json.dumps(height_ranges),
            'width_ranges': json.dumps(width_ranges),
            'matrix_data': json.dumps(matrix_data),
            'last_imported': fields.Datetime.now(),
            'import_source': 'csv'
        })

        return matrix


class ConfigMatrixPriceMatrixCell(models.Model):
    _name = 'config.matrix.price.matrix.cell'
    _description = 'Price Matrix Cell'
    _order = 'matrix_id, height_min, width_min'

    matrix_id = fields.Many2one('config.matrix.price.matrix', "Price Matrix", required=True, ondelete='cascade')

    # Dimension ranges for this cell
    height_min = fields.Integer("Height Min", required=True)
    height_max = fields.Integer("Height Max", required=True)
    width_min = fields.Integer("Width Min", required=True)
    width_max = fields.Integer("Width Max", required=True)

    # Value
    price = fields.Float("Sale Price", digits='Product Price')

    # Special conditions
    condition_codes = fields.Char("Condition Codes", help="Comma-separated condition codes")
    notes = fields.Text("Notes")

    # Display helpers
    height_range_display = fields.Char("Height Range", compute='_compute_range_display', store=True)
    width_range_display = fields.Char("Width Range", compute='_compute_range_display', store=True)

    @api.depends('height_min', 'height_max', 'width_min', 'width_max')
    def _compute_range_display(self):
        for cell in self:
            if cell.height_min == cell.height_max:
                cell.height_range_display = str(cell.height_max)
            else:
                cell.height_range_display = f"{cell.height_min}-{cell.height_max}"

            if cell.width_min == cell.width_max:
                cell.width_range_display = str(cell.width_max)
            else:
                cell.width_range_display = f"{cell.width_min}-{cell.width_max}"

    def matches_dimensions(self, height, width):
        """Check if this cell matches the given dimensions"""
        return (self.height_min <= height <= self.height_max and
                self.width_min <= width <= self.width_max)
