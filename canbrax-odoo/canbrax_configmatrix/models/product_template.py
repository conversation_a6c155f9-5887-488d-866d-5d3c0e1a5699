# -*- coding: utf-8 -*-

from odoo import models, fields, api, _

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    is_configurable = fields.<PERSON><PERSON>an("Configurable Product",
                                    help="This product can be configured with ConfigMatrix")
    matrix_id = fields.Many2one('config.matrix.template', "Configuration Matrix", ondelete='set null')

    configuration_count = fields.Integer("Configurations", compute='_compute_configuration_count')

    @api.depends('matrix_id')
    def _compute_configuration_count(self):
        for product in self:
            if product.matrix_id:
                product.configuration_count = self.env['config.matrix.configuration'].search_count([
                    ('template_id', '=', product.matrix_id.id)
                ])
            else:
                product.configuration_count = 0

    def action_view_configurations(self):
        """View configurations for this product"""
        self.ensure_one()

        if not self.matrix_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Configuration Matrix'),
                    'message': _('This product does not have a configuration matrix.'),
                    'type': 'warning',
                }
            }

        return {
            'name': _('Configurations'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'list,form',
            'domain': [('template_id', '=', self.matrix_id.id)],
            'context': {'default_template_id': self.matrix_id.id},
        }

    def action_create_matrix(self):
        """Create a new configuration matrix for this product"""
        self.ensure_one()

        if self.matrix_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Matrix Exists'),
                    'message': _('This product already has a configuration matrix.'),
                    'type': 'warning',
                }
            }

        # Create new matrix
        matrix = self.env['config.matrix.template'].create({
            'name': f"{self.name} Configuration",
            'product_template_id': self.id,
            'state': 'draft',
        })

        # Link matrix to product
        self.write({
            'is_configurable': True,
            'matrix_id': matrix.id,
        })

        # Open the matrix
        return {
            'name': _('Configuration Matrix'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.template',
            'view_mode': 'form',
            'res_id': matrix.id,
        }


