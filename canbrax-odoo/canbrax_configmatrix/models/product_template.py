# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from .mesh_constants import MESH_SERIES_SELECTION

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    is_configurable = fields.Boolean("Configurable Product",
                                    help="This product can be configured with ConfigMatrix")
    matrix_id = fields.Many2one('config.matrix.template', "Configuration Matrix", ondelete='set null')

    configuration_count = fields.Integer("Configurations", compute='_compute_configuration_count')

    # Mesh-specific fields
    is_mesh_product = fields.Boolean('Is Mesh Product',
                                   help="This product is a mesh that can be cut and tracked")
    mesh_type = fields.Selection([
        ('master', 'Master Sheet'),
        ('planned', 'Planned Off-cut'),
        ('unplanned', 'Unplanned Off-cut'),
    ], string='Mesh Type', help="Type of mesh product for inventory management")

    mesh_width = fields.Float('Mesh Width (mm)', help="Width of the mesh in millimeters")
    mesh_height = fields.Float('Mesh Height (mm)', help="Height of the mesh in millimeters")
    mesh_series = fields.Selection(
        MESH_SERIES_SELECTION,
        string='Mesh Series',
        help="Series/type of mesh material"
    )



    # Cut matrix relationship (replaces mesh_series for better integration)
    cut_matrix_id = fields.Many2one('mesh.cut.matrix', 'Associated Cut Matrix',
                                  help="The cutting matrix this mesh product belongs to")

    # Computed field for mesh series (derived from cut matrix)
    mesh_series_computed = fields.Char('Mesh Series', compute='_compute_mesh_series_from_matrix', store=True)

    @api.depends('cut_matrix_id.mesh_series')
    def _compute_mesh_series_from_matrix(self):
        """Compute mesh series from associated cut matrix"""
        for product in self:
            if product.cut_matrix_id:
                product.mesh_series_computed = product.cut_matrix_id.mesh_series
            else:
                product.mesh_series_computed = product.mesh_series or False

    @api.model_create_multi
    def create(self, vals_list):
        """Override create to auto-generate codes and set tracking for mesh products"""
        for vals in vals_list:
            if vals.get('is_mesh_product'):
                # Auto-generate code if not provided
                if not vals.get('default_code'):
                    vals['default_code'] = self._generate_mesh_product_code(vals)

                # Auto-set type to goods (stockable) if not specified
                if not vals.get('type'):
                    vals['type'] = 'consu'

                # Auto-set tracking based on mesh type
                if not vals.get('tracking'):
                    mesh_type = vals.get('mesh_type')
                    if mesh_type == 'unplanned':
                        vals['tracking'] = 'lot'
                    elif mesh_type in ('master', 'planned'):
                        vals['tracking'] = 'none'

        return super().create(vals_list)

    def write(self, vals):
        """Override write to update codes and tracking when mesh properties change"""
        result = super().write(vals)

        # Handle mesh property changes
        mesh_fields = ['mesh_type', 'mesh_width', 'mesh_height', 'mesh_series', 'cut_matrix_id']
        if any(field in vals for field in mesh_fields):
            for product in self:
                if product.is_mesh_product:
                    updates = {}

                    # Regenerate code if no manual code set
                    if not vals.get('default_code'):
                        new_code = product._generate_mesh_product_code()
                        if new_code != product.default_code:
                            updates['default_code'] = new_code

                    # Update tracking if mesh_type changed and no manual tracking set
                    if 'mesh_type' in vals and not vals.get('tracking'):
                        if product.mesh_type == 'unplanned':
                            updates['tracking'] = 'lot'
                        elif product.mesh_type in ('master', 'planned'):
                            updates['tracking'] = 'none'

                    # Apply updates if any
                    if updates:
                        super(ProductTemplate, product).write(updates)

        return result

    def _generate_mesh_product_code(self, vals=None):
        """Generate product code based on mesh type and properties"""
        if vals:
            # Creating new product
            mesh_type = vals.get('mesh_type')
            mesh_width = vals.get('mesh_width', 0)
            mesh_height = vals.get('mesh_height', 0)
            mesh_series = vals.get('mesh_series')
            cut_matrix_id = vals.get('cut_matrix_id')
        else:
            # Updating existing product
            mesh_type = self.mesh_type
            mesh_width = self.mesh_width or 0
            mesh_height = self.mesh_height or 0
            mesh_series = self.mesh_series
            cut_matrix_id = self.cut_matrix_id.id if self.cut_matrix_id else None

        # Get series prefix from cut matrix if available
        series_prefix = self._get_series_prefix(mesh_series, cut_matrix_id)

        if mesh_type == 'master':
            # Master sheets: SWMMS1100620 (SW + MMS + dimensions)
            return f"{series_prefix}MMS{int(mesh_width)}{int(mesh_height)}"

        elif mesh_type == 'planned':
            # Planned offcuts: BX900X1200PO (series prefix + dimensions + PO)
            return f"{series_prefix}{int(mesh_width)}X{int(mesh_height)}PO"

        elif mesh_type == 'unplanned':
            # Unplanned offcuts: Similar to planned but with UO suffix
            return f"{series_prefix}{int(mesh_width)}X{int(mesh_height)}UO"

        # Fallback for unknown types
        return f"MESH{int(mesh_width)}X{int(mesh_height)}"

    def _get_series_prefix(self, mesh_series, cut_matrix_id=None):
        """Get the series prefix for product codes"""
        # Try to get from cut matrix first
        if cut_matrix_id:
            cut_matrix = self.env['mesh.cut.matrix'].browse(cut_matrix_id)
            if cut_matrix.exists():
                mesh_series = cut_matrix.mesh_series

        # Map series to prefixes based on your examples
        series_prefixes = {
            'saltwaterseries': 'SW',
            'xceed': 'XC',
            'basix': 'BX',
            'diamond_7mm': 'D7',
            'flyscreen': 'FS',
            'petscreen': 'PS',
            'sandfly': 'SF',
            'bal_insect': 'BI',
            'diamond_small': 'DS',
            'colonial_castings': 'CC',
        }

        return series_prefixes.get(mesh_series, 'SW')  # Default to SW

    def action_regenerate_mesh_code(self):
        """Regenerate the product code for mesh products"""
        self.ensure_one()

        if not self.is_mesh_product:
            raise UserError(_("This action is only available for mesh products."))

        # Generate the proper mesh code
        new_code = self._generate_mesh_product_code()

        if not new_code:
            raise UserError(_("Could not generate product code. Please ensure all mesh properties are set."))

        try:
            self.write({'default_code': new_code})

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Code Generated'),
                    'message': _('Product code has been set to: %s') % new_code,
                    'type': 'success',
                }
            }
        except Exception as e:
            raise UserError(_("Failed to update product code: %s") % str(e))

    @api.model
    def update_mesh_product_tracking(self):
        """Utility method to update tracking for existing mesh products"""
        mesh_products = self.search([('is_mesh_product', '=', True)])

        updated_count = 0
        for product in mesh_products:
            correct_tracking = None
            if product.mesh_type == 'unplanned':
                correct_tracking = 'lot'
            elif product.mesh_type in ('master', 'planned'):
                correct_tracking = 'none'

            if correct_tracking and product.tracking != correct_tracking:
                product.write({'tracking': correct_tracking})
                updated_count += 1

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Tracking Updated'),
                'message': _('Updated tracking for %d mesh products') % updated_count,
                'type': 'success',
            }
        }



    @api.depends('matrix_id')
    def _compute_configuration_count(self):
        for product in self:
            if product.matrix_id:
                product.configuration_count = self.env['config.matrix.configuration'].search_count([
                    ('template_id', '=', product.matrix_id.id)
                ])
            else:
                product.configuration_count = 0

    @api.onchange('categ_id')
    def _onchange_category(self):
        """Auto-set mesh product flag when category is Mesh"""
        if self.categ_id and self.categ_id.name == 'Mesh':
            self.is_mesh_product = True

    @api.onchange('is_mesh_product', 'mesh_type')
    def _onchange_mesh_product_tracking(self):
        """Auto-set tracking and type for mesh products based on type"""
        if self.is_mesh_product:
            # Set as goods (stockable when tracking is enabled)
            if not self.type or self.type == 'service':
                self.type = 'consu'

            if self.mesh_type == 'unplanned':
                # Unplanned off-cuts use lot/serial tracking for dimension storage
                self.tracking = 'lot'
            elif self.mesh_type in ('master', 'planned'):
                # Master sheets and planned off-cuts are regular products
                self.tracking = 'none'
        else:
            # Non-mesh products keep their current tracking setting
            pass

    def action_view_configurations(self):
        """View configurations for this product"""
        self.ensure_one()

        if not self.matrix_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Configuration Matrix'),
                    'message': _('This product does not have a configuration matrix.'),
                    'type': 'warning',
                }
            }

        return {
            'name': _('Configurations'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'list,form',
            'domain': [('template_id', '=', self.matrix_id.id)],
            'context': {'default_template_id': self.matrix_id.id},
        }

    def action_create_matrix(self):
        """Create a new configuration matrix for this product"""
        self.ensure_one()

        if self.matrix_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Matrix Exists'),
                    'message': _('This product already has a configuration matrix.'),
                    'type': 'warning',
                }
            }

        # Create new matrix
        matrix = self.env['config.matrix.template'].create({
            'name': f"{self.name} Configuration",
            'product_template_id': self.id,
            'state': 'draft',
        })

        # Link matrix to product
        self.write({
            'is_configurable': True,
            'matrix_id': matrix.id,
        })

        # Open the matrix
        return {
            'name': _('Configuration Matrix'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.template',
            'view_mode': 'form',
            'res_id': matrix.id,
        }

    def action_view_mesh_operations(self):
        """View mesh operations - Cut Plans for master sheets, Cut Operations for off-cuts"""
        self.ensure_one()

        if self.mesh_type == 'master':
            # For master sheets, show cut plans that match this product's dimensions
            cut_matrix = self.env['mesh.cut.matrix'].search([
                ('mesh_series', '=', self.mesh_series)
            ], limit=1)

            if cut_matrix:
                # Find cut plans for this master sheet size
                domain = [
                    ('matrix_id', '=', cut_matrix.id),
                    ('cut_width', '=', self.mesh_width),
                    ('cut_height', '=', self.mesh_height),
                ]
            else:
                # No cut matrix found, show empty domain
                domain = [('id', '=', False)]

            return {
                'name': _('Cut Plans'),
                'type': 'ir.actions.act_window',
                'res_model': 'mesh.cut.plan',
                'view_mode': 'list,form',
                'domain': domain,
                'context': {
                    'default_cut_width': self.mesh_width,
                    'default_cut_height': self.mesh_height,
                    'default_matrix_id': cut_matrix.id if cut_matrix else False,
                },
            }
        else:
            # For off-cuts, show regular cut operations
            return {
                'name': _('Cut Operations'),
                'type': 'ir.actions.act_window',
                'res_model': 'mesh.cut.operation',
                'view_mode': 'list,form',
                'domain': [('source_product_id', '=', self.id)],
                'context': {'default_source_product_id': self.id},
            }


class ProductProduct(models.Model):
    _inherit = 'product.product'

    def get_available_mesh_stock(self, required_width, required_height):
        """Get available mesh stock that can satisfy the requirement

        Args:
            required_width (float): Required width in mm
            required_height (float): Required height in mm

        Returns:
            list: Available stock options with details
        """
        stock_quants = self.env['stock.quant'].search([
            ('product_id', '=', self.id),
            ('quantity', '>', 0)
        ])

        available = []
        for quant in stock_quants:
            # For unplanned off-cuts, check lot/serial dimensions
            if self.mesh_type == 'unplanned' and quant.lot_id:
                lot_width = quant.lot_id.mesh_width
                lot_height = quant.lot_id.mesh_height
                if lot_width >= required_width and lot_height >= required_height:
                    available.append({
                        'quant': quant,
                        'width': lot_width,
                        'height': lot_height,
                        'type': 'unplanned',
                        'waste_width': lot_width - required_width,
                        'waste_height': lot_height - required_height,
                    })
            # For planned off-cuts and masters, use product dimensions
            elif self.mesh_width >= required_width and self.mesh_height >= required_height:
                available.append({
                    'quant': quant,
                    'width': self.mesh_width,
                    'height': self.mesh_height,
                    'type': self.mesh_type,
                    'waste_width': self.mesh_width - required_width,
                    'waste_height': self.mesh_height - required_height,
                })

        return available
