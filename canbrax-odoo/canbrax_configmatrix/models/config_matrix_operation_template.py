# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class ConfigMatrixOperationTemplate(models.Model):
    _name = 'config.matrix.operation.template'
    _description = 'Operation Template for Configuration Matrix'
    _order = 'sequence, name'

    name = fields.Char('Operation Name', required=True, help='Name of the operation')
    sequence = fields.Integer('Sequence', default=10, help='Order of operations')
    active = fields.<PERSON><PERSON>an('Active', default=True)
    
    # Configuration Template Link
    config_template_id = fields.Many2one('config.matrix.template', 'Configuration Template',
                                        help='Configuration template this operation belongs to. '
                                             'Required for accessing field values in formulas.')

    # Work Center
    workcenter_id = fields.Many2one('mrp.workcenter', 'Work Center', required=False,
                                   help='Work center where this operation is performed')
    
    # Duration and Cost Settings
    default_duration = fields.Float('Default Duration (minutes)', default=60.0,
                                   help='Default duration in minutes if no formula is provided')

    # LEGACY: This field currently contains cost formulas but should contain duration formulas
    # We'll migrate this to cost_formula and create a new duration_formula
    duration_formula = fields.Char('Duration Formula (for BOM)',
                                  help='Python expression to calculate duration in minutes for BOM operations. '
                                       'Leave empty to use default duration.')
    duration_minutes = fields.Float('Duration (minutes)', compute='_compute_duration_minutes', store=True,
                                   help='Calculated duration in minutes for BOM operations')
    # Operation timing details
    setup_time = fields.Float("Setup Time (minutes)", default=0.0, help="Setup time in minutes")
    cleanup_time = fields.Float("Cleanup Time (minutes)", default=0.0, help="Cleanup time in minutes")
    

    # Cost calculation (separate from duration)
    cost_formula = fields.Char('Cost Formula (for Pricing)',
                              help='Python expression to calculate cost for pricing. '
                                   'This is separate from duration and used for cost estimation.')
    cost_amount = fields.Float('Cost', compute='_compute_cost_amount', store=True,
                              help='Calculated cost for pricing purposes')
    
    # Operation Details
    description = fields.Text('Description', help='Detailed description of the operation')
    
    # Worksheet Settings
    worksheet_type = fields.Selection([
        ('text', 'Text'),
        ('pdf', 'PDF'),
        ('google_slide', 'Google Slide')
    ], string='Worksheet Type', default='text')
    
    worksheet_content = fields.Text('Worksheet Content',
                                   help='Content or instructions for the worksheet')
    

    
    # Notes and Instructions
    notes = fields.Text('Notes', help='Additional notes or instructions for this operation')

    # Matrix Integration (Labor and Pricing)
    matrix_type = fields.Selection([
        ('fixed', 'Fixed Cost (No Matrix)'),
        ('labor', 'Variable - Labor Time Matrix'),
        ('price', 'Variable - Price Matrix')
    ], string='Matrix Type', help='Type of calculation: Fixed uses formula only, Variable uses matrix lookup')

    labor_matrix_id = fields.Many2one('config.matrix.labor.time.matrix', 'Labor Matrix',
                                     help='Labor time matrix for this operation')
    price_matrix_id = fields.Many2one('config.matrix.price.matrix', 'Price Matrix',
                                     help='Price matrix for this operation')

    # Dimension Field Mappings for Matrix Lookup
    x_axis_field_type = fields.Selection([
        ('calculated', 'Calculated Field'),
        ('question', 'Field Question')
    ], string='X-Axis Field Type', default='calculated', help='Type of field to use for X-axis dimension')

    y_axis_field_type = fields.Selection([
        ('calculated', 'Calculated Field'),
        ('question', 'Field Question')
    ], string='Y-Axis Field Type', default='calculated', help='Type of field to use for Y-axis dimension')

    x_axis_calculated_field_id = fields.Many2one(
        'config.matrix.calculated.field',
        "X-Axis Calculated Field",
        help="Calculated field that provides the X-axis dimension for matrix lookup"
    )
    y_axis_calculated_field_id = fields.Many2one(
        'config.matrix.calculated.field',
        "Y-Axis Calculated Field",
        help="Calculated field that provides the Y-axis dimension for matrix lookup"
    )

    x_axis_question_field_id = fields.Many2one(
        'config.matrix.field',
        "X-Axis Question Field",
        help="Question field that provides the X-axis dimension for matrix lookup",
        domain="[('field_type', '=', 'number')]"
    )
    y_axis_question_field_id = fields.Many2one(
        'config.matrix.field',
        "Y-Axis Question Field",
        help="Question field that provides the Y-axis dimension for matrix lookup",
        domain="[('field_type', '=', 'number')]"
    )

    # Test Fields for Formula Testing
    test_height = fields.Integer('Test Height (mm)', default=2100,
                                help='Sample height value for testing formulas')
    test_width = fields.Integer('Test Width (mm)', default=860,
                               help='Sample width value for testing formulas')
    test_duration_result = fields.Float('Test Duration Result (minutes)', compute='_compute_test_result',
                                       digits=(16, 4), help='Calculated duration using test values')
    test_cost_result = fields.Float('Test Cost Result', compute='_compute_test_result',
                                   digits=(16, 4), help='Calculated cost using test values')
    test_labor_time = fields.Float('Test Labor Time', compute='_compute_test_result',
                                  digits=(16, 3), help='Labor time from matrix using test values')

    # Usage tracking
    usage_count = fields.Integer('Usage Count', compute='_compute_usage_count', store=True,
                                help='Number of times this template has been used')
    
    # Category for organization
    category = fields.Selection([
        ('cutting', 'Cutting'),
        ('assembly', 'Assembly'),
        ('finishing', 'Finishing'),
        ('quality', 'Quality Control'),
        ('packaging', 'Packaging'),
        ('other', 'Other')
    ], string='Category', default='other', help='Category for organizing operations')

    # Mesh operation links
    mesh_cut_operation_ids = fields.One2many(
        'mesh.cut.operation',
        'operation_template_id',
        string='Created Mesh Cut Operations',
        help='Mesh cut operations created from this template'
    )
    mesh_cut_operation_count = fields.Integer(
        'Mesh Cut Operations Count',
        compute='_compute_mesh_cut_operation_count',
        help='Number of mesh cut operations created from this template'
    )
    
    @api.depends('name', 'workcenter_id')
    def _compute_display_name(self):
        for template in self:
            if template.workcenter_id:
                template.display_name = f"{template.name} ({template.workcenter_id.name})"
            else:
                template.display_name = template.name

    @api.depends('mesh_cut_operation_ids')
    def _compute_mesh_cut_operation_count(self):
        for template in self:
            template.mesh_cut_operation_count = len(template.mesh_cut_operation_ids)

    def action_view_mesh_cut_operations(self):
        """Open the mesh cut operations created from this template"""
        self.ensure_one()

        action = {
            'name': f'Mesh Cut Operations - {self.name}',
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.operation',
            'view_mode': 'list,form',
            'domain': [('operation_template_id', '=', self.id)],
            'context': {
                'default_operation_template_id': self.id,
                'search_default_group_by_state': 1,
            },
        }

        if self.mesh_cut_operation_count == 1:
            action['view_mode'] = 'form'
            action['res_id'] = self.mesh_cut_operation_ids[0].id
            action['target'] = 'current'

        return action
    
    @api.depends()
    def _compute_usage_count(self):
        """Compute how many times this template is used"""
        for template in self:
            # Count field operation mappings
            field_count = self.env['config.matrix.field.operation.mapping'].search_count([
                ('operation_template_id', '=', template.id)
            ])
            # Count option operation mappings
            option_count = self.env['config.matrix.option.operation.mapping'].search_count([
                ('operation_template_id', '=', template.id)
            ])
            template.usage_count = field_count + option_count

    @api.depends('duration_formula', 'default_duration', 'labor_matrix_id', 'test_height', 'test_width')
    def _compute_duration_minutes(self):
        """Compute the duration in minutes using formula or default"""
        for template in self:
            if template.duration_formula:
                # Use test values for calculation if available
                test_config = {
                    'height': template.test_height or 2100,
                    'width': template.test_width or 860,
                    'door_height': template.test_height or 2100,
                    'door_width': template.test_width or 860,
                    'area': (template.test_height or 2100) * (template.test_width or 860),
                }
                template.duration_minutes = template.get_calculated_duration(test_config)
            else:
                template.duration_minutes = template.default_duration

    @api.depends('cost_formula', 'test_height', 'test_width')
    def _compute_cost_amount(self):
        """Compute the cost using formula"""
        for template in self:
            if template.cost_formula:
                # Use test values for calculation if available
                test_config = {
                    'height': template.test_height or 2100,
                    'width': template.test_width or 860,
                    'door_height': template.test_height or 2100,
                    'door_width': template.test_width or 860,
                    'area': (template.test_height or 2100) * (template.test_width or 860),
                }
                template.cost_amount = template.get_calculated_cost(test_config)
            else:
                template.cost_amount = 0.0

    @api.depends('test_height', 'test_width', 'duration_formula', 'cost_formula', 'labor_matrix_id', 'price_matrix_id', 'matrix_type')
    def _compute_test_result(self):
        """Compute test results using test height and width values"""
        for template in self:
            if template.test_height and template.test_width:
                # Create test config values
                test_config = {
                    'height': template.test_height,
                    'width': template.test_width,
                    'door_height': template.test_height,
                    'door_width': template.test_width,
                    'area': template.test_height * template.test_width,
                }

                # Calculate test results
                template.test_duration_result = template.get_calculated_duration(test_config)
                template.test_cost_result = template.get_calculated_cost(test_config)

                # Get matrix value based on matrix type
                if template.matrix_type == 'labor' and template.labor_matrix_id:
                    labor_time = template.labor_matrix_id.get_labor_time_for_dimensions(
                        template.test_height, template.test_width
                    )
                    template.test_labor_time = labor_time if labor_time is not False else 0.0
                elif template.matrix_type == 'price' and template.price_matrix_id:
                    # For price matrices, we'll show the price value in the labor time field for testing
                    price_value = template.price_matrix_id.get_price_for_dimensions(
                        template.test_height, template.test_width
                    )
                    template.test_labor_time = price_value if price_value is not False else 0.0
                else:
                    template.test_labor_time = 0.0
            else:
                template.test_duration_result = 0.0
                template.test_cost_result = 0.0
                template.test_labor_time = 0.0
    
    @api.constrains('default_duration')
    def _check_default_duration(self):
        for template in self:
            if template.default_duration <= 0:
                raise ValidationError(_("Default duration must be greater than 0"))
    




    @api.onchange('matrix_type')
    def _onchange_matrix_type(self):
        """Clear matrix selections when type changes"""
        if self.matrix_type == 'fixed':
            # Clear both matrices for fixed operations
            self.labor_matrix_id = False
            self.price_matrix_id = False
        elif self.matrix_type == 'labor':
            # Clear price matrix when switching to labor
            self.price_matrix_id = False
        elif self.matrix_type == 'price':
            # Clear labor matrix when switching to price
            self.labor_matrix_id = False

    @api.onchange('x_axis_field_type')
    def _onchange_x_axis_field_type(self):
        """Clear field selections when type changes"""
        if self.x_axis_field_type == 'calculated':
            self.x_axis_question_field_id = False
        elif self.x_axis_field_type == 'question':
            self.x_axis_calculated_field_id = False

    @api.onchange('y_axis_field_type')
    def _onchange_y_axis_field_type(self):
        """Clear field selections when type changes"""
        if self.y_axis_field_type == 'calculated':
            self.y_axis_question_field_id = False
        elif self.y_axis_field_type == 'question':
            self.y_axis_calculated_field_id = False

    def get_calculated_duration(self, config_values=None):
        """
        Calculate the duration in minutes for this operation template based on configuration values

        Args:
            config_values (dict): Configuration values for formula evaluation

        Returns:
            float: Calculated duration in minutes
        """
        self.ensure_one()

        if not self.duration_formula:
            return self.default_duration

        if not config_values:
            config_values = {}

        try:
            # Create safe evaluation context
            from odoo.tools.safe_eval import safe_eval
            import math

            ctx = dict(config_values)

            # Add math functions
            math_context = {
                'round': round,
                'ceil': math.ceil,
                'floor': math.floor,
                'abs': abs,
                'max': max,
                'min': min,
                'sum': sum
            }
            ctx.update(math_context)

            # Add enhanced calculation functions
            enhanced_context = self._get_enhanced_formula_context(config_values)
            ctx.update(enhanced_context)

            # Add configuration access functions
            config_access_context = self._get_config_access_context(config_values)
            ctx.update(config_access_context)

            # Evaluate formula
            duration = safe_eval(self.duration_formula, ctx)
            return float(duration)
        except Exception as e:
            _logger.error(f"Error evaluating duration formula for operation template {self.name}: {str(e)}")
            return self.default_duration

    def get_calculated_cost(self, config_values=None):
        """
        Calculate the cost for this operation template based on configuration values

        Args:
            config_values (dict): Configuration values for formula evaluation

        Returns:
            float: Calculated cost
        """
        self.ensure_one()

        if not self.cost_formula:
            return 0.0

        if not config_values:
            config_values = {}

        try:
            # Create safe evaluation context
            from odoo.tools.safe_eval import safe_eval
            import math

            ctx = dict(config_values)

            # Add math functions
            math_context = {
                'round': round,
                'ceil': math.ceil,
                'floor': math.floor,
                'abs': abs,
                'max': max,
                'min': min,
                'sum': sum
            }
            ctx.update(math_context)

            # Add enhanced calculation functions
            enhanced_context = self._get_enhanced_formula_context(config_values)
            ctx.update(enhanced_context)

            # Add configuration access functions
            config_access_context = self._get_config_access_context(config_values)
            ctx.update(config_access_context)

            # Evaluate formula
            cost = safe_eval(self.cost_formula, ctx)
            return float(cost)
        except Exception as e:
            _logger.error(f"Error evaluating cost formula for operation template {self.name}: {str(e)}")
            return 0.0



    def _convert_answer_value(self, value, field_type):
        """
        Convert answer value to appropriate type for calculations

        Args:
            value: Raw value from configuration
            field_type: Type of the field

        Returns:
            Converted value suitable for calculations
        """
        if value is None or value == '':
            return 0

        if field_type in ['integer', 'float']:
            try:
                return float(value) if field_type == 'float' else int(value)
            except (ValueError, TypeError):
                return 0
        elif field_type == 'boolean':
            return 1 if value in [True, 'true', 'True', '1', 1] else 0
        elif field_type == 'selection':
            # For selection fields, try to extract numeric value
            if isinstance(value, str):
                # Try to extract number from text like "3 Mullions (Vertical)"
                import re
                match = re.search(r'^(\d+)', str(value))
                if match:
                    return int(match.group(1))
                # If it's just a digit string
                if value.isdigit():
                    return int(value)
            return 0
        else:
            # For text fields, try to extract numeric value
            try:
                return float(value)
            except (ValueError, TypeError):
                return 0

    def _get_config_access_context(self, config_values):
        """
        Provide functions to access configuration values in formulas

        Args:
            config_values (dict): Configuration values from config_data

        Returns:
            dict: Context with configuration access functions
        """
        def get_question(question_number):
            """Get answer by question number (e.g., get_question(25) or get_question(36))"""
            try:
                # First try to find by question number using the template relationship
                if self.config_template_id:
                    template = self.config_template_id
                    for section in template.section_ids:
                        for field in section.field_ids:
                            if field.question_number == question_number:
                                # Try both field ID and technical name
                                value = config_values.get(str(field.id)) or config_values.get(field.technical_name)
                                converted_value = self._convert_answer_value(value, field.field_type)
                                _logger.info(f"[OPERATION_COSTS] get_question({question_number}): Found field {field.name} (ID: {field.id}, tech_name: {field.technical_name}), raw_value: {value}, converted: {converted_value}")
                                return converted_value

                # Fallback: search across all fields
                field = self.env['config.matrix.field'].search([
                    ('question_number', '=', question_number)
                ], limit=1)

                if field:
                    # Try both field ID and technical name
                    value = config_values.get(str(field.id)) or config_values.get(field.technical_name)
                    converted_value = self._convert_answer_value(value, field.field_type)
                    _logger.info(f"[OPERATION_COSTS] get_question({question_number}): Found field {field.name} (ID: {field.id}, tech_name: {field.technical_name}), raw_value: {value}, converted: {converted_value}")
                    return converted_value

                # Final fallback: try direct lookup
                fallback_value = self._convert_answer_value(config_values.get(str(question_number), 0), 'integer')
                _logger.warning(f"[OPERATION_COSTS] get_question({question_number}): No field found, using fallback: {fallback_value}")
                return fallback_value
            except Exception as e:
                _logger.error(f"[OPERATION_COSTS] Error in get_question({question_number}): {e}")
                return 0



        def get_answer(question_id):
            """Get answer by question ID"""
            # First try to find by question number (field.question_number)
            if self.config_template_id:
                template = self.config_template_id
                for section in template.section_ids:
                    for field in section.field_ids:
                        if field.question_number == question_id:
                            # Try both field ID and technical name
                            value = config_values.get(str(field.id)) or config_values.get(field.technical_name)
                            return self._convert_answer_value(value, field.field_type)

            # Fallback: try direct lookup by question_id as string
            return config_values.get(str(question_id), 0)

        def get_field_value(field_name):
            """Get value by field technical name"""
            return config_values.get(field_name, 0)

        def get_field_by_id(field_id):
            """Get value by field ID"""
            return config_values.get(str(field_id), 0)

        def get_option_quantity(question_number):
            """
            Get the quantity value from the selected option for a selection field

            Args:
                question_number: The question number of the selection field

            Returns:
                float: The calculated quantity from the selected option's quantity_formula
            """
            try:
                # Find the field by question number
                field = None
                if self.config_template_id:
                    template = self.config_template_id
                    for section in template.section_ids:
                        for f in section.field_ids:
                            if f.question_number == question_number:
                                field = f
                                break
                        if field:
                            break

                if not field:
                    # Fallback: search across all fields
                    field = self.env['config.matrix.field'].search([
                        ('question_number', '=', question_number)
                    ], limit=1)

                if not field:
                    _logger.warning(f"[OPERATION_COSTS] get_option_quantity({question_number}): Field not found")
                    return 1.0

                # Check if it's a selection field
                if field.field_type != 'selection':
                    _logger.warning(f"[OPERATION_COSTS] get_option_quantity({question_number}): Field {field.name} is not a selection field")
                    return 1.0

                # Get the selected value
                selected_value = config_values.get(str(field.id)) or config_values.get(field.technical_name)
                if not selected_value:
                    _logger.warning(f"[OPERATION_COSTS] get_option_quantity({question_number}): No value selected for field {field.name}")
                    return 1.0

                # Find the matching option
                option = field.option_ids.filtered(lambda o: o.value == selected_value)
                if not option:
                    _logger.warning(f"[OPERATION_COSTS] get_option_quantity({question_number}): Option not found for value '{selected_value}' in field {field.name}")
                    return 1.0

                # Evaluate the option's quantity formula
                if not option.quantity_formula:
                    _logger.info(f"[OPERATION_COSTS] get_option_quantity({question_number}): Option '{option.name}' has no quantity formula, returning 1.0")
                    return 1.0

                try:
                    from odoo.tools.safe_eval import safe_eval
                    import math

                    # Create evaluation context
                    ctx = dict(config_values)
                    math_context = {
                        'round': round,
                        'ceil': math.ceil,
                        'floor': math.floor,
                        'abs': abs,
                        'max': max,
                        'min': min,
                        'sum': sum
                    }
                    ctx.update(math_context)

                    # Evaluate the quantity formula
                    quantity = safe_eval(option.quantity_formula, ctx)
                    _logger.info(f"[OPERATION_COSTS] get_option_quantity({question_number}): Option '{option.name}' quantity formula '{option.quantity_formula}' evaluated to {quantity}")
                    return float(quantity)

                except Exception as e:
                    _logger.error(f"[OPERATION_COSTS] get_option_quantity({question_number}): Error evaluating quantity formula '{option.quantity_formula}': {e}")
                    return 1.0

            except Exception as e:
                _logger.error(f"[OPERATION_COSTS] get_option_quantity({question_number}): Unexpected error: {e}")
                return 1.0

        return {
            'get_question': get_question,
            'get_answer': get_answer,
            'get_field_value': get_field_value,
            'get_field_by_id': get_field_by_id,
            'get_option_quantity': get_option_quantity,
        }

    def _convert_answer_value(self, value, field_type):
        """
        Convert answer value to appropriate type for calculations

        Args:
            value: Raw value from configuration
            field_type: Type of the field

        Returns:
            Converted value suitable for calculations
        """
        if value is None:
            return 0

        if field_type in ['integer', 'float']:
            try:
                return float(value) if field_type == 'float' else int(value)
            except (ValueError, TypeError):
                return 0
        elif field_type == 'boolean':
            return 1 if value in [True, 'true', 'True', '1', 1] else 0
        elif field_type == 'selection':
            # For selection fields, try to extract numeric value
            if isinstance(value, str) and value.isdigit():
                return int(value)
            # Try to extract number from text like "3 Mullions (Vertical)"
            import re
            match = re.search(r'^(\d+)', str(value))
            if match:
                return int(match.group(1))
            return 0
        else:
            # For text fields, try to extract numeric value
            try:
                return float(value)
            except (ValueError, TypeError):
                return 0

    def _get_enhanced_formula_context(self, config_values=None):
        """
        Get enhanced context for formula evaluation with access to Labour Matrix

        Args:
            config_values (dict): Configuration values

        Returns:
            dict: Enhanced context with helper functions
        """
        if not config_values:
            config_values = {}

        def get_labor_time(matrix_name=None, height=None, width=None):
            """Get labor time from Labour Matrix by name and dimensions"""
            try:
                # Use linked matrix if no matrix_name provided
                if matrix_name is None and self.labor_matrix_id:
                    labor_matrix = self.labor_matrix_id
                else:
                    # Find the labor matrix by name
                    labor_matrix = self.env['config.matrix.labor.time.matrix'].search([
                        ('name', '=', matrix_name),
                        ('active', '=', True)
                    ], limit=1)

                if not labor_matrix:
                    _logger.warning(f"Labor matrix '{matrix_name}' not found")
                    return 0.0

                # Use provided dimensions or try to get from calculated field mappings or config_values
                if height is None:
                    # Try to get from Y-axis calculated field mapping first
                    if self.y_axis_calculated_field_id and self.y_axis_calculated_field_id.name in config_values:
                        height = config_values.get(self.y_axis_calculated_field_id.name)
                    else:
                        # Fallback to common field names
                        height = config_values.get('height') or config_values.get('door_height')

                if width is None:
                    # Try to get from X-axis calculated field mapping first
                    if self.x_axis_calculated_field_id and self.x_axis_calculated_field_id.name in config_values:
                        width = config_values.get(self.x_axis_calculated_field_id.name)
                    else:
                        # Fallback to common field names
                        width = config_values.get('width') or config_values.get('door_width')

                if height is None or width is None:
                    _logger.warning(f"Height ({height}) or width ({width}) not available for labor matrix lookup")
                    return 0.0

                # Get labor time for dimensions
                labor_time = labor_matrix.get_labor_time_for_dimensions(float(height), float(width))
                return labor_time if labor_time is not False else 0.0

            except Exception as e:
                _logger.error(f"Error getting labor time from matrix '{matrix_name}': {str(e)}")
                return 0.0

        def get_labor_time_hours(matrix_name=None, height=None, width=None):
            """Get labor time in hours from Labour Matrix"""
            try:
                # Use linked matrix if no matrix_name provided
                if matrix_name is None and self.labor_matrix_id:
                    labor_matrix = self.labor_matrix_id
                else:
                    # Find the labor matrix by name
                    labor_matrix = self.env['config.matrix.labor.time.matrix'].search([
                        ('name', '=', matrix_name),
                        ('active', '=', True)
                    ], limit=1)

                if not labor_matrix:
                    _logger.warning(f"Labor matrix '{matrix_name}' not found")
                    return 0.0

                # Use provided dimensions or try to get from calculated field mappings or config_values
                if height is None:
                    # Try to get from Y-axis calculated field mapping first
                    if self.y_axis_calculated_field_id and self.y_axis_calculated_field_id.name in config_values:
                        height = config_values.get(self.y_axis_calculated_field_id.name)
                    else:
                        # Fallback to common field names
                        height = config_values.get('height') or config_values.get('door_height')

                if width is None:
                    # Try to get from X-axis calculated field mapping first
                    if self.x_axis_calculated_field_id and self.x_axis_calculated_field_id.name in config_values:
                        width = config_values.get(self.x_axis_calculated_field_id.name)
                    else:
                        # Fallback to common field names
                        width = config_values.get('width') or config_values.get('door_width')

                if height is None or width is None:
                    _logger.warning(f"Height ({height}) or width ({width}) not available for labor matrix lookup")
                    return 0.0

                # Get labor time in hours
                labor_time = labor_matrix.get_labor_time_in_hours(float(height), float(width))
                return labor_time if labor_time is not False else 0.0

            except Exception as e:
                _logger.error(f"Error getting labor time in hours from matrix '{matrix_name}': {str(e)}")
                return 0.0

        def requires_multiple_workers(height, width, threshold=2500):
            """Check if dimensions require multiple workers (e.g., 2 men for large doors)"""
            try:
                h = float(height) if height is not None else 0
                w = float(width) if width is not None else 0
                return max(h, w) >= threshold
            except:
                return False

        def get_worker_multiplier(height, width, threshold=2500, single_worker=1, multiple_workers=2):
            """Get worker multiplier based on dimensions"""
            if requires_multiple_workers(height, width, threshold):
                return multiple_workers
            return single_worker

        def get_fixed_price(code):
            """Get value from Fixed Price Table by code"""
            try:
                price_entry = self.env['config.matrix.operation.price'].search([
                    ('code', '=', code),
                    ('active', '=', True)
                ], limit=1)
                if price_entry:
                    return price_entry.value_cost
                else:
                    _logger.warning(f"Fixed price code '{code}' not found")
                    return 0.0
            except Exception as e:
                _logger.error(f"Error getting fixed price for code '{code}': {str(e)}")
                return 0.0

        def get_price(matrix_name=None, height=None, width=None):
            """Get price from Price Matrix by name and dimensions"""
            try:
                # Use linked matrix if no matrix_name provided
                if matrix_name is None and self.price_matrix_id:
                    price_matrix = self.price_matrix_id
                else:
                    # Find the price matrix by name - try exact match first
                    price_matrix = self.env['config.matrix.price.matrix'].search([
                        ('name', '=', matrix_name),
                        ('active', '=', True)
                    ], limit=1)

                    # If no exact match, try partial matching (case-insensitive)
                    if not price_matrix:
                        price_matrix = self.env['config.matrix.price.matrix'].search([
                            ('name', 'ilike', matrix_name),
                            ('active', '=', True)
                        ], limit=1)

                if not price_matrix:
                    _logger.warning(f"Price matrix '{matrix_name}' not found")
                    return 0.0

                # Get height and width from config values if not provided
                if height is None:
                    # Try to get from Y-axis calculated field mapping first
                    if self.y_axis_calculated_field_id and self.y_axis_calculated_field_id.name in config_values:
                        height = config_values.get(self.y_axis_calculated_field_id.name)
                    else:
                        # Fallback to common field names
                        height = config_values.get('height') or config_values.get('door_height')

                if width is None:
                    # Try to get from X-axis calculated field mapping first
                    if self.x_axis_calculated_field_id and self.x_axis_calculated_field_id.name in config_values:
                        width = config_values.get(self.x_axis_calculated_field_id.name)
                    else:
                        # Fallback to common field names
                        width = config_values.get('width') or config_values.get('door_width')

                if height is None or width is None:
                    _logger.warning(f"Height ({height}) or width ({width}) not available for price matrix lookup")
                    return 0.0

                # Get price for dimensions
                price = price_matrix.get_price_for_dimensions(float(height), float(width))
                return price if price is not False else 0.0

            except Exception as e:
                _logger.error(f"Error getting price from matrix '{matrix_name}': {str(e)}")
                return 0.0

        # Return enhanced context
        context = {
            'get_labor_time': get_labor_time,
            'get_labor_time_hours': get_labor_time_hours,
            'requires_multiple_workers': requires_multiple_workers,
            'get_worker_multiplier': get_worker_multiplier,
            'get_fixed_price': get_fixed_price,
            'get_price': get_price,
        }

        # Add test values for formula testing
        if hasattr(self, 'test_height') and hasattr(self, 'test_width'):
            context.update({
                'test_height': self.test_height or 0,
                'test_width': self.test_width or 0,
            })

        return context




    

    


    def copy_data(self, default=None):
        """Override copy to add 'Copy' to the name"""
        default = dict(default or {})
        if 'name' not in default:
            default['name'] = _("%s (Copy)") % self.name
        return super().copy_data(default)
    
    def action_view_usage(self):
        """Action to view where this template is used"""
        self.ensure_one()
        
        # Get all field mappings using this template
        field_mappings = self.env['config.matrix.field.operation.mapping'].search([
            ('operation_template_id', '=', self.id)
        ])
        
        # Get all option mappings using this template
        option_mappings = self.env['config.matrix.option.operation.mapping'].search([
            ('operation_template_id', '=', self.id)
        ])
        
        # Create a combined view or show separate views
        if field_mappings and option_mappings:
            # Show a custom view with both
            return {
                'type': 'ir.actions.act_window',
                'name': f'Usage of {self.name}',
                'view_mode': 'tree,form',
                'res_model': 'config.matrix.operation.mapping',
                'domain': [
                    '|',
                    ('field_id', 'in', [m.field_id.id for m in field_mappings]),
                    ('option_id', 'in', [m.option_id.id for m in option_mappings])
                ],
                'context': {'default_operation_template_id': self.id}
            }
        elif field_mappings:
            return {
                'type': 'ir.actions.act_window',
                'name': f'Field Mappings using {self.name}',
                'view_mode': 'tree,form',
                'res_model': 'config.matrix.field.operation.mapping',
                'domain': [('operation_template_id', '=', self.id)],
            }
        elif option_mappings:
            return {
                'type': 'ir.actions.act_window',
                'name': f'Option Mappings using {self.name}',
                'view_mode': 'tree,form',
                'res_model': 'config.matrix.option.operation.mapping',
                'domain': [('operation_template_id', '=', self.id)],
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': _('This operation template is not currently used anywhere.'),
                    'type': 'info',
                }
            }
