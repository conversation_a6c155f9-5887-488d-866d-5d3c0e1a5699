# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json
import logging

_logger = logging.getLogger(__name__)

class ConfigMatrixConfiguration(models.Model):
    _inherit = 'config.matrix.configuration'
    
    # Mesh cutting operation
    mesh_cut_operation_id = fields.Many2one('mesh.cut.operation', 'Mesh Cut Operation',
                                          help="Associated mesh cutting operation for this configuration")
    
    # Mesh requirements (computed from configuration)
    requires_mesh = fields.Bo<PERSON>an('Requires Mesh', compute='_compute_mesh_requirements', store=True)
    mesh_width_required = fields.Float('Required Mesh Width (mm)', compute='_compute_mesh_requirements', store=True)
    mesh_height_required = fields.Float('Required Mesh Height (mm)', compute='_compute_mesh_requirements', store=True)
    mesh_series_required = fields.Selection([
        ('saltwaterseries', 'SaltwaterSeries'),
        ('xceed', 'Xceed'),
        ('basix', 'BasiX'),
        ('diamond_7mm', '7mm Diamond Grille'),
        ('flyscreen', 'Flyscreen'),
        ('petscreen', 'Petscreen'),
        ('sandfly', 'Sandfly Mesh'),
        ('bal_insect', 'BAL Rated Insect Screen'),
        ('diamond_small', 'Small Diamond Grille'),
        ('colonial_castings', 'Colonial Castings Design'),
    ], string='Required Mesh Series', compute='_compute_mesh_requirements', store=True)
    
    @api.depends('config_data', 'template_id')
    def _compute_mesh_requirements(self):
        """Compute mesh requirements from configuration values using template mesh config"""
        for config in self:
            # Initialize defaults
            config.requires_mesh = False
            config.mesh_width_required = 0.0
            config.mesh_height_required = 0.0
            config.mesh_series_required = False

            if not config.config_data or not config.template_id:
                continue

            # Get mesh configuration for this template
            mesh_config = self.env['config.matrix.mesh.config'].search([
                ('template_id', '=', config.template_id.id),
                ('active', '=', True)
            ], limit=1)

            if not mesh_config:
                # Fallback to legacy detection
                config._compute_mesh_requirements_legacy()
                continue

            try:
                field_values = json.loads(config.config_data) if isinstance(config.config_data, str) else config.config_data

                # Check if this configuration requires mesh based on template config
                requires_mesh = False

                if mesh_config.detection_method == 'field_names':
                    patterns = [p.strip().lower() for p in (mesh_config.field_name_patterns or '').split(',') if p.strip()]
                    requires_mesh = any(
                        any(pattern in field_name.lower() for pattern in patterns)
                        for field_name in field_values.keys()
                    )

                elif mesh_config.detection_method == 'field_values':
                    patterns = [p.strip().lower() for p in (mesh_config.field_value_patterns or '').split(',') if p.strip()]
                    requires_mesh = any(
                        any(pattern in str(value).lower() for pattern in patterns)
                        for value in field_values.values()
                        if isinstance(value, str)
                    )

                if requires_mesh:
                    config.requires_mesh = True

                    # Extract dimensions using configured field names
                    width_field = mesh_config.width_field_name or 'width'
                    height_field = mesh_config.height_field_name or 'height'

                    for field_name, value in field_values.items():
                        field_name_lower = field_name.lower()

                        # Extract width
                        if width_field.lower() in field_name_lower:
                            try:
                                config.mesh_width_required = float(value)
                            except (ValueError, TypeError):
                                pass

                        # Extract height
                        elif height_field.lower() in field_name_lower:
                            try:
                                config.mesh_height_required = float(value)
                            except (ValueError, TypeError):
                                pass

                    # Extract mesh series using series mappings
                    for mapping in mesh_config.series_mapping_ids:
                        pattern = mapping.field_value_pattern.lower()
                        if any(pattern in str(value).lower() for value in field_values.values() if isinstance(value, str)):
                            config.mesh_series_required = mapping.mesh_series
                            break

                    # Fallback series detection if no mapping found
                    if not config.mesh_series_required:
                        for value in field_values.values():
                            if isinstance(value, str):
                                value_lower = str(value).lower()
                                if 'saltwater' in value_lower:
                                    config.mesh_series_required = 'saltwater'
                                    break
                                elif 'basix' in value_lower:
                                    config.mesh_series_required = 'basix'
                                    break
                                elif 'insect' in value_lower:
                                    config.mesh_series_required = 'insect'
                                    break
                                elif 'sandfly' in value_lower:
                                    config.mesh_series_required = 'sandfly'
                                    break
                                elif 'diamond' in value_lower:
                                    config.mesh_series_required = 'diamond'
                                    break
                                elif 'fly' in value_lower:
                                    config.mesh_series_required = 'flyscreen'
                                    break
                
            except Exception as e:
                _logger.warning(f"Error computing mesh requirements for config {config.id}: {e}")

    def _compute_mesh_requirements_legacy(self):
        """Legacy mesh requirements computation for templates without mesh config"""
        self.ensure_one()

        if not self.config_data:
            return

        try:
            field_values = json.loads(self.config_data) if isinstance(self.config_data, str) else self.config_data

            # Legacy detection logic
            mesh_indicators = ['mesh', 'screen', 'grill']
            requires_mesh = any(
                any(indicator in str(value).lower() for indicator in mesh_indicators)
                for value in field_values.values()
                if isinstance(value, str)
            )

            if requires_mesh:
                self.requires_mesh = True

                # Extract dimensions - legacy field names
                width_fields = ['width', 'door_width', 'opening_width']
                height_fields = ['height', 'door_height', 'opening_height']

                for field_name, value in field_values.items():
                    field_name_lower = field_name.lower()

                    # Extract width
                    if any(width_field in field_name_lower for width_field in width_fields):
                        try:
                            self.mesh_width_required = float(value)
                        except (ValueError, TypeError):
                            pass

                    # Extract height
                    elif any(height_field in field_name_lower for height_field in height_fields):
                        try:
                            self.mesh_height_required = float(value)
                        except (ValueError, TypeError):
                            pass

                # Legacy series detection
                for value in field_values.values():
                    if isinstance(value, str):
                        value_lower = str(value).lower()
                        if 'saltwater' in value_lower:
                            self.mesh_series_required = 'saltwater'
                            break
                        elif 'diamond' in value_lower:
                            self.mesh_series_required = 'diamond'
                            break
                        elif 'fly' in value_lower:
                            self.mesh_series_required = 'flyscreen'
                            break

        except Exception as e:
            _logger.warning(f"Error in legacy mesh requirements computation for config {self.id}: {e}")
    
    def action_create_mesh_cut_operation(self):
        """Create mesh cutting operation for this configuration"""
        self.ensure_one()
        
        if not self.requires_mesh:
            raise UserError(_("This configuration does not require mesh."))
        
        if self.mesh_cut_operation_id:
            raise UserError(_("Mesh cutting operation already exists for this configuration."))
        
        if not self.mesh_width_required or not self.mesh_height_required:
            raise UserError(_("Cannot determine required mesh dimensions from configuration."))
        
        # Create the cutting operation
        operation = self.env['mesh.cut.operation'].create({
            'name': f'Cut Mesh - {self.name}',
            'config_id': self.id,
            'sale_order_id': self.sale_order_line_id.order_id.id if self.sale_order_line_id else False,
            'required_width': self.mesh_width_required,
            'required_height': self.mesh_height_required,
            'mesh_series': self.mesh_series_required or 'saltwater',
            'required_qty': 1.0,
        })
        
        self.mesh_cut_operation_id = operation.id
        
        # Automatically find mesh
        try:
            operation.action_find_mesh()
        except UserError:
            # If no mesh found, still create the operation but show warning
            pass
        
        return {
            'name': _('Mesh Cut Operation'),
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.operation',
            'view_mode': 'form',
            'res_id': operation.id,
        }
    
    def action_view_mesh_cut_operation(self):
        """View the mesh cutting operation for this configuration"""
        self.ensure_one()

        if not self.mesh_cut_operation_id:
            raise UserError(_("No mesh cutting operation exists for this configuration."))

        return {
            'name': _('Mesh Cut Operation'),
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.operation',
            'view_mode': 'form',
            'res_id': self.mesh_cut_operation_id.id,
        }

    def _auto_create_mesh_operation_if_needed(self):
        """Automatically create mesh operation if configuration requires mesh"""
        for config in self:
            if config.requires_mesh and not config.mesh_cut_operation_id:
                try:
                    config.action_create_mesh_cut_operation()
                    _logger.info(f"Auto-created mesh cut operation for configuration {config.id}")
                except Exception as e:
                    _logger.warning(f"Failed to auto-create mesh operation for config {config.id}: {e}")

    def apply_to_sale_order_line(self):
        """Override to include mesh operation creation"""
        # Call parent method first
        result = super().apply_to_sale_order_line()

        # Auto-create mesh operations if needed
        self._auto_create_mesh_operation_if_needed()

        return result


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'
    
    # Mesh-related fields
    requires_mesh_cutting = fields.Boolean('Requires Mesh Cutting', 
                                         compute='_compute_mesh_requirements', store=True)
    mesh_cut_operation_ids = fields.One2many('mesh.cut.operation', 'sale_order_id', 
                                           string='Mesh Cut Operations',
                                           domain="[('config_id', '=', config_id)]")
    mesh_cut_count = fields.Integer('Mesh Cut Operations', compute='_compute_mesh_cut_count')
    
    @api.depends('config_id.requires_mesh')
    def _compute_mesh_requirements(self):
        for line in self:
            line.requires_mesh_cutting = line.config_id.requires_mesh if line.config_id else False
    
    @api.depends('mesh_cut_operation_ids')
    def _compute_mesh_cut_count(self):
        for line in self:
            line.mesh_cut_count = len(line.mesh_cut_operation_ids)
    
    def action_view_mesh_operations(self):
        """View mesh cutting operations for this line"""
        self.ensure_one()
        
        return {
            'name': _('Mesh Cut Operations'),
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.operation',
            'view_mode': 'list,form',
            'domain': [('id', 'in', self.mesh_cut_operation_ids.ids)],
            'context': {
                'default_sale_order_id': self.order_id.id,
                'default_config_id': self.config_id.id if self.config_id else False,
            },
        }


class StockMove(models.Model):
    _inherit = 'stock.move'
    
    # Link to mesh cutting operation
    mesh_cut_operation_id = fields.Many2one('mesh.cut.operation', 'Mesh Cut Operation')
    is_mesh_byproduct = fields.Boolean('Is Mesh Byproduct', 
                                     help="This move creates a mesh byproduct")


class MrpProduction(models.Model):
    _inherit = 'mrp.production'
    
    def _update_raw_moves(self, factor):
        """Override to handle mesh-specific components"""
        result = super()._update_raw_moves(factor)
        
        # Handle mesh components with specific lot requirements
        for move in self.move_raw_ids:
            if move.product_id.is_mesh_product and move.sale_line_id:
                config = move.sale_line_id.config_id
                if config and config.mesh_cut_operation_id:
                    operation = config.mesh_cut_operation_id
                    if operation.state == 'done' and operation.source_lot_id:
                        # Assign specific lot to the move
                        move.write({
                            'lot_ids': [(6, 0, [operation.source_lot_id.id])],
                            'restrict_lot_id': operation.source_lot_id.id,
                        })
        
        return result


class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    def action_view_mesh_operations(self):
        """View mesh cutting operations for this product"""
        self.ensure_one()
        
        operations = self.env['mesh.cut.operation'].search([
            ('source_product_id.product_tmpl_id', '=', self.id)
        ])
        
        return {
            'name': _('Mesh Cut Operations'),
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.operation',
            'view_mode': 'list,form',
            'domain': [('id', 'in', operations.ids)],
        }
    
    def action_view_mesh_stock_by_dimensions(self):
        """View mesh stock grouped by dimensions"""
        self.ensure_one()
        
        if not self.is_mesh_product:
            raise UserError(_("This is not a mesh product."))
        
        # For unplanned off-cuts, group by lot dimensions
        if self.mesh_type == 'unplanned':
            lots = self.env['stock.lot'].search([
                ('product_id.product_tmpl_id', '=', self.id),
                ('mesh_width', '>', 0),
                ('mesh_height', '>', 0),
            ])
            
            return {
                'name': _('Mesh Stock by Dimensions'),
                'type': 'ir.actions.act_window',
                'res_model': 'stock.lot',
                'view_mode': 'list,form',
                'domain': [('id', 'in', lots.ids)],
            }
        
        # For planned off-cuts and masters, show quants
        else:
            quants = self.env['stock.quant'].search([
                ('product_id.product_tmpl_id', '=', self.id),
                ('quantity', '>', 0),
            ])
            
            return {
                'name': _('Mesh Stock'),
                'type': 'ir.actions.act_window',
                'res_model': 'stock.quant',
                'view_mode': 'list,form',
                'domain': [('id', 'in', quants.ids)],
            }
