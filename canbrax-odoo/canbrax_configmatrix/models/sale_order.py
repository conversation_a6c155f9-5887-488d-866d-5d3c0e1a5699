# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import logging

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    has_configurable_products = fields.Boolean(compute='_compute_has_configurable_products',
                                             store=True, string="Has Configurable Products")

    @api.depends('order_line.is_configurable')
    def _compute_has_configurable_products(self):
        for order in self:
            order.has_configurable_products = any(line.is_configurable for line in order.order_line)

    def action_confirm(self):
        """Override to validate and process configured products before confirming the order"""
        self._validate_configurable_products_boms()
        # Process all configured products
        self._process_configured_products()
        result = super(SaleOrder, self).action_confirm()
        return result

    def _process_configured_products(self):
        """Process all configured products in the order"""
        for order in self:
            configured_lines = order.order_line.filtered(lambda l: l.is_configured and l.config_id)

            for line in configured_lines:
                config = line.config_id
                _logger.info(f"Processing configured product for line {line.id}, config {config.id}")

                try:
                    # 1. Generate BOM if not already generated
                    if not config.bom_id:
                        _logger.info(f"Generating BOM for configuration {config.id}")
                        bom = config.generate_bom()
                        if not bom and line._requires_configured_bom():
                            # If BOM generation failed for a product that requires it, this is an error
                            raise UserError(self.env._(
                                "Failed to generate Bill of Materials for configured product '%s' on line %s. "
                                "This product requires a BOM for manufacturing. Please check the configuration and try again."
                            ) % (line.product_id.name, line.id))

                    # 2. Use the total aggregated price from configuration
                    _logger.info(f"Setting price from configuration total for {config.id}")
                    _logger.info(f"[PRICING_DEBUG] Current line price: ${line.price_unit}")
                    _logger.info(f"[PRICING_DEBUG] Configuration total price: ${config.price_total}")

                    # Use the total aggregated price from the configuration
                    # This includes components + operations calculated during configuration
                    if config.price_total > 0:
                        final_price = config.price_total
                        _logger.info(f"[PRICING_DEBUG] Using configuration total price: ${final_price}")
                    else:
                        # Fallback to existing line price if total is not set
                        final_price = line.price_unit
                        _logger.info(f"[PRICING_DEBUG] Configuration total not set, keeping line price: ${final_price}")

                    # 3. Apply to Order Line
                    _logger.info(f"Applying configuration {config.id} to order line {line.id}")
                    if line.price_unit != final_price:
                        _logger.info(f"[PRICING_DEBUG] Updating line price from ${line.price_unit} to ${final_price}")
                        line.price_unit = final_price
                    else:
                        _logger.info(f"[PRICING_DEBUG] Price unchanged: ${final_price}")

                    # Update state if needed
                    if config.state != 'applied':
                        config.state = 'applied'

                except Exception as e:
                    _logger.error(f"Error processing configured product: {str(e)}")
                    # For products that require BOMs, raise the error to prevent confirmation
                    if line._requires_configured_bom():
                        raise UserError(self.env._(
                            "Error processing configured product '%s' on line %s: %s\n\n"
                            "This product requires a valid configuration and BOM for manufacturing. "
                            "Please resolve the issue before confirming the order."
                        ) % (line.product_id.name, line.id, str(e)))
                    # For other products, just log and continue

    def action_view_boms(self):
        """
        View all BOMs related to configured products in this order.

        This method opens a view showing all BOMs that were generated
        from the configured products in this sales order.

        Returns:
            dict: Action to open the BOM list view filtered to show only BOMs from this order
        """
        self.ensure_one()

        # Get all configurations with BOMs
        configs_with_boms = self.order_line.mapped('config_id').filtered(lambda c: c.bom_id)
        bom_ids = configs_with_boms.mapped('bom_id').ids

        if not bom_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No BOMs Available'),
                    'message': _('No Bills of Materials have been generated for the configured products in this order.'),
                    'type': 'warning',
                }
            }

        # Return action to view BOMs
        return {
            'name': _('Bills of Materials'),
            'type': 'ir.actions.act_window',
            'res_model': 'mrp.bom',
            'view_mode': 'list,form',
            'domain': [('id', 'in', bom_ids)],
            'context': {'create': False},
        }

    def has_boms(self):
        """
        Check if this order has any BOMs from configured products.

        Returns:
            bool: True if at least one configured product has a BOM, False otherwise
        """
        return bool(self.order_line.mapped('config_id').filtered(lambda c: c.bom_id))

    def _validate_configurable_products_boms(self):
        """
        Validate that all configurable products with MTO+Manufacturing routes have valid BOMs.
        """
        for order in self:
            lines_requiring_boms = order.order_line.filtered(lambda l: l._requires_configured_bom())

            if not lines_requiring_boms:
                continue

            validation_errors = []
            for line in lines_requiring_boms:
                try:
                    line._validate_configured_bom()
                except ValidationError as e:
                    validation_errors.append(str(e))

            if validation_errors:
                error_message = self.env._(
                    "Cannot confirm Sale Order %s due to missing BOMs for configurable products:\n\n%s\n\n"
                    "Please ensure all configurable products are properly configured with valid BOMs before confirming the order."
                ) % (order.name, '\n'.join(validation_errors))

                raise ValidationError(error_message)
