# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import logging

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    has_configurable_products = fields.Boolean(compute='_compute_has_configurable_products',
                                             store=True, string="Has Configurable Products")

    # Override state field to insert review state in correct position
    state = fields.Selection([
        ('draft', 'Quotation'),
        ('sent', 'Quotation Sent'),
        ('review', 'Review'),
        ('sale', 'Sales Order'),
        ('done', 'Locked'),
        ('cancel', 'Cancelled'),
    ], string='Status', readonly=True, copy=False, index=True, tracking=3, default='draft')

    @api.depends('order_line.is_configurable')
    def _compute_has_configurable_products(self):
        for order in self:
            order.has_configurable_products = any(line.is_configurable for line in order.order_line)

    def action_set_review(self):
        """Set the sales order to review state"""
        for order in self:
            if order.state in ['draft', 'sent']:
                order.state = 'review'
                order.message_post(body=_("Sales order moved to Review stage after configuration."))
            else:
                raise UserError(_("Cannot move to Review state from current state: %s") % order.state)

    def action_review_to_sale(self):
        """Move from review state to confirmed sale"""
        for order in self:
            if order.state == 'review':
                return order.action_confirm()
            else:
                raise UserError(_("Cannot confirm order from current state: %s") % order.state)

    def action_confirm(self):
        """Override to handle review state and process configured products"""
        # Allow confirmation from review state
        for order in self:
            if order.state not in ['draft', 'sent', 'review']:
                raise UserError(_("Cannot confirm order from current state: %s") % order.state)

        # Store original states for orders in review
        original_states = {}
        for order in self:
            if order.state == 'review':
                original_states[order.id] = 'review'
                # Temporarily set to 'sent' for parent validation
                order.write({'state': 'sent'})

        try:
            # Call the original validation and processing
            self._validate_configurable_products_boms()
            # Process all configured products
            self._process_configured_products()
            # Execute mesh hunting for templates that require it
            self._execute_mesh_hunting()
            result = super(SaleOrder, self).action_confirm()
            return result
        except Exception as e:
            # Restore original states if there was an error
            for order in self:
                if order.id in original_states:
                    order.write({'state': original_states[order.id]})
            raise e



    def _process_configured_products(self):
        """Process all configured products in the order"""
        for order in self:
            configured_lines = order.order_line.filtered(lambda l: l.is_configured and l.config_id)

            for line in configured_lines:
                config = line.config_id
                _logger.info(f"Processing configured product for line {line.id}, config {config.id}")

                try:
                    # 1. Generate BOM if not already generated
                    if not config.bom_id:
                        _logger.info(f"Generating BOM for configuration {config.id}")
                        bom = config.generate_bom()
                        if not bom and line._requires_configured_bom():
                            # If BOM generation failed for a product that requires it, this is an error
                            raise UserError(self.env._(
                                "Failed to generate Bill of Materials for configured product '%s' on line %s. "
                                "This product requires a BOM for manufacturing. Please check the configuration and try again."
                            ) % (line.product_id.name, line.id))

                    # 2. Use the total aggregated price from configuration
                    _logger.info(f"Setting price from configuration total for {config.id}")
                    _logger.info(f"[PRICING_DEBUG] Current line price: ${line.price_unit}")
                    _logger.info(f"[PRICING_DEBUG] Configuration total price: ${config.price_total}")

                    # Use the total aggregated price from the configuration
                    # This includes components + operations calculated during configuration
                    if config.price_total > 0:
                        final_price = config.price_total
                        _logger.info(f"[PRICING_DEBUG] Using configuration total price: ${final_price}")
                    else:
                        # Fallback to existing line price if total is not set
                        final_price = line.price_unit
                        _logger.info(f"[PRICING_DEBUG] Configuration total not set, keeping line price: ${final_price}")

                    # 3. Apply to Order Line
                    _logger.info(f"Applying configuration {config.id} to order line {line.id}")
                    if line.price_unit != final_price:
                        _logger.info(f"[PRICING_DEBUG] Updating line price from ${line.price_unit} to ${final_price}")
                        line.price_unit = final_price
                    else:
                        _logger.info(f"[PRICING_DEBUG] Price unchanged: ${final_price}")

                    # Update state if needed
                    if config.state != 'applied':
                        config.state = 'applied'

                except Exception as e:
                    _logger.error(f"Error processing configured product: {str(e)}")
                    # For products that require BOMs, raise the error to prevent confirmation
                    if line._requires_configured_bom():
                        raise UserError(self.env._(
                            "Error processing configured product '%s' on line %s: %s\n\n"
                            "This product requires a valid configuration and BOM for manufacturing. "
                            "Please resolve the issue before confirming the order."
                        ) % (line.product_id.name, line.id, str(e)))
                    # For other products, just log and continue

    def _execute_mesh_hunting(self):
        """Execute mesh hunting for templates that require it during SO confirmation"""
        for order in self:
            # Find order lines with configurations that require mesh hunting
            mesh_required_lines = order.order_line.filtered(
                lambda l: l.config_id and 
                         l.config_id.template_id and 
                         l.config_id.template_id.mesh_required
            )
            
            if not mesh_required_lines:
                _logger.info(f"No mesh hunting required for order {order.name}")
                return
            
            _logger.info(f"Processing mesh hunting for {len(mesh_required_lines)} lines in order {order.name}")
            
            for line in mesh_required_lines:
                try:
                    self._process_mesh_hunting_for_line(line)
                except Exception as e:
                    _logger.error(f"Mesh hunting failed for line {line.id}: {str(e)}")
                    # Continue with other lines rather than failing the entire order
                    
    def _process_mesh_hunting_for_line(self, order_line):
        """Process mesh hunting for a specific order line"""
        config = order_line.config_id
        template = config.template_id

        _logger.info(f"Processing mesh hunting for line {order_line.id}, config {config.id}, template {template.name}")

        # Check if mesh operation already exists (from preview)
        existing_operation = self.env['mesh.cut.operation'].search([
            ('sale_order_line_id', '=', order_line.id)
        ], limit=1)

        if existing_operation:
            _logger.info(f"Found existing mesh operation {existing_operation.id} for line {order_line.id}")
            # Update the operation state to confirmed
            existing_operation.state = 'confirmed'
            existing_operation.selection_reason = 'Confirmed during SO confirmation'
            return

        # Debug: Show config details
        _logger.info(f"[MESH-DEBUG] Config details:")
        _logger.info(f"[MESH-DEBUG]   - Config ID: {config.id}")
        _logger.info(f"[MESH-DEBUG]   - Template ID: {template.id}")
        _logger.info(f"[MESH-DEBUG]   - Template Name: {template.name}")
        _logger.info(f"[MESH-DEBUG]   - Has config_data: {bool(config.config_data)}")
        _logger.info(f"[MESH-DEBUG]   - Config_data preview: {config.config_data[:200] if config.config_data else 'None'}...")

        # Extract mesh requirements from config_data
        mesh_requirements = self._extract_mesh_requirements_from_config(config)

        if not mesh_requirements:
            _logger.warning(f"No mesh requirements found in config {config.id}")
            return

        _logger.info(f"Mesh requirements extracted: {mesh_requirements}")

        # Execute mesh hunting using existing mesh cut operations
        mesh_component = self._hunt_optimal_mesh_component(mesh_requirements)

        if mesh_component:
            _logger.info(f"Optimal mesh component found: {mesh_component.name} (ID: {mesh_component.id})")
            # Apply mesh component to the order line or configuration
            self._assign_mesh_component_to_line(order_line, mesh_component, mesh_requirements)
        else:
            _logger.warning(f"No suitable mesh component found for requirements: {mesh_requirements}")
            
    def _extract_mesh_requirements_from_config(self, config):
        """Extract mesh dimensions and requirements from configuration data"""
        _logger.info(f"[MESH-DEBUG] Starting mesh extraction for config {config.id}")
        _logger.info(f"[MESH-DEBUG] Config has config_data: {bool(config.config_data)}")
        _logger.info(f"[MESH-DEBUG] Config_data length: {len(config.config_data) if config.config_data else 0}")

        if not config.config_data:
            _logger.warning(f"No config_data found for config {config.id}")
            # Try to regenerate calculated fields
            _logger.info(f"[MESH-DEBUG] Attempting to regenerate calculated fields for config {config.id}")
            try:
                config.save_calculated_fields_to_config_data()
                if config.config_data:
                    _logger.info(f"[MESH-DEBUG] Successfully regenerated config_data, length: {len(config.config_data)}")
                else:
                    _logger.warning(f"[MESH-DEBUG] Failed to regenerate config_data for config {config.id}")
                    return None
            except Exception as e:
                _logger.error(f"[MESH-DEBUG] Error regenerating calculated fields: {str(e)}")
                return None
        else:
            # Even if config_data exists, try to update calculated fields to ensure they're current
            _logger.info(f"[MESH-DEBUG] Config has config_data, but forcing recalculation to ensure mesh fields are current")
            try:
                # Force save calculated fields to config_data
                config.save_calculated_fields_to_config_data()
                # Refresh the config to get updated data
                config.refresh()
                _logger.info(f"[MESH-DEBUG] Forced recalculation completed, config_data length now: {len(config.config_data) if config.config_data else 0}")
            except Exception as e:
                _logger.error(f"[MESH-DEBUG] Error in forced recalculation: {str(e)}")

        try:
            import json
            config_data = json.loads(config.config_data) if isinstance(config.config_data, str) else config.config_data
            _logger.info(f"Config data keys for config {config.id}: {list(config_data.keys())}")

            # Log all calculated fields found
            calculated_fields = {k: v for k, v in config_data.items() if k.startswith('_CALCULATED_')}
            _logger.info(f"All calculated fields in config {config.id}: {calculated_fields}")

        except (json.JSONDecodeError, TypeError):
            _logger.error(f"Failed to parse config_data for config {config.id}")
            return None

        # Extract calculated mesh fields first
        mesh_requirements = {}

        # Look for calculated mesh fields as specified in the technical spec
        mesh_fields = [
            '_CALCULATED_mesh_width',
            '_CALCULATED_mesh_height',
            '_CALCULATED_mesh_series',
            '_CALCULATED_mesh_required'
        ]

        _logger.info(f"Looking for mesh fields: {mesh_fields}")

        for field in mesh_fields:
            if field in config_data:
                value = config_data[field]
                mesh_requirements[field.replace('_CALCULATED_mesh_', '')] = value
                _logger.info(f"✅ Found {field} = {value} (type: {type(value)})")
            else:
                _logger.info(f"❌ Missing {field}")

        _logger.info(f"Extracted mesh requirements: {mesh_requirements}")

        # If we don't have calculated mesh fields, try to extract from door dimensions
        if 'width' not in mesh_requirements or 'height' not in mesh_requirements:
            _logger.info(f"No calculated mesh fields found, trying to extract from door dimensions")

            # Try various door dimension field names that might exist
            dimension_fields = [
                'door_width', 'width', 'opening_width', '_CALCULATED_door_width',
                'bx_dbl_hinge_make_left_door_top_width_mm_manual',
                'bx_dbl_hinge_make_right_door_top_width_mm_manual'
            ]

            height_fields = [
                'door_height', 'height', 'opening_height', '_CALCULATED_door_height',
                'bx_dbl_hinge_make_left_door_height_mm_manual',
                'bx_dbl_hinge_make_right_door_height_mm_manual'
            ]

            # Extract width
            for field_name in dimension_fields:
                if field_name in config_data and config_data[field_name]:
                    try:
                        width_value = float(config_data[field_name])
                        if width_value > 0:
                            mesh_requirements['width'] = width_value
                            _logger.info(f"Found width from {field_name}: {width_value}")
                            break
                    except (ValueError, TypeError):
                        continue

            # Extract height
            for field_name in height_fields:
                if field_name in config_data and config_data[field_name]:
                    try:
                        height_value = float(config_data[field_name])
                        if height_value > 0:
                            mesh_requirements['height'] = height_value
                            _logger.info(f"Found height from {field_name}: {height_value}")
                            break
                    except (ValueError, TypeError):
                        continue

            # Set default series if not found - should match one of: saltwater, basix, insect, flyscreen, sandfly
            if 'series' not in mesh_requirements:
                mesh_requirements['series'] = 'saltwater'  # Default to saltwater as most common

            # Set required flag if we have dimensions
            if 'width' in mesh_requirements and 'height' in mesh_requirements:
                mesh_requirements['required'] = True
                _logger.info(f"Successfully extracted mesh requirements from door dimensions")

        # Only return requirements if we have the essential dimensions
        if 'width' in mesh_requirements and 'height' in mesh_requirements:
            _logger.info(f"Final mesh requirements: {mesh_requirements}")
            return mesh_requirements
        else:
            _logger.warning(f"Missing essential mesh dimensions in config {config.id}: {list(mesh_requirements.keys())}")
            return None
    def _search_mesh_by_type(self, width, height, series, mesh_type):
        """Unified search method for mesh products by series and type"""
        _logger.info(f"Searching for {series} {mesh_type} mesh: {width}x{height}")

        # Build search domain using proper mesh product fields
        domain = [
            ('is_mesh_product', '=', True),
            ('qty_available', '>', 0)
        ]

        # Add series filter using mesh_series field
        if series:
            domain.append(('mesh_series', '=', series))

        # Add type filter using mesh_type field
        if mesh_type == 'master':
            domain.append(('mesh_type', '=', 'master'))
        elif mesh_type == 'planned':
            domain.append(('mesh_type', '=', 'planned'))
        elif mesh_type == 'unplanned':
            domain.append(('mesh_type', '=', 'unplanned'))

        products = self.env['product.product'].search(domain)
        _logger.info(f"Found {len(products)} potential {mesh_type} products for {series} series")

        if mesh_type == 'unplanned':
            return self._search_unplanned_with_lots(products, width, height, series)
        else:
            return self._search_dimensioned_products(products, width, height, mesh_type)
    
    def _search_dimensioned_products(self, products, width, height, mesh_type):
        """Search products with dimensional matching"""
        best_match = None
        best_waste = float('inf')
        
        for product in products:
            _logger.info(f"Checking {mesh_type} product: {product.name}")
            product_width, product_height = self._extract_product_dimensions(product)
            
            if product_width and product_height:
                if product_width >= width and product_height >= height:
                    waste = (product_width * product_height) - (width * height)
                    _logger.info(f"{mesh_type.title()} {product.name} fits with waste: {waste}")
                    
                    if mesh_type == 'master' or waste < best_waste:
                        best_waste = waste
                        best_match = product
                        # For master sheets, prefer first suitable match (usually largest)
                        if mesh_type == 'master':
                            break
                else:
                    _logger.info(f"{mesh_type.title()} {product.name} too small: {product_width}x{product_height} < {width}x{height}")
            else:
                # If no dimensions found, consider as potential fallback
                _logger.info(f"No dimensions found for {product.name}, considering as fallback")
                if not best_match:
                    best_match = product
        
        if best_match:
            _logger.info(f"Selected {mesh_type}: {best_match.name} (waste: {best_waste})")
        
        return best_match
        
    def _search_unplanned_with_lots(self, products, width, height, series):
        """Search unplanned off-cuts using stock lots for dimensions"""
        best_match = None
        best_waste = float('inf')
        
        for product in products:
            _logger.info(f"Checking unplanned product: {product.name}")
            
            # Search stock lots for this product
            stock_lots = self.env['stock.lot'].search([
                ('product_id', '=', product.id),
                ('product_qty', '>', 0)
            ])
            
            for lot in stock_lots:
                if series and series.lower() not in lot.name.lower():
                    continue
                    
                lot_width, lot_height = self._extract_lot_dimensions(lot)
                if lot_width and lot_height:
                    if lot_width >= width and lot_height >= height:
                        waste = (lot_width * lot_height) - (width * height)
                        _logger.info(f"Unplanned lot {lot.name} fits with waste: {waste}")
                        if waste < best_waste:
                            best_waste = waste
                            best_match = product
                            
        if best_match:
            _logger.info(f"Selected unplanned off-cut: {best_match.name} (waste: {best_waste})")
            
        return best_match
            
    def _hunt_optimal_mesh_component(self, mesh_requirements):
        """Hunt for optimal mesh component using existing mesh cut operations infrastructure"""
        required_width = float(mesh_requirements.get('width', 0))
        required_height = float(mesh_requirements.get('height', 0))
        series = mesh_requirements.get('series', 'saltwater')
        
        if not required_width or not required_height:
            return None
            
        _logger.info(f"Hunting mesh component for: {required_width}x{required_height}, series: {series}")
        
        # Search strategy: Master sheets first, then planned off-cuts, then unplanned off-cuts
        # Each search now considers mesh series and type

        # 1. Try to find suitable master sheets (best for large requirements)
        mesh_component = self._search_mesh_by_type(required_width, required_height, series, 'master')
        if mesh_component:
            _logger.info(f"Selected master sheet: {mesh_component.name}")
            return mesh_component

        # 2. Try planned off-cuts (good efficiency, known dimensions)
        mesh_component = self._search_mesh_by_type(required_width, required_height, series, 'planned')
        if mesh_component:
            _logger.info(f"Selected planned off-cut: {mesh_component.name}")
            return mesh_component

        # 3. Try unplanned off-cuts with stock lots (utilize all available material)
        mesh_component = self._search_mesh_by_type(required_width, required_height, series, 'unplanned')
        if mesh_component:
            _logger.info(f"Selected unplanned off-cut: {mesh_component.name}")
            return mesh_component
            
        # 4. If no exact series match, try other series as fallback
        fallback_series = ['saltwater', 'basix', 'flyscreen', 'insect', 'sandfly']
        for fallback in fallback_series:
            if fallback != series:  # Don't search the same series again
                _logger.info(f"Trying fallback series: {fallback}")
                mesh_component = self._search_mesh_by_type(required_width, required_height, fallback, 'master')
                if mesh_component:
                    _logger.info(f"Selected fallback {fallback} master: {mesh_component.name}")
                    return mesh_component
                    
        return None
        
    def _search_mesh_master_sheets(self, width, height, series):
        """Search for suitable master mesh sheets"""
        # Look for master mesh products that can accommodate the required dimensions
        # Use the proper series and type structure
        domain = [
            '|', 
            ('categ_id.name', 'ilike', 'mesh'),
            ('name', 'ilike', 'mesh'),
            '|',
            ('name', 'ilike', 'master sheet'),
            ('name', 'ilike', 'master'),
            ('qty_available', '>', 0)
        ]
        
        # Add series filter if specified
        if series and series.lower() != 'none':
            domain.append(('name', 'ilike', series))
            
        master_products = self.env['product.product'].search(domain)
        _logger.info(f"Found {len(master_products)} potential master mesh products for series '{series}'")
        
        best_match = None
        best_waste = float('inf')
        
        for product in master_products:
            _logger.info(f"Checking master product: {product.name}")
            # Extract dimensions from product name
            product_width, product_height = self._extract_product_dimensions(product)
            if product_width and product_height:
                if product_width >= width and product_height >= height:
                    waste = (product_width * product_height) - (width * height)
                    _logger.info(f"Master sheet {product.name} fits: {product_width}x{product_height} (waste: {waste})")
                    if waste < best_waste:
                        best_waste = waste
                        best_match = product
                else:
                    _logger.info(f"Master sheet too small: {product.name} ({product_width}x{product_height}) < required ({width}x{height})")
            else:
                # If no dimensions found in name, consider as fallback
                _logger.info(f"No dimensions found for {product.name}, considering as fallback")
                if not best_match:
                    best_match = product
        
        if best_match:
            _logger.info(f"Selected master sheet: {best_match.name} (waste: {best_waste})")
                    
        return best_match
        
    def _search_mesh_planned_offcuts(self, width, height, series):
        """Search for suitable planned off-cuts"""
        domain = [
            '|',
            ('categ_id.name', 'ilike', 'mesh'),
            ('name', 'ilike', 'mesh'),
            '|',
            ('name', 'ilike', 'planned off-cut'),
            ('name', 'ilike', 'offcut'),
            ('qty_available', '>', 0)
        ]
        
        # Add series filter if specified
        if series and series.lower() != 'none':
            domain.append(('name', 'ilike', series))
            
        offcut_products = self.env['product.product'].search(domain)
        _logger.info(f"Found {len(offcut_products)} potential planned off-cut products for series '{series}'")
        
        best_match = None
        best_waste = float('inf')
        
        for product in offcut_products:
            _logger.info(f"Checking planned off-cut product: {product.name}")
            product_width, product_height = self._extract_product_dimensions(product)
            if product_width and product_height:
                if product_width >= width and product_height >= height:
                    waste = (product_width * product_height) - (width * height)
                    _logger.info(f"Planned off-cut {product.name} fits with waste: {waste}")
                    if waste < best_waste:
                        best_waste = waste
                        best_match = product
                else:
                    _logger.info(f"Planned off-cut too small: {product.name} ({product_width}x{product_height}) < required ({width}x{height})")
            else:
                # If no dimensions, consider as potential match
                _logger.info(f"No dimensions found for {product.name}, considering as potential match")
                if not best_match:
                    best_match = product
                        
        if best_match:
            _logger.info(f"Selected planned off-cut: {best_match.name} (waste: {best_waste})")
            
        return best_match
        
    def _search_mesh_unplanned_offcuts(self, width, height, series):
        """Search for suitable unplanned off-cuts using stock lots"""
        # Search stock lots for unplanned mesh pieces
        stock_lots = self.env['stock.lot'].search([
            ('product_id.categ_id.name', 'ilike', 'mesh'),
            ('name', 'ilike', 'unplanned'),
            ('product_qty', '>', 0)
        ])
        
        best_match = None
        best_waste = float('inf')
        
        for lot in stock_lots:
            if series and series.lower() not in lot.name.lower():
                continue
                
            lot_width, lot_height = self._extract_lot_dimensions(lot)
            if lot_width and lot_height:
                if lot_width >= width and lot_height >= height:
                    waste = (lot_width * lot_height) - (width * height)
                    if waste < best_waste:
                        best_waste = waste
                        best_match = lot.product_id
                        
        if best_match:
            _logger.info(f"Found suitable unplanned off-cut: {best_match.name} (waste: {best_waste})")
            
        return best_match
        
    def _extract_product_dimensions(self, product):
        """Extract width and height from mesh product fields or product name"""
        # First try to use the proper mesh dimension fields
        if hasattr(product, 'mesh_width') and hasattr(product, 'mesh_height'):
            if product.mesh_width and product.mesh_height:
                return product.mesh_width, product.mesh_height

        # Fallback to extracting dimensions from product name (e.g., "1100x620")
        import re
        match = re.search(r'(\d+)x(\d+)', product.name)
        if match:
            return float(match.group(1)), float(match.group(2))

        return None, None
        
    def _extract_lot_dimensions(self, lot):
        """Extract width and height from lot dimension fields or lot name"""
        # First try to use the proper lot dimension fields
        if hasattr(lot, 'mesh_width') and hasattr(lot, 'mesh_height'):
            if lot.mesh_width and lot.mesh_height:
                return lot.mesh_width, lot.mesh_height

        # Fallback to extracting dimensions from lot name (e.g., "450x380")
        import re
        match = re.search(r'(\d+)x(\d+)', lot.name)
        if match:
            return float(match.group(1)), float(match.group(2))

        return None, None
        
    def _assign_mesh_component_to_line(self, order_line, mesh_component, mesh_requirements):
        """Assign the selected mesh component to the order line"""
        _logger.info(f"Assigning mesh component {mesh_component.name} to order line {order_line.id}")
        
        # Create a mesh cut operation for this line
        mesh_operation = self.env['mesh.cut.operation'].create({
            'name': f'Mesh Cut - {order_line.product_id.name}',
            'sale_order_id': self.id,  # Link to the sale order
            'sale_order_line_id': order_line.id,
            'mesh_product_id': mesh_component.id,
            'required_width': mesh_requirements.get('width'),
            'required_height': mesh_requirements.get('height'),
            'required_qty': 1.0,
            'mesh_series': mesh_requirements.get('series', 'saltwater'),
            'state': 'draft',  # Start in draft state so auto-find can work
        })
        
        _logger.info(f"Created mesh cut operation {mesh_operation.id} for line {order_line.id}")

        # Auto-find best mesh for this operation
        try:
            mesh_operation.action_find_mesh()
            mesh_operation.state = 'auto_selected'
            mesh_operation.selection_reason = 'Auto-selected during SO confirmation'
            _logger.info(f"Auto-found mesh for operation {mesh_operation.id}")
        except Exception as e:
            _logger.warning(f"Could not auto-find mesh for operation {mesh_operation.id}: {str(e)}")
            # Keep in draft state so user can manually find mesh

        # Update the order line with mesh operation reference
        order_line.mesh_operation_id = mesh_operation.id
        
    def action_manage_mesh_components(self):
        """Open interface to manually manage mesh components for this order"""
        self.ensure_one()

        # If in review state and no mesh operations exist, create preview operations
        if self.state == 'review':
            existing_operations = self.env['mesh.cut.operation'].search([
                ('sale_order_line_id.order_id', '=', self.id)
            ])
            if not existing_operations:
                self._create_preview_mesh_operations()

        return {
            'name': _('Manage Mesh Components'),
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.operation',
            'view_mode': 'list,form',
            'domain': [('sale_order_line_id.order_id', '=', self.id)],
            'context': {
                'default_sale_order_id': self.id,
                'search_default_sale_order': True
            },
        }

    def _create_preview_mesh_operations(self):
        """Create preview mesh operations for review state (before confirmation)"""
        for order in self:
            configured_lines = order.order_line.filtered(lambda l: l.is_configured and l.config_id)

            for line in configured_lines:
                # Extract mesh requirements from configuration
                mesh_requirements = self._extract_mesh_requirements_from_config(line.config_id)
                if not mesh_requirements:
                    continue

                # Check if mesh is actually required
                if not mesh_requirements.get('required', False):
                    continue

                # Create preview mesh operation (in draft state)
                mesh_operation = self.env['mesh.cut.operation'].create({
                    'name': f'Preview Mesh Cut - {line.product_id.name}',
                    'sale_order_id': order.id,
                    'sale_order_line_id': line.id,
                    'required_width': mesh_requirements.get('width'),
                    'required_height': mesh_requirements.get('height'),
                    'required_qty': 1.0,
                    'mesh_series': mesh_requirements.get('series', 'saltwaterseries'),
                    'state': 'draft',  # Keep in draft for manual review
                })

                # Try to auto-find mesh but don't fail if not found
                try:
                    mesh_operation.action_find_mesh()
                    mesh_operation.selection_reason = 'Auto-preview during review'
                except Exception as e:
                    _logger.info(f"Could not auto-find mesh for preview operation {mesh_operation.id}: {str(e)}")

                # Link the operation to the order line
                line.mesh_operation_id = mesh_operation.id

    def action_view_boms(self):
        """
        View all BOMs related to configured products in this order.

        This method opens a view showing all BOMs that were generated
        from the configured products in this sales order.

        Returns:
            dict: Action to open the BOM list view filtered to show only BOMs from this order
        """
        self.ensure_one()

        # Get all configurations with BOMs
        configs_with_boms = self.order_line.mapped('config_id').filtered(lambda c: c.bom_id)
        bom_ids = configs_with_boms.mapped('bom_id').ids

        if not bom_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No BOMs Available'),
                    'message': _('No Bills of Materials have been generated for the configured products in this order.'),
                    'type': 'warning',
                }
            }

        # Return action to view BOMs
        return {
            'name': _('Bills of Materials'),
            'type': 'ir.actions.act_window',
            'res_model': 'mrp.bom',
            'view_mode': 'list,form',
            'domain': [('id', 'in', bom_ids)],
            'context': {'create': False},
        }

    def has_boms(self):
        """
        Check if this order has any BOMs from configured products.

        Returns:
            bool: True if at least one configured product has a BOM, False otherwise
        """
        return bool(self.order_line.mapped('config_id').filtered(lambda c: c.bom_id))

    def _validate_configurable_products_boms(self):
        """
        Validate that all configurable products with MTO+Manufacturing routes have valid BOMs.
        """
        for order in self:
            lines_requiring_boms = order.order_line.filtered(lambda l: l._requires_configured_bom())

            if not lines_requiring_boms:
                continue

            validation_errors = []
            for line in lines_requiring_boms:
                try:
                    line._validate_configured_bom()
                except ValidationError as e:
                    validation_errors.append(str(e))

            if validation_errors:
                error_message = self.env._(
                    "Cannot confirm Sale Order %s due to missing BOMs for configurable products:\n\n%s\n\n"
                    "Please ensure all configurable products are properly configured with valid BOMs before confirming the order."
                ) % (order.name, '\n'.join(validation_errors))

                raise ValidationError(error_message)
