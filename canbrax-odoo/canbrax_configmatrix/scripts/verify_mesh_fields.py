#!/usr/bin/env python3
"""
Verification script to check if mesh calculated fields were created properly.
Run this in Odoo shell to verify the mesh integration setup.
"""

def verify_mesh_fields():
    """Verify that mesh calculated fields are properly loaded"""
    
    # Expected mesh fields
    expected_fields = [
        '_CALCULATED_mesh_required',
        '_CALCULATED_mesh_width', 
        '_CALCULATED_mesh_height',
        '_CALCULATED_mesh_series',
        '_CALCULATED_mesh_area',
        '_CALCULATED_mesh_complexity',
        '_CALCULATED_mesh_operation_required',
        '_CALCULATED_mesh_operation_type',
        '_CALCULATED_mesh_area_m2',
        '_CALCULATED_mesh_perimeter',
        '_CALCULATED_mesh_aspect_ratio',
        '_CALCULATED_mesh_size_category'
    ]
    
    print("🔍 Verifying Mesh Calculated Fields...")
    print("=" * 50)
    
    # Check calculated fields
    calc_field_model = env['config.matrix.calculated.field']
    found_fields = []
    missing_fields = []
    
    for field_name in expected_fields:
        field = calc_field_model.search([('name', '=', field_name)], limit=1)
        if field:
            found_fields.append(field_name)
            print(f"✅ {field_name}")
        else:
            missing_fields.append(field_name)
            print(f"❌ {field_name} - MISSING")
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {len(found_fields)}/{len(expected_fields)} fields found")
    
    if missing_fields:
        print(f"❌ Missing fields: {len(missing_fields)}")
        for field in missing_fields:
            print(f"   - {field}")
        print("\n💡 To fix: Update the module or check data file loading")
    else:
        print("✅ All mesh calculated fields are properly loaded!")
    
    # Check operation templates
    print("\n🔧 Verifying Mesh Operation Templates...")
    print("=" * 50)
    
    op_template_model = env['config.matrix.operation.template']
    mesh_templates = op_template_model.search([('is_mesh_operation', '=', True)])
    
    if mesh_templates:
        print(f"✅ Found {len(mesh_templates)} mesh operation templates:")
        for template in mesh_templates:
            print(f"   - {template.name} ({template.mesh_operation_type})")
    else:
        print("❌ No mesh operation templates found")
        print("💡 Check if mesh_cut_operation_template.xml was loaded")
    
    # Test field calculation
    print("\n🧪 Testing Field Calculations...")
    print("=" * 50)
    
    if found_fields:
        test_values = {
            'mesh_type': 'saltwater',
            '_CALCULATED_largest_door_width': 600,
            '_CALCULATED_largest_door_height': 400,
            '_CALCULATED_door_width': 600,
            '_CALCULATED_door_height': 400
        }
        
        try:
            results = calc_field_model.calculate_fields(test_values)
            
            key_results = {
                '_CALCULATED_mesh_required': results.get('_CALCULATED_mesh_required'),
                '_CALCULATED_mesh_width': results.get('_CALCULATED_mesh_width'),
                '_CALCULATED_mesh_height': results.get('_CALCULATED_mesh_height'),
                '_CALCULATED_mesh_series': results.get('_CALCULATED_mesh_series'),
                '_CALCULATED_mesh_operation_required': results.get('_CALCULATED_mesh_operation_required')
            }
            
            print("✅ Field calculation test successful:")
            for field, value in key_results.items():
                if field in found_fields:
                    print(f"   {field}: {value}")
                    
        except Exception as e:
            print(f"❌ Field calculation test failed: {str(e)}")
    
    print("\n" + "=" * 50)
    if not missing_fields and mesh_templates:
        print("🎉 Mesh integration is ready to use!")
        print("💡 Create a configuration with mesh_type field to test")
    else:
        print("⚠️  Mesh integration setup incomplete")
        print("💡 Update the module to load missing components")

# Run verification if executed directly
if __name__ == '__main__':
    verify_mesh_fields()
