# Task 1: Enhance Template Model

**Phase**: 1 - Core Functionality  
**Priority**: High  
**Dependencies**: None  
**Estimated Time**: 2-3 hours

## Overview
Add new fields to the `config.matrix.template` model to support individual mesh panel hunting with different calculation methods and hunting priorities.

## Files to Modify
- `canbrax_configmatrix/models/config_matrix_template.py`

## Implementation Details

### New Fields to Add

Add these fields after the existing mesh fields in the template model:

```python
# Panel Calculation Method
mesh_panel_calculation_method = fields.Selection([
    ('simple_count', 'Simple Panel Count'),
    ('midrail_split', 'Midrail Split Calculation'),
    ('custom_formula', 'Custom Formula')
], string="Panel Calculation Method", default='midrail_split',
   help="Method to calculate mesh panel dimensions")

# Simple Count Formula
mesh_panel_count_formula = fields.Char(
    "Panel Count Formula", 
    default="1",
    help="Simple count formula when method is 'simple_count'"
)

# Custom Dimensions Formula
mesh_panel_dimensions_formula = fields.Text(
    "Panel Dimensions Formula",
    help="Python code to return list of panel dimensions when method is 'custom_formula'.\n"
         "Return format: [{'width': 500, 'height': 600, 'name': 'Top Panel'}, {'width': 500, 'height': 400, 'name': 'Bottom Panel'}]\n"
         "Available variables: door_width, door_height, midrail_position, is_double_door, config_values"
)

# Individual Hunting Control
hunt_each_panel_separately = fields.Boolean(
    "Hunt Each Panel Separately",
    default=True,
    help="When enabled, each mesh panel will be hunted individually, allowing different sources for each panel"
)

# Hunting Priority
mesh_hunting_priority = fields.Selection([
    ('unplanned_planned_master', 'Unplanned → Planned → Master'),
    ('planned_unplanned_master', 'Planned → Unplanned → Master'),
    ('master_only', 'Master Sheets Only'),
    ('offcuts_only', 'Offcuts Only (Unplanned + Planned)')
], string="Mesh Hunting Priority", default='unplanned_planned_master',
   help="Priority order for hunting mesh panels")
```

## Field Descriptions

### mesh_panel_calculation_method
- **Purpose**: Determines how panel dimensions are calculated
- **Options**:
  - `simple_count`: Use a simple formula to determine number of identical panels
  - `midrail_split`: Automatically split panels based on midrail position (default)
  - `custom_formula`: Use custom Python code for complex calculations

### mesh_panel_count_formula
- **Purpose**: Simple formula for panel count when using 'simple_count' method
- **Default**: "1" (single panel)
- **Examples**: "2 if door_type == 'double' else 1"

### mesh_panel_dimensions_formula
- **Purpose**: Custom Python code for complex panel calculations
- **Return Format**: List of dictionaries with panel specifications
- **Available Variables**: door_width, door_height, midrail_position, is_double_door, config_values

### hunt_each_panel_separately
- **Purpose**: Enable/disable individual panel hunting
- **Default**: True
- **Impact**: When enabled, each panel is hunted separately for optimal material usage

### mesh_hunting_priority
- **Purpose**: Define the order in which sources are searched
- **Default**: 'unplanned_planned_master' (most efficient order)
- **Options**: Different priority sequences for various business needs

## Testing Checklist

- [ ] Fields are added to the model without errors
- [ ] Default values are set correctly
- [ ] Field help text is informative
- [ ] Selection options are properly defined
- [ ] Model can be saved and loaded without issues

## Next Steps
After completing this task:
1. Proceed to Task 2 (Template Form View updates)
2. Test field creation in Odoo interface
3. Verify field defaults and help text display correctly

## Notes
- These fields extend the existing mesh functionality
- Backward compatibility is maintained with default values
- The midrail_split method will be the most commonly used option
