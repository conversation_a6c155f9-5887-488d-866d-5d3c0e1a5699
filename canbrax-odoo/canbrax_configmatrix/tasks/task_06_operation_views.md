# Task 6: Update Mesh Cut Operation Views

**Phase**: 2 - Enhanced Hunting  
**Priority**: Medium  
**Dependencies**: Task 4 (Hunting Logic), Task 5 (Offcut Model)  
**Estimated Time**: 2-3 hours

## Overview
Update the mesh cut operation views to display the new panel tracking fields and hunting results, making it easy to see individual panel information and hunting outcomes.

## Files to Modify
- `canbrax_configmatrix/views/mesh_cut_operation_views.xml`

## Implementation Details

### Form View Enhancement

Add these fields to the existing form view:

```xml
<record id="view_mesh_cut_operation_form_enhanced" model="ir.ui.view">
    <field name="name">mesh.cut.operation.form.enhanced</field>
    <field name="model">mesh.cut.operation</field>
    <field name="inherit_id" ref="view_mesh_cut_operation_form"/>
    <field name="arch" type="xml">
        <!-- Add panel identification fields -->
        <field name="required_qty" position="after">
            <field name="panel_number"/>
            <field name="panel_position"/>
            <field name="door_number"/>
        </field>
        
        <!-- Add hunting configuration and results -->
        <field name="mesh_series" position="after">
            <field name="hunting_priority"/>
            <field name="mesh_source_type" readonly="1"/>
            <field name="selected_mesh_id" readonly="1"/>
        </field>
        
        <!-- Add hunting notes -->
        <xpath expr="//notebook" position="inside">
            <page string="Hunting Details">
                <group>
                    <field name="hunting_notes" widget="text" readonly="1" nolabel="1"/>
                </group>
            </page>
        </xpath>
    </field>
</record>
```

### List View Enhancement

Update the list view to show panel information:

```xml
<record id="view_mesh_cut_operation_list_enhanced" model="ir.ui.view">
    <field name="name">mesh.cut.operation.list.enhanced</field>
    <field name="model">mesh.cut.operation</field>
    <field name="inherit_id" ref="view_mesh_cut_operation_tree"/>
    <field name="arch" type="xml">
        <!-- Add panel tracking columns -->
        <field name="name" position="after">
            <field name="panel_number" string="Panel #"/>
            <field name="panel_position" string="Position"/>
            <field name="door_number" string="Door #"/>
        </field>
        
        <!-- Add hunting result columns -->
        <field name="mesh_series" position="after">
            <field name="mesh_source_type" string="Source Type"/>
            <field name="selected_mesh_id" string="Selected Mesh"/>
        </field>
    </field>
</record>
```

### Search View Enhancement

Add filters for the new fields:

```xml
<record id="view_mesh_cut_operation_search_enhanced" model="ir.ui.view">
    <field name="name">mesh.cut.operation.search.enhanced</field>
    <field name="model">mesh.cut.operation</field>
    <field name="inherit_id" ref="view_mesh_cut_operation_search"/>
    <field name="arch" type="xml">
        <!-- Add panel position filters -->
        <filter string="Series" position="after">
            <separator/>
            <filter string="Full Panels" name="full_panels" domain="[('panel_position', '=', 'full')]"/>
            <filter string="Top Panels" name="top_panels" domain="[('panel_position', '=', 'top')]"/>
            <filter string="Bottom Panels" name="bottom_panels" domain="[('panel_position', '=', 'bottom')]"/>
        </filter>
        
        <!-- Add source type filters -->
        <filter string="Bottom Panels" position="after">
            <separator/>
            <filter string="From Unplanned Offcuts" name="unplanned_source" 
                    domain="[('mesh_source_type', '=', 'unplanned_offcut')]"/>
            <filter string="From Planned Offcuts" name="planned_source" 
                    domain="[('mesh_source_type', '=', 'planned_offcut')]"/>
            <filter string="From Master Sheets" name="master_source" 
                    domain="[('mesh_source_type', '=', 'master_sheet')]"/>
            <filter string="Not Found" name="not_found" 
                    domain="[('mesh_source_type', '=', 'not_found')]"/>
        </filter>
        
        <!-- Add group by options -->
        <filter string="Template" position="after">
            <filter string="Panel Position" name="group_panel_position" 
                    context="{'group_by': 'panel_position'}"/>
            <filter string="Door Number" name="group_door_number" 
                    context="{'group_by': 'door_number'}"/>
            <filter string="Source Type" name="group_source_type" 
                    context="{'group_by': 'mesh_source_type'}"/>
        </filter>
    </field>
</record>
```

## Field Layout Strategy

### Form View Organization

#### Basic Information Group
- Keep existing required dimensions
- Add panel identification fields (number, position, door)
- Show hunting priority setting

#### Hunting Results Group
- Display source type with color coding
- Show selected mesh product
- Make fields readonly to prevent manual changes

#### Hunting Details Tab
- Full hunting notes in text widget
- Readonly to preserve hunting log
- Helpful for debugging and analysis

### List View Columns

#### Panel Identification
- Panel # (panel_number): Quick identification
- Position (panel_position): Top/Bottom/Full indication
- Door # (door_number): Multi-door support

#### Hunting Results
- Source Type: Where mesh was found
- Selected Mesh: Which product was selected

### Search and Filtering

#### Panel Filters
- Filter by panel position (full, top, bottom)
- Useful for analyzing midrail configurations

#### Source Filters
- Filter by hunting result type
- Identify patterns in material usage
- Find operations that couldn't find mesh

#### Grouping Options
- Group by panel position for analysis
- Group by door number for multi-door orders
- Group by source type for material usage analysis

## Visual Enhancements

### Status Indicators
Consider adding visual indicators for hunting results:
- Green: Successfully found mesh
- Yellow: Found but low efficiency
- Red: No mesh found

### Field Styling
- Use badges for source type display
- Color-code panel positions
- Highlight failed hunts

## User Experience Improvements

### Progressive Disclosure
- Show hunting details only when relevant
- Hide empty fields to reduce clutter
- Use tabs to organize information

### Contextual Information
- Show efficiency calculations where available
- Display hunting timestamp
- Link to source records (offcuts, master sheets)

## Testing Scenarios

### Single Panel Operation
- **Setup**: Single door, no midrail
- **Expected**: Panel #1, Position: Full, Door #1

### Multi-Panel Operation
- **Setup**: Single door with midrail
- **Expected**: Panel #1 (Top), Panel #2 (Bottom), both Door #1

### Double Door Operation
- **Setup**: Double door with midrail
- **Expected**: 4 panels with correct door numbers and positions

### Hunting Results Display
- **Setup**: Various hunting outcomes
- **Expected**: Clear display of source types and selected mesh

## Testing Checklist

- [ ] Form view displays new fields correctly
- [ ] List view shows panel information
- [ ] Search filters work as expected
- [ ] Grouping options function properly
- [ ] Hunting notes display correctly
- [ ] Field visibility rules work
- [ ] View inheritance doesn't break existing functionality

## Next Steps
After completing this task:
1. Test views with sample data
2. Verify field visibility and readonly behavior
3. Proceed to Task 7 (MRP Integration)

## Notes
- Ensure view inheritance IDs match existing views
- Test with both single and multi-panel operations
- Consider adding custom widgets for better visualization
- Maintain backward compatibility with existing operations
