# Task 2: Update Template Form View

**Phase**: 1 - Core Functionality  
**Priority**: High  
**Dependencies**: Task 1 (Template Model)  
**Estimated Time**: 1-2 hours

## Overview
Update the template form view to display the new mesh panel calculation fields with proper visibility conditions and user-friendly layout.

## Files to Modify
- `canbrax_configmatrix/views/config_matrix_template_views.xml`

## Implementation Details

### View Updates Required

Add these field groups after the existing mesh_series field in the template form view:

```xml
<!-- Panel Calculation Configuration -->
<group string="Panel Calculation" col="2">
    <field name="mesh_panel_calculation_method" 
           invisible="mesh_required == False"/>
    <field name="hunt_each_panel_separately" 
           invisible="mesh_required == False"/>
    <field name="mesh_hunting_priority" 
           invisible="mesh_required == False or hunt_each_panel_separately == False"/>
</group>

<!-- Panel Formulas Configuration -->
<group string="Panel Formulas" col="1">
    <field name="mesh_panel_count_formula" 
           invisible="mesh_required == False or mesh_panel_calculation_method != 'simple_count'"
           placeholder="e.g., 2 if door_type == 'double' else 1"/>
    <field name="mesh_panel_dimensions_formula" 
           invisible="mesh_required == False or mesh_panel_calculation_method != 'custom_formula'"
           widget="ace" options="{'mode': 'python'}"
           placeholder="# Return list of panel dimensions&#10;panels = []&#10;if midrail_position > 0:&#10;    panels.append({'width': door_width, 'height': midrail_position, 'name': 'Top Panel'})&#10;    panels.append({'width': door_width, 'height': door_height - midrail_position, 'name': 'Bottom Panel'})&#10;return panels"/>
</group>
```

## Field Layout Strategy

### Panel Calculation Group
- **Layout**: 2 columns for compact display
- **Visibility**: Only shown when mesh is required
- **Fields**:
  - Calculation method (dropdown)
  - Individual hunting toggle (checkbox)
  - Hunting priority (dropdown, only when individual hunting enabled)

### Panel Formulas Group
- **Layout**: 1 column for better formula editing
- **Visibility**: Context-sensitive based on calculation method
- **Fields**:
  - Simple count formula (text input with placeholder)
  - Custom dimensions formula (code editor with Python syntax highlighting)

## Visibility Logic

### mesh_panel_calculation_method
- **Condition**: `mesh_required == False`
- **Logic**: Only show when mesh is required for the template

### hunt_each_panel_separately
- **Condition**: `mesh_required == False`
- **Logic**: Only relevant when mesh is required

### mesh_hunting_priority
- **Condition**: `mesh_required == False or hunt_each_panel_separately == False`
- **Logic**: Only show when mesh required AND individual hunting enabled

### mesh_panel_count_formula
- **Condition**: `mesh_required == False or mesh_panel_calculation_method != 'simple_count'`
- **Logic**: Only show for simple count method

### mesh_panel_dimensions_formula
- **Condition**: `mesh_required == False or mesh_panel_calculation_method != 'custom_formula'`
- **Logic**: Only show for custom formula method

## Widget Configuration

### Code Editor for Custom Formula
- **Widget**: `ace` (Advanced Code Editor)
- **Options**: `{'mode': 'python'}` for Python syntax highlighting
- **Placeholder**: Multi-line example showing proper return format

### Placeholders
- **Simple Count**: Example showing conditional logic
- **Custom Formula**: Complete example with midrail split logic

## User Experience Considerations

### Progressive Disclosure
- Fields appear/disappear based on selections
- Reduces cognitive load by showing only relevant options
- Clear visual grouping of related fields

### Help Text Integration
- Field help text provides context
- Placeholders show practical examples
- Code editor makes formula editing easier

## Testing Checklist

- [ ] Fields appear correctly in form view
- [ ] Visibility conditions work as expected
- [ ] Code editor loads and functions properly
- [ ] Placeholders display helpful examples
- [ ] Field grouping is logical and clear
- [ ] Form saves without validation errors

## Next Steps
After completing this task:
1. Test the form view in Odoo interface
2. Verify all visibility conditions work correctly
3. Proceed to Task 3 (Panel Dimension Calculation)

## Notes
- Use proper XML escaping for placeholder text (&#10; for newlines)
- Test with different mesh_required values to verify visibility
- Ensure code editor widget is available in your Odoo version
