# Task 5: Add Mesh Offcut Model

**Phase**: 2 - Enhanced Hunting  
**Priority**: High  
**Dependencies**: None (can be done in parallel with Task 4)  
**Estimated Time**: 3-4 hours

## Overview
Create a new model to track mesh offcuts (both planned and unplanned) with their current dimensions, availability status, and usage tracking.

## Files to Create
- `canbrax_configmatrix/models/mesh_offcut.py`
- `canbrax_configmatrix/views/mesh_offcut_views.xml`

## Files to Modify
- `canbrax_configmatrix/models/__init__.py`
- `canbrax_configmatrix/security/ir.model.access.csv`

## Implementation Details

### Model Definition

Create `canbrax_configmatrix/models/mesh_offcut.py`:

```python
from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class MeshOffcut(models.Model):
    _name = 'mesh.offcut'
    _description = 'Mesh Offcut Tracking'
    _order = 'create_date desc'

    name = fields.Char("Offcut Name", required=True)
    product_id = fields.Many2one('product.product', string="Mesh Product", required=True)
    mesh_series = fields.Char("Mesh Series", required=True)
    
    # Original dimensions
    original_width = fields.Float("Original Width (mm)", required=True)
    original_height = fields.Float("Original Height (mm)", required=True)
    
    # Current dimensions
    current_width = fields.Float("Current Width (mm)", required=True)
    current_height = fields.Float("Current Height (mm)", required=True)
    
    # Offcut type
    is_planned_offcut = fields.Boolean("Is Planned Offcut", default=False)
    is_master_sheet = fields.Boolean("Is Master Sheet", default=False)
    
    # Status
    state = fields.Selection([
        ('available', 'Available'),
        ('reserved', 'Reserved'),
        ('used', 'Used'),
        ('scrapped', 'Scrapped')
    ], string="State", default='available')
    
    # Tracking
    location_id = fields.Many2one('stock.location', string="Location")
    notes = fields.Text("Notes")
    
    # Usage tracking
    used_in_operation_ids = fields.One2many('mesh.cut.operation', 'source_offcut_id', string="Used In Operations")
    
    # Computed fields
    area_mm2 = fields.Float("Area (mm²)", compute='_compute_area', store=True)
    area_m2 = fields.Float("Area (m²)", compute='_compute_area', store=True)
    
    @api.depends('current_width', 'current_height')
    def _compute_area(self):
        for offcut in self:
            area_mm2 = offcut.current_width * offcut.current_height
            offcut.area_mm2 = area_mm2
            offcut.area_m2 = area_mm2 / 1000000  # Convert to m²
    
    @api.model
    def find_suitable_offcuts(self, required_width, required_height, mesh_series, offcut_type='any'):
        """Find suitable offcuts for given dimensions"""
        domain = [
            ('mesh_series', '=', mesh_series),
            ('current_width', '>=', required_width),
            ('current_height', '>=', required_height),
            ('state', '=', 'available')
        ]
        
        if offcut_type == 'planned':
            domain.append(('is_planned_offcut', '=', True))
        elif offcut_type == 'unplanned':
            domain.append(('is_planned_offcut', '=', False))
        
        return self.search(domain, order='current_width asc, current_height asc')
    
    def reserve_for_operation(self, operation_id):
        """Reserve this offcut for a specific operation"""
        self.ensure_one()
        if self.state == 'available':
            self.write({
                'state': 'reserved',
                'notes': f"{self.notes or ''}\nReserved for operation {operation_id}".strip()
            })
            return True
        return False
    
    def mark_as_used(self, operation_id):
        """Mark offcut as used"""
        self.ensure_one()
        self.write({
            'state': 'used',
            'notes': f"{self.notes or ''}\nUsed in operation {operation_id}".strip()
        })
    
    def action_make_available(self):
        """Make offcut available again"""
        self.ensure_one()
        if self.state in ['reserved', 'scrapped']:
            self.write({'state': 'available'})
    
    def action_scrap(self):
        """Mark offcut as scrapped"""
        self.ensure_one()
        self.write({'state': 'scrapped'})
```

### View Definition

Create `canbrax_configmatrix/views/mesh_offcut_views.xml`:

```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Mesh Offcut List View -->
    <record id="view_mesh_offcut_list" model="ir.ui.view">
        <field name="name">mesh.offcut.list</field>
        <field name="model">mesh.offcut</field>
        <field name="arch" type="xml">
            <list string="Mesh Offcuts" decoration-success="state == 'available'"
                  decoration-warning="state == 'reserved'" decoration-muted="state in ('used', 'scrapped')">
                <field name="name"/>
                <field name="mesh_series"/>
                <field name="current_width"/>
                <field name="current_height"/>
                <field name="area_m2" string="Area (m²)"/>
                <field name="is_planned_offcut"/>
                <field name="state" widget="badge"/>
                <field name="location_id"/>
            </list>
        </field>
    </record>

    <!-- Mesh Offcut Form View -->
    <record id="view_mesh_offcut_form" model="ir.ui.view">
        <field name="name">mesh.offcut.form</field>
        <field name="model">mesh.offcut</field>
        <field name="arch" type="xml">
            <form string="Mesh Offcut">
                <header>
                    <button name="action_make_available" type="object" string="Make Available"
                            class="btn-primary" invisible="state == 'available'"/>
                    <button name="action_scrap" type="object" string="Scrap"
                            class="btn-secondary" invisible="state == 'scrapped'"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1><field name="name"/></h1>
                    </div>
                    <group>
                        <group string="Basic Information">
                            <field name="product_id"/>
                            <field name="mesh_series"/>
                            <field name="location_id"/>
                        </group>
                        <group string="Type">
                            <field name="is_planned_offcut"/>
                            <field name="is_master_sheet"/>
                        </group>
                    </group>
                    <group>
                        <group string="Original Dimensions">
                            <field name="original_width"/>
                            <field name="original_height"/>
                        </group>
                        <group string="Current Dimensions">
                            <field name="current_width"/>
                            <field name="current_height"/>
                            <field name="area_mm2"/>
                            <field name="area_m2"/>
                        </group>
                    </group>
                    <group string="Notes">
                        <field name="notes" nolabel="1"/>
                    </group>
                    <notebook>
                        <page string="Usage History">
                            <field name="used_in_operation_ids">
                                <list string="Operations">
                                    <field name="name"/>
                                    <field name="required_width"/>
                                    <field name="required_height"/>
                                    <field name="state"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Mesh Offcut Search View -->
    <record id="view_mesh_offcut_search" model="ir.ui.view">
        <field name="name">mesh.offcut.search</field>
        <field name="model">mesh.offcut</field>
        <field name="arch" type="xml">
            <search string="Search Mesh Offcuts">
                <field name="name"/>
                <field name="mesh_series"/>
                <field name="product_id"/>
                <separator/>
                <filter string="Available" name="available" domain="[('state', '=', 'available')]"/>
                <filter string="Reserved" name="reserved" domain="[('state', '=', 'reserved')]"/>
                <filter string="Used" name="used" domain="[('state', '=', 'used')]"/>
                <separator/>
                <filter string="Planned Offcuts" name="planned" domain="[('is_planned_offcut', '=', True)]"/>
                <filter string="Unplanned Offcuts" name="unplanned" domain="[('is_planned_offcut', '=', False)]"/>
                <filter string="Master Sheets" name="master" domain="[('is_master_sheet', '=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="Mesh Series" name="group_series" context="{'group_by': 'mesh_series'}"/>
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Type" name="group_type" context="{'group_by': 'is_planned_offcut'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Mesh Offcut Action -->
    <record id="action_mesh_offcut" model="ir.actions.act_window">
        <field name="name">Mesh Offcuts</field>
        <field name="res_model">mesh.offcut</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No mesh offcuts found!
            </p>
            <p>
                Mesh offcuts track available pieces of mesh material that can be used for cutting operations.
                This includes both planned offcuts (predictable waste) and unplanned offcuts (actual waste pieces).
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_mesh_offcuts"
              name="Mesh Offcuts"
              parent="canbrax_configmatrix.menu_mesh_operations_root"
              action="action_mesh_offcut"
              sequence="40"/>
</odoo>
```

## Model Integration

### Update models/__init__.py
Add the import for the new model:
```python
from . import mesh_offcut
```

### Update security/ir.model.access.csv
Add access rights:
```csv
access_mesh_offcut_admin,mesh.offcut admin,model_mesh_offcut,canbrax_configmatrix.group_config_matrix_admin,1,1,1,1
access_mesh_offcut_user,mesh.offcut user,model_mesh_offcut,canbrax_configmatrix.group_config_matrix_user,1,1,1,0
```

## Key Features

### Dimension Tracking
- **Original Dimensions**: Track initial size when offcut was created
- **Current Dimensions**: Track current usable size (may be smaller after cuts)
- **Area Calculations**: Automatic area calculations in mm² and m²

### Type Classification
- **Planned Offcuts**: Predictable waste from cutting operations
- **Unplanned Offcuts**: Actual waste pieces from production
- **Master Sheets**: Full-size sheets (for completeness)

### State Management
- **Available**: Ready for use
- **Reserved**: Temporarily allocated to an operation
- **Used**: Consumed in production
- **Scrapped**: No longer usable

### Search and Filtering
- **Find Suitable**: Method to find offcuts that fit required dimensions
- **Type Filtering**: Filter by planned/unplanned/master
- **State Filtering**: Filter by availability status
- **Series Filtering**: Filter by mesh series

## Usage Patterns

### Creating Offcuts
- Manual creation for existing waste
- Automatic creation from cutting operations
- Import from external systems

### Hunting Integration
- Used by hunting methods in Task 4
- Provides available offcuts for selection
- Tracks usage and reservations

### Inventory Management
- Track location of physical pieces
- Monitor usage patterns
- Identify scrapping needs

## Testing Checklist

- [ ] Model creates and saves correctly
- [ ] Views display properly
- [ ] Search and filters work
- [ ] State transitions function
- [ ] Area calculations are accurate
- [ ] Find suitable method works
- [ ] Menu item appears correctly

## Next Steps
After completing this task:
1. Create sample offcut data for testing
2. Test integration with Task 4 hunting logic
3. Proceed to Task 6 (Operation Views)

## Notes
- This model is essential for the hunting logic in Task 4
- Consider creating sample data for testing
- Location tracking integrates with stock management
