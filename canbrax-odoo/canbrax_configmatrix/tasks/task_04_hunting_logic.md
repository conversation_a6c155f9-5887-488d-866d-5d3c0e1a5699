# Task 4: <PERSON><PERSON><PERSON><PERSON> Cut Operation Model

**Phase**: 2 - Enhanced Hunting  
**Priority**: High  
**Dependencies**: Task 3 (Panel Calculation)  
**Estimated Time**: 5-6 hours

## Overview
Enhance the mesh cut operation model to support individual panel hunting with priority-based source selection and detailed tracking.

## Files to Modify
- `canbrax_configmatrix/models/mesh_cut_operation.py`

## Implementation Details

### New Fields to Add

Add these fields to track individual panel hunting:

```python
# Panel Identification
panel_number = fields.Integer("Panel Number", help="Sequential panel number within configuration")
panel_position = fields.Selection([
    ('full', 'Full Panel'),
    ('top', 'Top Panel'),
    ('bottom', 'Bottom Panel'),
    ('left', 'Left Panel'),
    ('right', 'Right Panel'),
    ('custom', 'Custom Position')
], string="Panel Position", help="Position of panel within door")
door_number = fields.Integer("Door Number", default=1, help="Door number for multi-door configurations")

# Hunting Configuration
hunting_priority = fields.Selection([
    ('unplanned_planned_master', 'Unplanned → Planned → Master'),
    ('planned_unplanned_master', 'Planned → Unplanned → Master'),
    ('master_only', 'Master Sheets Only'),
    ('offcuts_only', 'Offcuts Only')
], string="Hunting Priority", help="Priority order for this panel")

# Hunting Results
mesh_source_type = fields.Selection([
    ('unplanned_offcut', 'Unplanned Offcut'),
    ('planned_offcut', 'Planned Offcut'),
    ('master_sheet', 'Master Sheet'),
    ('not_found', 'Not Found')
], string="Mesh Source Type", readonly=True, help="Source type of selected mesh")
selected_mesh_id = fields.Many2one('product.product', string="Selected Mesh", readonly=True)
hunting_notes = fields.Text("Hunting Notes", readonly=True, help="Notes from hunting process")
```

### Main Hunting Method

```python
def hunt_individual_panel(self, width, height, series, priority_order=None):
    """Hunt for mesh for individual panel with priority order"""
    self.ensure_one()
    
    if not priority_order:
        priority_order = self.hunting_priority or 'unplanned_planned_master'
    
    hunting_methods = self._get_hunting_methods(priority_order)
    hunting_log = []
    
    for method_name, method_func in hunting_methods:
        hunting_log.append(f"Trying {method_name}...")
        
        result = method_func(width, height, series)
        if result and result.get('product'):
            # Found mesh
            self.write({
                'selected_mesh_id': result['product'].id,
                'mesh_source_type': result.get('source_type', 'not_found'),
                'hunting_notes': '\n'.join(hunting_log + [f"✓ Found: {result.get('details', 'No details')}"])
            })
            return result
        else:
            hunting_log.append(f"  ✗ {method_name}: {result.get('reason', 'No suitable mesh found')}")
    
    # No mesh found
    self.write({
        'mesh_source_type': 'not_found',
        'hunting_notes': '\n'.join(hunting_log + ["✗ No suitable mesh found in any source"])
    })
    return None
```

### Priority Method Selection

```python
def _get_hunting_methods(self, priority_order):
    """Get hunting methods in priority order"""
    methods = {
        'unplanned_offcuts': ('Unplanned Offcuts', self._hunt_unplanned_offcuts),
        'planned_offcuts': ('Planned Offcuts', self._hunt_planned_offcuts),
        'master_sheets': ('Master Sheets', self._hunt_master_sheets)
    }
    
    if priority_order == 'unplanned_planned_master':
        return [methods['unplanned_offcuts'], methods['planned_offcuts'], methods['master_sheets']]
    elif priority_order == 'planned_unplanned_master':
        return [methods['planned_offcuts'], methods['unplanned_offcuts'], methods['master_sheets']]
    elif priority_order == 'master_only':
        return [methods['master_sheets']]
    elif priority_order == 'offcuts_only':
        return [methods['unplanned_offcuts'], methods['planned_offcuts']]
    else:
        return [methods['unplanned_offcuts'], methods['planned_offcuts'], methods['master_sheets']]
```

### Individual Hunting Methods

```python
def _hunt_unplanned_offcuts(self, width, height, series):
    """Hunt in unplanned offcuts"""
    # Search for unplanned offcuts that can fit the required dimensions
    domain = [
        ('mesh_series', '=', series),
        ('current_width', '>=', width),
        ('current_height', '>=', height),
        ('state', '=', 'available'),
        ('is_planned_offcut', '=', False),
        ('is_master_sheet', '=', False)
    ]
    
    offcuts = self.env['mesh.offcut'].search(domain, order='current_width asc, current_height asc', limit=1)
    
    if offcuts:
        offcut = offcuts[0]
        return {
            'product': offcut.product_id,
            'source_type': 'unplanned_offcut',
            'source_record': offcut,
            'details': f"Unplanned offcut {offcut.current_width}x{offcut.current_height}mm (ID: {offcut.id})",
            'efficiency': (width * height) / (offcut.current_width * offcut.current_height)
        }
    
    return {'reason': 'No suitable unplanned offcuts found'}

def _hunt_planned_offcuts(self, width, height, series):
    """Hunt in planned offcuts"""
    domain = [
        ('mesh_series', '=', series),
        ('current_width', '>=', width),
        ('current_height', '>=', height),
        ('state', '=', 'available'),
        ('is_planned_offcut', '=', True),
        ('is_master_sheet', '=', False)
    ]
    
    offcuts = self.env['mesh.offcut'].search(domain, order='current_width asc, current_height asc', limit=1)
    
    if offcuts:
        offcut = offcuts[0]
        return {
            'product': offcut.product_id,
            'source_type': 'planned_offcut',
            'source_record': offcut,
            'details': f"Planned offcut {offcut.current_width}x{offcut.current_height}mm (ID: {offcut.id})",
            'efficiency': (width * height) / (offcut.current_width * offcut.current_height)
        }
    
    return {'reason': 'No suitable planned offcuts found'}

def _hunt_master_sheets(self, width, height, series):
    """Hunt in master sheets"""
    # Find best master sheet from cut matrix
    matrix = self.env['mesh.cut.matrix'].search([('mesh_series', '=', series)], limit=1)
    
    if not matrix:
        return {'reason': 'No cut matrix found for series'}
    
    # Find best cut plan for these dimensions
    best_plan = matrix.find_best_cut_plan(width, height)
    
    if best_plan:
        master_product = self.env['product.product'].search([
            ('mesh_series', '=', series),
            ('is_master_sheet', '=', True),
            ('mesh_width', '=', best_plan['cut_width']),
            ('mesh_height', '=', best_plan['cut_height'])
        ], limit=1)
        
        if master_product:
            return {
                'product': master_product,
                'source_type': 'master_sheet',
                'source_record': best_plan,
                'details': f"Master sheet {best_plan['cut_width']}x{best_plan['cut_height']}mm (Plan: {best_plan.get('name', 'Unknown')})",
                'efficiency': best_plan.get('efficiency', 0.0)
            }
    
    return {'reason': 'No suitable master sheet cut plan found'}
```

## Hunting Logic Flow

### 1. Priority Selection
- Get hunting methods based on template priority setting
- Order methods according to business rules
- Most efficient sources first (unplanned offcuts → planned offcuts → master sheets)

### 2. Sequential Hunting
- Try each method in priority order
- Stop at first successful match
- Log all attempts for traceability

### 3. Result Recording
- Update operation with selected mesh
- Record source type and details
- Store hunting notes for debugging

### 4. Efficiency Calculation
- Calculate material efficiency for each option
- Help with cost analysis and waste tracking

## Hunting Strategies

### Unplanned Offcuts First (Default)
- **Logic**: Use waste material first
- **Benefits**: Minimize waste, reduce costs
- **Order**: Unplanned → Planned → Master

### Planned Offcuts First
- **Logic**: Use predictable offcuts first
- **Benefits**: Better planning, consistent quality
- **Order**: Planned → Unplanned → Master

### Master Only
- **Logic**: Use only new material
- **Benefits**: Consistent quality, predictable supply
- **Order**: Master sheets only

### Offcuts Only
- **Logic**: Use only waste material
- **Benefits**: Maximum waste reduction
- **Order**: Unplanned → Planned (no master sheets)

## Testing Scenarios

### Successful Hunt
- Panel: 400x300mm, Saltwater series
- Available: Unplanned offcut 450x350mm
- Expected: Success with unplanned offcut

### Fallback Hunt
- Panel: 800x600mm, Saltwater series
- Available: No suitable offcuts, master sheet 1000x800mm
- Expected: Success with master sheet

### Failed Hunt
- Panel: 1500x1200mm, Saltwater series
- Available: No suitable sources
- Expected: Not found status with detailed notes

## Testing Checklist

- [ ] Individual hunting method works correctly
- [ ] Priority ordering functions properly
- [ ] All hunting methods implemented
- [ ] Result recording works
- [ ] Hunting notes are informative
- [ ] Efficiency calculations are accurate
- [ ] Error handling for missing sources

## Next Steps
After completing this task:
1. Test hunting with various panel sizes
2. Proceed to Task 5 (Mesh Offcut Model)
3. Verify hunting results are properly recorded

## Notes
- This task depends on the mesh.offcut model (Task 5)
- Consider implementing mesh.offcut model first if needed
- Focus on robust error handling and logging
