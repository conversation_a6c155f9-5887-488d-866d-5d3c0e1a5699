# Task 7: Manufacturing Order Integration

**Phase**: 3 - Integration  
**Priority**: Medium  
**Dependencies**: Task 3 (Panel Calculation), Task 4 (Hunting Logic)  
**Estimated Time**: 3-4 hours

## Overview
Integrate the individual panel hunting system with manufacturing orders (MRP) to automatically create mesh cut operations when production orders are created.

## Files to Modify
- `canbrax_configmatrix/models/mrp_production.py`
- `canbrax_configmatrix/views/mrp_production_views.xml` (if exists)

## Implementation Details

### Manufacturing Order Enhancement

Add these methods to the MRP production model:

```python
def _create_mesh_cut_operations(self):
    """Create mesh cut operations for manufacturing order with individual panel dimensions"""
    self.ensure_one()
    
    # Skip if not configured or no mesh required
    if not self.config_id or not self.config_id.template_id.mesh_required:
        return False
    
    # Get configuration values
    config_values = self.config_id.get_configuration_values()
    
    # Calculate panel dimensions
    panel_dimensions = self.config_id._calculate_mesh_panel_dimensions(config_values)
    
    if not panel_dimensions:
        return False
    
    mesh_series = config_values.get('mesh_series', self.config_id.template_id.mesh_series)
    hunting_priority = self.config_id.template_id.mesh_hunting_priority
    
    # Create operations for each panel
    operations_created = 0
    for i, panel in enumerate(panel_dimensions):
        operation = self.env['mesh.cut.operation'].create({
            'name': f"MO-{self.name}: {panel['name']}",
            'required_width': panel['width'],
            'required_height': panel['height'],
            'required_qty': 1.0,
            'mesh_series': mesh_series,
            'state': 'draft',
            'production_id': self.id,
            'config_id': self.config_id.id,
            'panel_number': i + 1,
            'panel_position': panel.get('panel_position', 'full'),
            'door_number': panel.get('door_number', 1),
            'hunting_priority': hunting_priority,
        })
        
        # Hunt for mesh if individual hunting is enabled
        if self.config_id.template_id.hunt_each_panel_separately:
            operation.hunt_individual_panel(panel['width'], panel['height'], mesh_series, hunting_priority)
        
        operations_created += 1
    
    return operations_created > 0

def action_view_mesh_operations(self):
    """View mesh operations for this manufacturing order"""
    self.ensure_one()
    
    operations = self.env['mesh.cut.operation'].search([('production_id', '=', self.id)])
    
    return {
        'type': 'ir.actions.act_window',
        'name': f'Mesh Operations - {self.name}',
        'res_model': 'mesh.cut.operation',
        'view_mode': 'list,form',
        'domain': [('id', 'in', operations.ids)],
        'context': {'default_production_id': self.id},
    }

def action_create_mesh_operations(self):
    """Manual action to create mesh operations"""
    self.ensure_one()
    
    if not self.config_id:
        raise UserError("This manufacturing order is not linked to a configuration.")
    
    if not self.config_id.template_id.mesh_required:
        raise UserError("This configuration template does not require mesh operations.")
    
    # Check if operations already exist
    existing_ops = self.env['mesh.cut.operation'].search([('production_id', '=', self.id)])
    if existing_ops:
        raise UserError("Mesh operations already exist for this manufacturing order.")
    
    # Create operations
    success = self._create_mesh_cut_operations()
    
    if not success:
        raise UserError("No mesh operations could be created. Check configuration values.")
    
    return self.action_view_mesh_operations()

# Add computed field for mesh operations count
mesh_operation_count = fields.Integer(
    "Mesh Operations Count",
    compute='_compute_mesh_operation_count'
)

@api.depends('config_id')
def _compute_mesh_operation_count(self):
    for production in self:
        production.mesh_operation_count = len(
            self.env['mesh.cut.operation'].search([('production_id', '=', production.id)])
        )
```

### Automatic Operation Creation

Add automatic creation hook:

```python
@api.model
def create(self, vals):
    """Override create to automatically create mesh operations"""
    production = super().create(vals)
    
    # Auto-create mesh operations if configured
    if (production.config_id and 
        production.config_id.template_id.mesh_required and
        production.config_id.template_id.hunt_each_panel_separately):
        
        try:
            production._create_mesh_cut_operations()
        except Exception as e:
            # Log error but don't fail production creation
            _logger.warning(f"Failed to auto-create mesh operations for {production.name}: {e}")
    
    return production

def write(self, vals):
    """Override write to handle configuration changes"""
    result = super().write(vals)
    
    # If configuration changed, recreate mesh operations
    if 'config_id' in vals:
        for production in self:
            if (production.config_id and 
                production.config_id.template_id.mesh_required):
                
                # Remove existing operations
                existing_ops = self.env['mesh.cut.operation'].search([
                    ('production_id', '=', production.id)
                ])
                existing_ops.unlink()
                
                # Create new operations
                try:
                    production._create_mesh_cut_operations()
                except Exception as e:
                    _logger.warning(f"Failed to recreate mesh operations for {production.name}: {e}")
    
    return result
```

### View Integration

If MRP production views exist, add these enhancements:

```xml
<record id="view_mrp_production_form_mesh_enhanced" model="ir.ui.view">
    <field name="name">mrp.production.form.mesh.enhanced</field>
    <field name="model">mrp.production</field>
    <field name="inherit_id" ref="mrp.mrp_production_form_view"/>
    <field name="arch" type="xml">
        <!-- Add button to button box -->
        <xpath expr="//div[hasclass('oe_button_box')]" position="inside">
            <button name="action_view_mesh_operations" type="object"
                    class="oe_stat_button" icon="fa-scissors"
                    invisible="mesh_operation_count == 0">
                <field name="mesh_operation_count" widget="statinfo" string="Mesh Operations"/>
            </button>
        </xpath>
        
        <!-- Add action buttons to header -->
        <header position="inside">
            <button name="action_create_mesh_operations" type="object"
                    string="Create Mesh Operations" class="btn-secondary"
                    invisible="config_id.template_id.mesh_required == False or mesh_operation_count > 0"/>
        </header>
    </field>
</record>
```

## Integration Points

### Configuration Link
- Manufacturing orders linked to configurations
- Automatic detection of mesh requirements
- Panel calculation based on configuration values

### Operation Creation Triggers
- **Automatic**: When MO is created with mesh-enabled configuration
- **Manual**: Button to create operations on demand
- **Update**: Recreate operations when configuration changes

### BOM Integration
- Mesh operations can update BOM lines
- Material requirements calculated from hunting results
- Stock reservations based on selected mesh

## Workflow Integration

### Standard Flow
1. **Configuration Created**: Customer configures product
2. **Sale Order Confirmed**: Triggers manufacturing order
3. **MO Created**: Automatically creates mesh operations
4. **Hunting Performed**: Each panel hunted individually
5. **Materials Reserved**: Based on hunting results
6. **Production Started**: With clear mesh cutting instructions

### Manual Flow
1. **MO Created**: Without automatic mesh operations
2. **Manual Trigger**: User clicks "Create Mesh Operations"
3. **Operations Generated**: Based on current configuration
4. **Hunting Performed**: Individual panel hunting
5. **Review Results**: User reviews hunting outcomes

## Error Handling

### Configuration Issues
- Missing configuration values
- Invalid panel calculations
- Template not configured for mesh

### Hunting Failures
- No suitable mesh found
- Insufficient stock
- Matrix calculation errors

### Recovery Actions
- Log errors without failing MO creation
- Provide manual retry options
- Clear error messages for users

## Testing Scenarios

### Automatic Creation
- **Setup**: Create MO with mesh-enabled configuration
- **Expected**: Mesh operations created automatically
- **Verify**: Operations match panel calculations

### Manual Creation
- **Setup**: MO without automatic operations
- **Expected**: Button available to create operations
- **Verify**: Operations created on button click

### Configuration Update
- **Setup**: Change configuration on existing MO
- **Expected**: Operations recreated with new panels
- **Verify**: Old operations removed, new ones created

### Error Handling
- **Setup**: Invalid configuration or missing data
- **Expected**: Graceful error handling
- **Verify**: MO creation doesn't fail, errors logged

## Testing Checklist

- [ ] Automatic operation creation works
- [ ] Manual operation creation works
- [ ] Configuration updates trigger recreation
- [ ] Error handling prevents MO creation failure
- [ ] View enhancements display correctly
- [ ] Button visibility rules work
- [ ] Operation count calculation is accurate

## Next Steps
After completing this task:
1. Test with various configuration types
2. Verify integration with existing MRP workflows
3. Proceed to Task 8 (Configuration Actions)

## Notes
- Ensure backward compatibility with existing MOs
- Consider performance impact of automatic creation
- Test error scenarios thoroughly
- Maintain audit trail of operation creation/recreation
