# Task 3: Add Panel Dimension Calculation

**Phase**: 1 - Core Functionality  
**Priority**: High  
**Dependencies**: Task 1 (Template Model)  
**Estimated Time**: 4-5 hours

## Overview
Implement panel dimension calculation logic in the configuration model to support different calculation methods (midrail split, simple count, custom formula).

## Files to Modify
- `canbrax_configmatrix/models/config_matrix_configuration.py`

## Implementation Details

### Main Calculation Method

Add this method to calculate panel dimensions based on template configuration:

```python
def _calculate_mesh_panel_dimensions(self, config_values):
    """Calculate dimensions for each mesh panel based on configuration"""
    if not self.template_id.mesh_required:
        return []
    
    method = self.template_id.mesh_panel_calculation_method
    
    if method == 'midrail_split':
        return self._calculate_midrail_split_panels(config_values)
    elif method == 'custom_formula':
        return self._calculate_custom_formula_panels(config_values)
    else:
        return self._calculate_simple_count_panels(config_values)
```

### Midrail Split Calculation

The most complex and commonly used method:

```python
def _calculate_midrail_split_panels(self, config_values):
    """Calculate panel dimensions for door/midrail combinations"""
    panels = []
    
    # Get door dimensions
    door_width = config_values.get('_CALCULATED_largest_door_width', 0)
    door_height = config_values.get('_CALCULATED_largest_door_height', 0)
    
    if not door_width or not door_height:
        return panels
    
    # Determine if double door
    is_double_door = self._is_double_door(config_values)
    door_count = 2 if is_double_door else 1
    
    # Check for midrail
    has_midrail = (config_values.get('midrail_selected', False) or 
                   config_values.get('has_midrail', False) or
                   config_values.get('midrail_position', 0) > 0)
    
    if has_midrail:
        # Get midrail position
        midrail_position = self._get_midrail_position(config_values, door_height)
        
        # Calculate panel heights
        top_height = midrail_position
        bottom_height = door_height - midrail_position
        
        # Create panels for each door
        for door_num in range(door_count):
            door_label = f"Door {door_num + 1}" if is_double_door else "Door"
            
            # Top panel
            panels.append({
                'width': door_width,
                'height': top_height,
                'door_number': door_num + 1,
                'panel_position': 'top',
                'name': f'{door_label} Top Panel ({door_width}x{int(top_height)}mm)'
            })
            
            # Bottom panel
            panels.append({
                'width': door_width,
                'height': bottom_height,
                'door_number': door_num + 1,
                'panel_position': 'bottom',
                'name': f'{door_label} Bottom Panel ({door_width}x{int(bottom_height)}mm)'
            })
    else:
        # No midrail - single panel per door
        for door_num in range(door_count):
            door_label = f"Door {door_num + 1}" if is_double_door else "Door"
            
            panels.append({
                'width': door_width,
                'height': door_height,
                'door_number': door_num + 1,
                'panel_position': 'full',
                'name': f'{door_label} Full Panel ({door_width}x{door_height}mm)'
            })
    
    return panels
```

### Helper Methods

```python
def _get_midrail_position(self, config_values, door_height):
    """Get midrail position from configuration"""
    # Try explicit midrail position first
    midrail_position = config_values.get('midrail_position', 0)
    if midrail_position > 0:
        return midrail_position
    
    # Check for calculated midrail height
    calc_midrail = config_values.get('_CALCULATED_midrail_height', 0)
    if calc_midrail > 0:
        return calc_midrail
    
    # Check for even split
    is_even_split = config_values.get('_CALCULATED_is_even_split', False)
    if is_even_split:
        return door_height / 2
    
    # Default to 60% of door height (common midrail position)
    return door_height * 0.6

def _is_double_door(self, config_values):
    """Check if this is a double door configuration"""
    # Check door type field
    door_type = config_values.get('door_type', '').lower()
    if 'double' in door_type:
        return True
    
    # Check template name
    if 'double' in (self.template_id.technical_name or '').lower():
        return True
    
    # Check other possible fields
    door_config = config_values.get('door_configuration', '').lower()
    if 'double' in door_config:
        return True
    
    # Check template name patterns
    template_name = (self.template_id.name or '').lower()
    if 'double' in template_name:
        return True
    
    return False
```

## Calculation Methods

### 1. Midrail Split Method
- **Purpose**: Automatically calculate panels based on midrail position
- **Logic**: 
  - Detect single/double door configuration
  - Check for midrail presence
  - Split panels at midrail position
  - Generate appropriate panel names and positions

### 2. Simple Count Method
- **Purpose**: Create specified number of identical panels
- **Logic**: Use formula to determine count, create identical panels

### 3. Custom Formula Method
- **Purpose**: Allow complex custom calculations
- **Logic**: Execute Python code with configuration context

## Configuration Value Sources

### Door Dimensions
- `_CALCULATED_largest_door_width`: Primary width source
- `_CALCULATED_largest_door_height`: Primary height source
- Fallback to other width/height fields if needed

### Midrail Detection
- `midrail_selected`: Explicit midrail selection
- `has_midrail`: Boolean midrail flag
- `midrail_position`: Explicit position value
- `_CALCULATED_midrail_height`: Calculated position
- `_CALCULATED_is_even_split`: Even split indicator

### Door Type Detection
- `door_type`: Explicit door type field
- `door_configuration`: Configuration type
- Template name patterns
- Technical name patterns

## Panel Data Structure

Each panel returns a dictionary with:
- `width`: Panel width in mm
- `height`: Panel height in mm
- `door_number`: Door number (1 for single, 1-2 for double)
- `panel_position`: Position identifier ('full', 'top', 'bottom', 'custom')
- `name`: Human-readable panel name

## Testing Scenarios

### Single Door, No Midrail
- **Input**: door_width=800, door_height=2000, midrail_selected=False
- **Expected**: 1 panel (800x2000mm, full)

### Single Door, With Midrail
- **Input**: door_width=800, door_height=2000, midrail_position=1200
- **Expected**: 2 panels (800x1200mm top, 800x800mm bottom)

### Double Door, No Midrail
- **Input**: door_width=800, door_height=2000, door_type="double"
- **Expected**: 2 panels (800x2000mm each door)

### Double Door, With Midrail
- **Input**: door_width=800, door_height=2000, door_type="double", midrail_position=1200
- **Expected**: 4 panels (2 doors × 2 panels each)

## Testing Checklist

- [ ] Midrail split calculation works correctly
- [ ] Single door configurations handled properly
- [ ] Double door configurations handled properly
- [ ] Midrail position detection works
- [ ] Panel naming is clear and consistent
- [ ] Edge cases handled (missing dimensions, invalid values)
- [ ] Custom formula method framework in place

## Next Steps
After completing this task:
1. Test with various configuration combinations
2. Proceed to Task 4 (Hunting Logic Implementation)
3. Verify panel calculations match expected results

## Notes
- Focus on midrail_split method first as it's most commonly used
- Ensure robust error handling for missing configuration values
- Panel names should be descriptive for manufacturing clarity
