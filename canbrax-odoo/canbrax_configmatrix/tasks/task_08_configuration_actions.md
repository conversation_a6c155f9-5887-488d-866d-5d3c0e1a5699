# Task 8: Configuration Actions

**Phase**: 3 - Integration  
**Priority**: Medium  
**Dependencies**: Task 3 (Panel Calculation), Task 4 (Hunting Logic)  
**Estimated Time**: 2-3 hours

## Overview
Add user actions to the configuration model and views to allow manual creation and management of mesh operations directly from configurations.

## Files to Modify
- `canbrax_configmatrix/models/config_matrix_configuration.py`
- `canbrax_configmatrix/views/config_matrix_configuration_views.xml`

## Implementation Details

### Configuration Model Actions

Add these methods to the configuration model:

```python
def action_create_mesh_operations(self):
    """Create mesh operations for this configuration"""
    self.ensure_one()
    
    if not self.template_id.mesh_required:
        raise UserError("This configuration template does not require mesh.")
    
    # Check if operations already exist
    existing_ops = self.env['mesh.cut.operation'].search([('config_id', '=', self.id)])
    if existing_ops:
        raise UserError("Mesh operations already exist for this configuration. Use 'View Mesh Operations' to see them.")
    
    # Create operations
    operations = self.env['mesh.cut.operation'].create_operations_for_configuration(self.id)
    
    if not operations:
        raise UserError("No mesh operations could be created. Check configuration values and ensure door dimensions are calculated.")
    
    return {
        'type': 'ir.actions.act_window',
        'name': f'Mesh Operations - {self.name}',
        'res_model': 'mesh.cut.operation',
        'view_mode': 'list,form',
        'domain': [('id', 'in', [op.id for op in operations])],
        'context': {'default_config_id': self.id},
    }

def action_view_mesh_operations(self):
    """View existing mesh operations for this configuration"""
    self.ensure_one()
    
    operations = self.env['mesh.cut.operation'].search([('config_id', '=', self.id)])
    
    if not operations:
        raise UserError("No mesh operations found for this configuration. Use 'Create Mesh Operations' to create them.")
    
    return {
        'type': 'ir.actions.act_window',
        'name': f'Mesh Operations - {self.name}',
        'res_model': 'mesh.cut.operation',
        'view_mode': 'list,form',
        'domain': [('id', 'in', operations.ids)],
        'context': {'default_config_id': self.id},
    }

def action_recreate_mesh_operations(self):
    """Recreate mesh operations (delete existing and create new)"""
    self.ensure_one()
    
    if not self.template_id.mesh_required:
        raise UserError("This configuration template does not require mesh.")
    
    # Remove existing operations
    existing_ops = self.env['mesh.cut.operation'].search([('config_id', '=', self.id)])
    if existing_ops:
        existing_ops.unlink()
    
    # Create new operations
    operations = self.env['mesh.cut.operation'].create_operations_for_configuration(self.id)
    
    if not operations:
        raise UserError("No mesh operations could be created. Check configuration values.")
    
    return {
        'type': 'ir.actions.act_window',
        'name': f'Mesh Operations - {self.name}',
        'res_model': 'mesh.cut.operation',
        'view_mode': 'list,form',
        'domain': [('id', 'in', [op.id for op in operations])],
        'context': {'default_config_id': self.id},
    }

def action_test_panel_calculation(self):
    """Test panel dimension calculation (for debugging)"""
    self.ensure_one()
    
    if not self.template_id.mesh_required:
        raise UserError("This configuration template does not require mesh.")
    
    # Get configuration values
    config_values = self.get_configuration_values()
    
    # Calculate panel dimensions
    panel_dimensions = self._calculate_mesh_panel_dimensions(config_values)
    
    if not panel_dimensions:
        raise UserError("No panels could be calculated. Check configuration values and door dimensions.")
    
    # Format results for display
    panel_info = []
    for i, panel in enumerate(panel_dimensions):
        panel_info.append(f"Panel {i+1}: {panel['name']} - {panel['width']}x{panel['height']}mm (Door {panel['door_number']}, Position: {panel['panel_position']})")
    
    message = f"Panel Calculation Results:\n\n" + "\n".join(panel_info)
    
    return {
        'type': 'ir.actions.client',
        'tag': 'display_notification',
        'params': {
            'title': 'Panel Calculation Test',
            'message': message,
            'type': 'info',
            'sticky': True,
        }
    }

# Add computed field for mesh operations count
mesh_operation_count = fields.Integer(
    "Mesh Operations Count",
    compute='_compute_mesh_operation_count'
)

@api.depends('id')
def _compute_mesh_operation_count(self):
    for config in self:
        config.mesh_operation_count = len(
            self.env['mesh.cut.operation'].search([('config_id', '=', config.id)])
        )
```

### Mesh Cut Operation Model Enhancement

Add the configuration creation method:

```python
@api.model
def create_operations_for_configuration(self, configuration_id):
    """Create mesh operations for a configuration with individual panel dimensions"""
    config = self.env['config.matrix.configuration'].browse(configuration_id)
    if not config.exists() or not config.template_id.mesh_required:
        return []
    
    # Get panel dimensions
    config_values = config.get_configuration_values()
    panel_dimensions = config._calculate_mesh_panel_dimensions(config_values)
    
    if not panel_dimensions:
        return []
    
    mesh_series = config_values.get('mesh_series', config.template_id.mesh_series)
    hunting_priority = config.template_id.mesh_hunting_priority
    operations = []
    
    # Create operation for each panel
    for i, panel in enumerate(panel_dimensions):
        operation = self.create({
            'name': f"{config.name}: {panel['name']}",
            'required_width': panel['width'],
            'required_height': panel['height'],
            'required_qty': 1.0,
            'mesh_series': mesh_series,
            'state': 'draft',
            'config_id': config.id,
            'panel_number': i + 1,
            'panel_position': panel.get('panel_position', 'full'),
            'door_number': panel.get('door_number', 1),
            'hunting_priority': hunting_priority,
        })
        
        # Hunt for mesh if enabled
        if config.template_id.hunt_each_panel_separately:
            operation.hunt_individual_panel(panel['width'], panel['height'], mesh_series, hunting_priority)
        
        operations.append(operation)
    
    return operations
```

### View Enhancements

Add buttons and stat box to configuration form view:

```xml
<record id="view_config_matrix_configuration_form_mesh_enhanced" model="ir.ui.view">
    <field name="name">config.matrix.configuration.form.mesh.enhanced</field>
    <field name="model">config.matrix.configuration</field>
    <field name="inherit_id" ref="view_config_matrix_configuration_form"/>
    <field name="arch" type="xml">
        <!-- Add stat button to button box -->
        <xpath expr="//div[hasclass('oe_button_box')]" position="inside">
            <button name="action_view_mesh_operations" type="object"
                    class="oe_stat_button" icon="fa-scissors"
                    invisible="template_id.mesh_required == False or mesh_operation_count == 0">
                <field name="mesh_operation_count" widget="statinfo" string="Mesh Operations"/>
            </button>
        </xpath>
        
        <!-- Add action buttons to header -->
        <header position="inside">
            <button name="action_create_mesh_operations" type="object"
                    string="Create Mesh Operations" class="btn-primary"
                    invisible="template_id.mesh_required == False or mesh_operation_count > 0"/>
            <button name="action_view_mesh_operations" type="object"
                    string="View Mesh Operations" class="btn-secondary"
                    invisible="template_id.mesh_required == False or mesh_operation_count == 0"/>
            <button name="action_recreate_mesh_operations" type="object"
                    string="Recreate Mesh Operations" class="btn-warning"
                    invisible="template_id.mesh_required == False or mesh_operation_count == 0"/>
            <button name="action_test_panel_calculation" type="object"
                    string="Test Panel Calculation" class="btn-secondary"
                    invisible="template_id.mesh_required == False"
                    groups="canbrax_configmatrix.group_config_matrix_admin"/>
        </header>
    </field>
</record>
```

## Button Logic and Visibility

### Create Mesh Operations
- **Visibility**: Only when mesh required AND no operations exist
- **Action**: Create new operations based on current configuration
- **Style**: Primary button (main action)

### View Mesh Operations
- **Visibility**: Only when mesh required AND operations exist
- **Action**: Open list view of existing operations
- **Style**: Secondary button

### Recreate Mesh Operations
- **Visibility**: Only when mesh required AND operations exist
- **Action**: Delete existing and create new operations
- **Style**: Warning button (destructive action)

### Test Panel Calculation
- **Visibility**: Only when mesh required AND user is admin
- **Action**: Show panel calculation results in notification
- **Style**: Secondary button (debugging tool)

## User Workflows

### Initial Setup
1. **Configure Product**: User completes configuration
2. **Create Operations**: Click "Create Mesh Operations"
3. **Review Results**: View operations and hunting results
4. **Proceed**: Continue with manufacturing or sales process

### Configuration Changes
1. **Modify Configuration**: User changes door dimensions or midrail
2. **Recreate Operations**: Click "Recreate Mesh Operations"
3. **Confirm Changes**: Review new panel calculations
4. **Update Process**: Continue with updated operations

### Debugging
1. **Test Calculation**: Admin clicks "Test Panel Calculation"
2. **Review Results**: See panel dimensions without creating operations
3. **Adjust Configuration**: Make changes if needed
4. **Create Operations**: Proceed when satisfied

## Error Handling

### User-Friendly Messages
- Clear error messages for common issues
- Helpful suggestions for resolution
- Prevent destructive actions without confirmation

### Validation Checks
- Verify mesh requirement before actions
- Check for existing operations
- Validate configuration completeness

## Testing Scenarios

### New Configuration
- **Setup**: Fresh configuration with mesh requirement
- **Action**: Click "Create Mesh Operations"
- **Expected**: Operations created successfully

### Existing Operations
- **Setup**: Configuration with existing operations
- **Action**: Try to create operations again
- **Expected**: Error message about existing operations

### Configuration Update
- **Setup**: Configuration with operations, change dimensions
- **Action**: Click "Recreate Mesh Operations"
- **Expected**: Old operations deleted, new ones created

### Panel Calculation Test
- **Setup**: Configuration with mesh requirement
- **Action**: Click "Test Panel Calculation" (admin only)
- **Expected**: Notification showing panel details

## Testing Checklist

- [ ] Create operations button works correctly
- [ ] View operations button opens correct view
- [ ] Recreate operations removes old and creates new
- [ ] Test calculation shows correct panel information
- [ ] Button visibility rules work as expected
- [ ] Error messages are clear and helpful
- [ ] Stat button shows correct count

## Next Steps
After completing this task:
1. Test all button actions with various configurations
2. Verify error handling and user messages
3. Proceed to Task 9 (Security and Access Rights)

## Notes
- Focus on user experience and clear error messages
- Ensure buttons are only visible when relevant
- Test with both admin and regular users
- Consider adding confirmation dialogs for destructive actions
