# Task 10: Testing Scenarios

**Phase**: 4 - Testing & Validation  
**Priority**: High  
**Dependencies**: All previous tasks (1-9)  
**Estimated Time**: 4-5 hours

## Overview
Comprehensive testing scenarios to validate the individual mesh panel hunting implementation works correctly across all supported configurations and edge cases.

## Testing Categories

### 1. Panel Calculation Testing
### 2. Individual Hunting Testing
### 3. Integration Testing
### 4. User Interface Testing
### 5. Error Handling Testing
### 6. Performance Testing

---

## 1. Panel Calculation Testing

### Test Case 1.1: Single Door, No Midrail
**Setup:**
- Template: Single door configuration
- Door dimensions: 800mm x 2000mm
- Midrail: None
- Method: midrail_split

**Expected Result:**
- 1 panel: 800x2000mm (Full Panel)
- Panel position: 'full'
- Door number: 1

**Test Steps:**
1. Create configuration with above parameters
2. Click "Test Panel Calculation"
3. Verify panel dimensions and naming

### Test Case 1.2: Single Door, With Midrail
**Setup:**
- Template: Single door configuration
- Door dimensions: 800mm x 2000mm
- Midrail position: 1200mm
- Method: midrail_split

**Expected Result:**
- 2 panels: 
  - Top: 800x1200mm
  - Bottom: 800x800mm
- Panel positions: 'top', 'bottom'
- Door number: 1 for both

### Test Case 1.3: Double Door, No Midrail
**Setup:**
- Template: Double door configuration
- Door dimensions: 800mm x 2000mm per door
- Midrail: None
- Method: midrail_split

**Expected Result:**
- 2 panels:
  - Door 1: 800x2000mm (Full Panel)
  - Door 2: 800x2000mm (Full Panel)
- Panel positions: 'full' for both
- Door numbers: 1, 2

### Test Case 1.4: Double Door, With Midrail
**Setup:**
- Template: Double door configuration
- Door dimensions: 800mm x 2000mm per door
- Midrail position: 1200mm
- Method: midrail_split

**Expected Result:**
- 4 panels:
  - Door 1 Top: 800x1200mm
  - Door 1 Bottom: 800x800mm
  - Door 2 Top: 800x1200mm
  - Door 2 Bottom: 800x800mm
- Panel positions: 'top', 'bottom' alternating
- Door numbers: 1, 1, 2, 2

---

## 2. Individual Hunting Testing

### Test Case 2.1: Successful Unplanned Offcut Hunt
**Setup:**
- Panel required: 400x300mm, Saltwater series
- Available: Unplanned offcut 450x350mm, Saltwater series
- Hunting priority: unplanned_planned_master

**Expected Result:**
- Mesh found: Unplanned offcut
- Source type: 'unplanned_offcut'
- Efficiency: ~77% ((400*300)/(450*350))
- Hunting notes: Success message with details

**Test Steps:**
1. Create mesh offcut with suitable dimensions
2. Create mesh operation requiring smaller panel
3. Run individual hunting
4. Verify result and efficiency calculation

### Test Case 2.2: Fallback to Planned Offcut
**Setup:**
- Panel required: 600x400mm, Saltwater series
- Available: No unplanned offcuts, Planned offcut 650x450mm
- Hunting priority: unplanned_planned_master

**Expected Result:**
- Mesh found: Planned offcut
- Source type: 'planned_offcut'
- Hunting notes: Shows unplanned attempt failed, planned succeeded

### Test Case 2.3: Fallback to Master Sheet
**Setup:**
- Panel required: 800x600mm, Saltwater series
- Available: No suitable offcuts, Master sheet 1000x800mm
- Hunting priority: unplanned_planned_master

**Expected Result:**
- Mesh found: Master sheet
- Source type: 'master_sheet'
- Hunting notes: Shows offcut attempts failed, master succeeded

### Test Case 2.4: No Suitable Mesh Found
**Setup:**
- Panel required: 1500x1200mm, Saltwater series
- Available: Only smaller offcuts and master sheets
- Hunting priority: unplanned_planned_master

**Expected Result:**
- Mesh found: None
- Source type: 'not_found'
- Hunting notes: Detailed log of all failed attempts

---

## 3. Integration Testing

### Test Case 3.1: Configuration to Operations
**Setup:**
- Complete configuration with mesh requirement
- Template configured for individual hunting
- Valid door dimensions and midrail

**Test Steps:**
1. Create configuration
2. Click "Create Mesh Operations"
3. Verify operations created match panel calculation
4. Check hunting was performed for each panel
5. Verify operation names and panel identification

### Test Case 3.2: Manufacturing Order Integration
**Setup:**
- Configuration linked to manufacturing order
- Automatic operation creation enabled

**Test Steps:**
1. Create manufacturing order with mesh configuration
2. Verify mesh operations created automatically
3. Check panel dimensions match configuration
4. Verify hunting results recorded

### Test Case 3.3: Configuration Update
**Setup:**
- Existing configuration with mesh operations
- Change door dimensions or midrail position

**Test Steps:**
1. Modify configuration parameters
2. Click "Recreate Mesh Operations"
3. Verify old operations deleted
4. Verify new operations match updated configuration
5. Check hunting performed with new dimensions

---

## 4. User Interface Testing

### Test Case 4.1: Template Form View
**Test Steps:**
1. Open template form view
2. Enable mesh requirement
3. Verify panel calculation fields appear
4. Test visibility conditions for different methods
5. Verify help text and placeholders display

### Test Case 4.2: Configuration Form View
**Test Steps:**
1. Open configuration with mesh requirement
2. Verify mesh operation buttons appear
3. Test button visibility based on operation count
4. Verify stat button shows correct count

### Test Case 4.3: Operation List View
**Test Steps:**
1. Create operations with panel information
2. Verify panel columns display correctly
3. Test filtering by panel position
4. Test grouping by door number and source type

### Test Case 4.4: Mesh Offcut Views
**Test Steps:**
1. Create various mesh offcuts
2. Test list view filtering and sorting
3. Verify form view displays all fields
4. Test state transitions (available → reserved → used)

---

## 5. Error Handling Testing

### Test Case 5.1: Missing Configuration Values
**Setup:**
- Configuration with missing door dimensions
- Template requires mesh

**Expected Result:**
- Clear error message about missing dimensions
- No operations created
- Helpful guidance for resolution

### Test Case 5.2: Invalid Template Configuration
**Setup:**
- Template with custom formula method
- Invalid Python code in formula field

**Expected Result:**
- Error caught during panel calculation
- Clear error message about formula issue
- No system crash

### Test Case 5.3: Hunting Failures
**Setup:**
- Panel requiring mesh
- No suitable mesh available in any source

**Expected Result:**
- Operation created with 'not_found' status
- Detailed hunting notes explaining attempts
- No system errors

---

## 6. Performance Testing

### Test Case 6.1: Large Configuration
**Setup:**
- Configuration requiring many panels (e.g., 10+ panels)
- Multiple hunting sources available

**Test Steps:**
1. Create configuration with many panels
2. Measure time to create operations
3. Measure time for hunting all panels
4. Verify system remains responsive

### Test Case 6.2: Many Offcuts
**Setup:**
- Database with 1000+ mesh offcuts
- Panel requiring hunting

**Test Steps:**
1. Create large number of offcuts
2. Perform hunting operation
3. Measure search time
4. Verify correct offcut selected

---

## Testing Data Setup

### Required Test Data

#### Templates
- Single door template (mesh enabled)
- Double door template (mesh enabled)
- Template with custom formula method
- Template with simple count method

#### Mesh Offcuts
- Various sizes of unplanned offcuts
- Various sizes of planned offcuts
- Different mesh series
- Different states (available, reserved, used)

#### Master Sheets
- Standard master sheet sizes
- Cut matrix configurations
- Different mesh series

#### Configurations
- Single door, no midrail
- Single door, with midrail
- Double door, no midrail
- Double door, with midrail
- Invalid configurations (missing data)

---

## Automated Testing Script

Consider creating a test script to automate common scenarios:

```python
def test_panel_calculation_scenarios():
    """Test all panel calculation scenarios"""
    # Test Case 1.1: Single door, no midrail
    # Test Case 1.2: Single door, with midrail
    # Test Case 1.3: Double door, no midrail
    # Test Case 1.4: Double door, with midrail

def test_hunting_scenarios():
    """Test all hunting scenarios"""
    # Test Case 2.1: Unplanned offcut success
    # Test Case 2.2: Planned offcut fallback
    # Test Case 2.3: Master sheet fallback
    # Test Case 2.4: No mesh found

def test_integration_scenarios():
    """Test integration scenarios"""
    # Test Case 3.1: Configuration to operations
    # Test Case 3.2: MRP integration
    # Test Case 3.3: Configuration updates
```

---

## Testing Checklist

### Panel Calculation
- [ ] Single door, no midrail works correctly
- [ ] Single door, with midrail splits properly
- [ ] Double door, no midrail creates 2 panels
- [ ] Double door, with midrail creates 4 panels
- [ ] Panel naming is clear and consistent
- [ ] Door numbers assigned correctly
- [ ] Panel positions identified correctly

### Individual Hunting
- [ ] Unplanned offcut hunting works
- [ ] Planned offcut hunting works
- [ ] Master sheet hunting works
- [ ] Priority order respected
- [ ] Efficiency calculations correct
- [ ] Hunting notes informative
- [ ] Failed hunts handled gracefully

### Integration
- [ ] Configuration actions work correctly
- [ ] MRP integration functions properly
- [ ] Operation recreation works
- [ ] Panel calculation test works
- [ ] Stat buttons show correct counts

### User Interface
- [ ] Template fields display correctly
- [ ] Configuration buttons work
- [ ] Operation views show panel info
- [ ] Offcut views function properly
- [ ] Visibility conditions work
- [ ] Error messages are clear

### Error Handling
- [ ] Missing data handled gracefully
- [ ] Invalid configurations caught
- [ ] Hunting failures managed
- [ ] System remains stable
- [ ] User guidance provided

### Performance
- [ ] Large configurations handled efficiently
- [ ] Many offcuts searched quickly
- [ ] System remains responsive
- [ ] Memory usage reasonable

---

## Success Criteria

The implementation is considered successful when:

1. **All test cases pass** without errors
2. **User interface is intuitive** and responsive
3. **Error handling is robust** and informative
4. **Performance is acceptable** for expected data volumes
5. **Integration works seamlessly** with existing systems
6. **Documentation is complete** and accurate

---

## Next Steps After Testing

1. **Fix any issues** identified during testing
2. **Update documentation** based on test results
3. **Create user training materials** if needed
4. **Plan deployment strategy** for production
5. **Monitor system performance** after deployment

## Notes
- Test with realistic data volumes
- Include edge cases and error scenarios
- Document any issues found and their resolutions
- Consider user acceptance testing with actual users
