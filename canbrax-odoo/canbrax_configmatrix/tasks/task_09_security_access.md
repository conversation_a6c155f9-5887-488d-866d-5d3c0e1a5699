# Task 9: Security and Access Rights

**Phase**: 3 - Integration  
**Priority**: Medium  
**Dependencies**: Task 5 (Offcut Model)  
**Estimated Time**: 1-2 hours

## Overview
Configure security access rights for the new mesh offcut model and ensure proper permissions for all mesh hunting functionality.

## Files to Modify
- `canbrax_configmatrix/security/ir.model.access.csv`
- `canbrax_configmatrix/__manifest__.py` (if new models added)

## Implementation Details

### Access Rights for Mesh Offcut Model

Add these lines to `ir.model.access.csv`:

```csv
# Mesh Offcut Access Rights
access_mesh_offcut_admin,mesh.offcut admin,model_mesh_offcut,canbrax_configmatrix.group_config_matrix_admin,1,1,1,1
access_mesh_offcut_user,mesh.offcut user,model_mesh_offcut,canbrax_configmatrix.group_config_matrix_user,1,1,1,0
access_mesh_offcut_builder,mesh.offcut builder,model_mesh_offcut,canbrax_configmatrix.group_config_matrix_builder,1,0,0,0
```

### Review Existing Access Rights

Verify these existing access rights are sufficient:

```csv
# Mesh Cut Operation Access Rights (should already exist)
access_mesh_cut_operation_admin,mesh.cut.operation admin,model_mesh_cut_operation,canbrax_configmatrix.group_config_matrix_admin,1,1,1,1
access_mesh_cut_operation_user,mesh.cut.operation user,model_mesh_cut_operation,canbrax_configmatrix.group_config_matrix_user,1,1,1,0
access_mesh_cut_operation_builder,mesh.cut.operation builder,model_mesh_cut_operation,canbrax_configmatrix.group_config_matrix_builder,1,0,0,0

# Configuration Access Rights (should already exist)
access_config_matrix_configuration_admin,config.matrix.configuration admin,model_config_matrix_configuration,canbrax_configmatrix.group_config_matrix_admin,1,1,1,1
access_config_matrix_configuration_user,config.matrix.configuration user,model_config_matrix_configuration,canbrax_configmatrix.group_config_matrix_user,1,1,1,0
access_config_matrix_configuration_builder,config.matrix.configuration builder,model_config_matrix_configuration,canbrax_configmatrix.group_config_matrix_builder,1,0,0,0

# Template Access Rights (should already exist)
access_config_matrix_template_admin,config.matrix.template admin,model_config_matrix_template,canbrax_configmatrix.group_config_matrix_admin,1,1,1,1
access_config_matrix_template_user,config.matrix.template user,model_config_matrix_template,canbrax_configmatrix.group_config_matrix_user,1,0,0,0
access_config_matrix_template_builder,config.matrix.template builder,model_config_matrix_template,canbrax_configmatrix.group_config_matrix_builder,1,0,0,0
```

## Security Group Permissions

### Admin Group (group_config_matrix_admin)
- **Full Access**: All models (read, write, create, delete)
- **Capabilities**:
  - Create and modify templates
  - Manage mesh offcuts
  - Create/delete mesh operations
  - Access debugging tools (Test Panel Calculation)
  - Configure hunting priorities

### User Group (group_config_matrix_user)
- **Standard Access**: Most models (read, write, create, no delete)
- **Capabilities**:
  - Create configurations
  - Create mesh operations
  - View and modify mesh offcuts
  - Use hunting functionality
  - View hunting results

### Builder Group (group_config_matrix_builder)
- **Read-Only Access**: Most models (read only)
- **Capabilities**:
  - View configurations
  - View mesh operations
  - View mesh offcuts
  - Access builder portal interfaces
  - No modification rights

## Field-Level Security

### Template Fields
New template fields should inherit existing template security:
- `mesh_panel_calculation_method`
- `mesh_panel_count_formula`
- `mesh_panel_dimensions_formula`
- `hunt_each_panel_separately`
- `mesh_hunting_priority`

### Operation Fields
New operation fields should inherit existing operation security:
- `panel_number`
- `panel_position`
- `door_number`
- `hunting_priority`
- `mesh_source_type`
- `selected_mesh_id`
- `hunting_notes`

### Configuration Fields
New configuration fields should inherit existing configuration security:
- `mesh_operation_count`

## Menu Security

### Mesh Offcuts Menu
```xml
<menuitem id="menu_mesh_offcuts"
          name="Mesh Offcuts"
          parent="canbrax_configmatrix.menu_mesh_operations_root"
          action="action_mesh_offcut"
          groups="canbrax_configmatrix.group_config_matrix_user,canbrax_configmatrix.group_config_matrix_admin"
          sequence="40"/>
```

### Admin-Only Features
Certain features should be restricted to admins:
- Test Panel Calculation button
- Advanced debugging tools
- Template modification
- System configuration

## Record Rules (if needed)

If multi-company or advanced security is required, consider adding record rules:

```xml
<!-- Example: Company-specific mesh offcuts -->
<record id="mesh_offcut_comp_rule" model="ir.rule">
    <field name="name">Mesh Offcut Company Rule</field>
    <field name="model_id" ref="model_mesh_offcut"/>
    <field name="global" eval="True"/>
    <field name="domain_force">['|', ('company_id','=',False), ('company_id', 'in', company_ids)]</field>
</record>
```

## Security Testing Scenarios

### Admin User
- **Test**: Login as admin user
- **Expected**: Full access to all features
- **Verify**: Can create/modify templates, offcuts, operations

### Standard User
- **Test**: Login as standard user
- **Expected**: Can use hunting features, limited admin access
- **Verify**: Cannot delete records, cannot access admin tools

### Builder User
- **Test**: Login as builder user
- **Expected**: Read-only access to most features
- **Verify**: Can view but not modify records

### Menu Visibility
- **Test**: Check menu visibility for different user groups
- **Expected**: Appropriate menus visible based on permissions
- **Verify**: Admin tools hidden from non-admin users

## Access Rights Matrix

| Model | Admin | User | Builder |
|-------|-------|------|---------|
| mesh.offcut | CRUD | CRU | R |
| mesh.cut.operation | CRUD | CRU | R |
| config.matrix.configuration | CRUD | CRU | R |
| config.matrix.template | CRUD | R | R |

Legend:
- C: Create
- R: Read
- U: Update
- D: Delete

## Implementation Checklist

- [ ] Add mesh.offcut access rights to CSV file
- [ ] Verify existing access rights are sufficient
- [ ] Test admin user permissions
- [ ] Test standard user permissions
- [ ] Test builder user permissions
- [ ] Verify menu visibility rules
- [ ] Test field-level security
- [ ] Confirm debugging tools are admin-only

## Common Security Issues to Avoid

### Over-Permissive Access
- Don't give delete rights to standard users
- Restrict template modification to admins
- Limit debugging tools to admins

### Under-Permissive Access
- Ensure users can create mesh operations
- Allow users to modify their own configurations
- Provide read access to necessary related models

### Missing Dependencies
- Ensure access to related models (product.product, stock.location)
- Verify MRP integration permissions
- Check sale order access if needed

## Testing Checklist

- [ ] Admin can access all features
- [ ] Users can perform standard operations
- [ ] Builders have appropriate read-only access
- [ ] Menu items show/hide correctly
- [ ] No permission errors during normal operations
- [ ] Debugging tools restricted to admins
- [ ] Record creation/modification works as expected

## Next Steps
After completing this task:
1. Test with different user roles
2. Verify no permission errors in normal workflows
3. Proceed to Task 10 (Testing Scenarios)

## Notes
- Security should be tested thoroughly with actual user accounts
- Consider business requirements for different user roles
- Document any custom security requirements
- Test both positive and negative security scenarios
