# -*- coding: utf-8 -*-
{
    'name': 'Matrix - Dynamic Product Configuration',
    'version': '********.6',
    'category': 'Manufacturing/Manufacturing',
    'summary': 'Advanced Product Configuration with Dynamic BOM Generation',
    'description': """
ConfigMatrix
===========

Advanced product configuration system with dynamic BOM generation.

Features:
---------
* Dynamic configuration interface with conditional visibility
* Automatic BOM generation based on configuration
* Visual product previews
* Seamless integration with sales and manufacturing
* Reusable configurations for repeat orders
* Administrative interface for creating and managing configuration templates
    """,
    'author': 'ITMSGROUP',
    'website': 'https://www.itmsgroup.com.au',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'product',
        'sale_management',
        'mrp',
        'stock',
        'sale_stock',
        'sale_mrp',
        'web',
        'website_sale',
        'mail',
    ],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',

        # Data files (load first)
        'data/matrix_category_data.xml',
        'data/operation_price_data.xml',
        'data/mesh_sample_data.xml',

        'data/sample_cut_operations_data.xml',
        'data/mesh_cut_operation_template.xml',
        'data/mesh_operation_mapping.xml',
        'data/mesh_calculated_fields.xml',

        # Category views (load early to define actions)
        'views/config_matrix_category_views.xml',
        'views/config_matrix_excel_tools_views.xml',
        'views/config_matrix_import_export_tools_views.xml',

        # Pricing matrix views (load first to define actions)
        'views/pricing/config_matrix_price_matrix_views.xml',
        'views/pricing/config_matrix_labor_time_matrix_views.xml',
        'views/pricing/config_matrix_template_matrix_assignment_views.xml',
        'views/pricing/config_matrix_import_wizard_views.xml',
        'views/pricing/config_matrix_simple_test_wizard_views.xml',
        'views/pricing/config_matrix_visual_editor_views.xml',

        # Visual editor views
        'views/config_matrix_visual_editor_views.xml',

        # Then load template views that reference the actions
        'views/config_matrix_template_views.xml',
        'views/config_matrix_section_views.xml',
        'views/config_matrix_visibility_condition_views.xml',
        'views/config_matrix_option_visibility_views.xml',
        'views/config_matrix_component_mapping_views.xml',
        'views/config_matrix_operation_template_views.xml',
        'views/config_matrix_operation_price_views.xml',
        'views/config_matrix_operation_mapping_views.xml',
        'views/config_matrix_field_views.xml',
        'views/config_matrix_option_views.xml',
        'views/config_matrix_calculated_field_views.xml',
        'views/config_matrix_configuration_views.xml',
        'views/config_matrix_svg_component_views.xml',

        'views/product_views.xml',
        'views/sale_views.xml',
        'views/sale_order_views.xml',
        'views/mrp_views.xml',
        'views/configurator_templates.xml',
        'views/website_portal/website_portal_templates.xml',

        # Mesh inventory management views
        'views/mesh_cut_matrix_views.xml',

        'views/mesh_cut_operation_views.xml',
        # 'views/stock_lot_mesh_views.xml',  # Temporarily disabled
        # 'views/config_matrix_configuration_mesh_views.xml',  # Temporarily disabled
        # 'views/sale_order_mesh_views.xml',  # Temporarily disabled
        'views/mesh_menu_views.xml',
        'views/config_matrix_settings_views.xml',

        # Menu views (load before wizards that reference menus)
        'views/menu_views.xml',

        # Wizard views (load after menus)
        'wizards/config_matrix_test_formula_wizard_views.xml',
        'wizards/config_matrix_component_builder_views.xml',
        'wizards/config_matrix_operation_builder_views.xml',
        'wizards/config_matrix_json_import_views.xml',
        'wizards/replace_text.xml',
        'wizards/mesh_cut_wizard_views.xml',
        'wizards/mesh_configuration_wizard_views.xml',
        'wizards/mesh_matrix_import_wizard_views.xml',
        'wizards/mesh_sample_stock_wizard_views.xml',
        'wizards/sample_cut_operations_wizard_views.xml',
        'wizards/sample_products_wizard_views.xml',
        'wizards/test_mesh_system_wizard_views.xml',
        'views/config_matrix_field_csv_import_wizard_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'canbrax_configmatrix/static/src/css/configurator.css',
            'canbrax_configmatrix/static/src/css/pricing/enhanced_excel_matrix.css',
            'canbrax_configmatrix/static/src/css/cut_matrix_widget.css',
            'canbrax_configmatrix/static/src/js/configurator.js',
            'canbrax_configmatrix/static/src/js/expression_builder.js',
            'canbrax_configmatrix/static/src/js/matrix_visual_widget.js',
            'canbrax_configmatrix/static/src/js/cut_matrix_widget.js',
            'canbrax_configmatrix/static/src/js/pricing/enhanced_excel_matrix_editor.js',
            'canbrax_configmatrix/static/src/js/pricing/price_matrix_widget.js',
            'canbrax_configmatrix/static/src/js/pricing/dimension_ranges_editor.js',
            'canbrax_configmatrix/static/src/js/visibility_conditions.js',

            'canbrax_configmatrix/static/src/xml/templates.xml',
            'canbrax_configmatrix/static/src/xml/configurator.xml',
            'canbrax_configmatrix/static/src/xml/enhanced_excel_matrix_templates.xml',
            'canbrax_configmatrix/static/src/xml/interactive_matrix_templates.xml',
            'canbrax_configmatrix/static/src/xml/cut_matrix_templates.xml',

            'canbrax_configmatrix/static/src/xml/dimension_ranges_templates.xml',
        ],
        'web.assets_frontend': [
            'canbrax_configmatrix/static/src/css/website_portal/website_portal.css',
            'canbrax_configmatrix/static/src/js/svg_renderer.js',
            'canbrax_configmatrix/static/src/js/svg_components_inline.js',
            'canbrax_configmatrix/static/src/js/enhanced_configurator_solutions.js',
            'canbrax_configmatrix/static/src/js/visibility_conditions.js',
        ],

    },
    'demo': [
    ],
    'images': [
        'static/src/img/matrix.png',
        'static/description/banner.png',
    ],
    'installable': True,
    'application': True,
    'auto_install': False,
    'post_init_hook': 'post_init_hook',
    'uninstall_hook': 'uninstall_hook',
}
