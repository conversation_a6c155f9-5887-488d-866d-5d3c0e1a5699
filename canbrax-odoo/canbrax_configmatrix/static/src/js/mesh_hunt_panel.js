/**
 * Mesh Hunt Panel JavaScript
 * Handles the mesh hunting functionality in the Product Configurator
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[MESH-HUNT] Initializing mesh hunt panel');

    // Initialize mesh panel functionality
    initializeMeshPanel();
});

function initializeMeshPanel() {
    // Show/hide mesh panel
    const showMeshButton = document.getElementById('show-mesh-panel');
    const hideMeshButton = document.getElementById('hide-mesh-panel');
    const meshPanel = document.getElementById('mesh-panel');
    const refreshButton = document.getElementById('mesh-hunt-refresh');

    if (showMeshButton && meshPanel) {
        showMeshButton.addEventListener('click', function() {
            meshPanel.style.display = meshPanel.style.display === 'none' ? 'block' : 'none';
            if (meshPanel.style.display === 'block') {
                loadMeshRequirements();
            }
        });
    }

    if (hideMeshButton && meshPanel) {
        hideMeshButton.addEventListener('click', function() {
            meshPanel.style.display = 'none';
        });
    }

    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            loadMeshRequirements();
            huntMeshComponents();
        });
    }
}

function loadMeshRequirements() {
    console.log('[MESH-HUNT] Loading mesh requirements');

    const requirementsList = document.getElementById('mesh-requirements-list');
    if (!requirementsList) return;

    // Show loading state
    requirementsList.innerHTML = `
        <div class="text-muted text-center p-3">
            <i class="fas fa-spinner fa-spin"></i> Analyzing configuration...
        </div>
    `;

    // Small delay to allow calculated fields to update
    setTimeout(() => {
        // Get current configuration data
        const configData = getCurrentConfigurationData();

        // Extract mesh requirements
        const meshRequirements = extractMeshRequirements(configData);

        // Display requirements
        displayMeshRequirements(meshRequirements);
    }, 100);
}

function getCurrentConfigurationData() {
    const configData = {};

    // Collect all form field values
    const form = document.querySelector('form');
    if (form) {
        const formData = new FormData(form);
        for (let [key, value] of formData.entries()) {
            configData[key] = value;
        }
    }

    // Also collect from any JavaScript variables if available
    if (window.currentConfigData) {
        Object.assign(configData, window.currentConfigData);
    }

    // Try to get calculated fields from the main configurator if available
    if (window.configurator && window.configurator.getCalculatedFields) {
        try {
            const calculatedFields = window.configurator.getCalculatedFields();
            Object.assign(configData, calculatedFields);
            console.log('[MESH-HUNT] ✅ Added calculated fields from configurator:', calculatedFields);
        } catch (e) {
            console.warn('[MESH-HUNT] Could not get calculated fields from configurator:', e);
        }
    }

    // Also try to get from global calculated fields cache
    if (window.calculatedFieldsCache) {
        console.log('[MESH-HUNT] Found global calculated fields cache:', window.calculatedFieldsCache);
        Object.assign(configData, window.calculatedFieldsCache);
    }

    // Try to get from visibility conditions system
    if (window.calculateDynamicFields && typeof window.calculateDynamicFields === 'function') {
        try {
            const dynamicFields = window.calculateDynamicFields(configData);
            Object.assign(configData, dynamicFields);
            console.log('[MESH-HUNT] ✅ Added dynamic fields:', dynamicFields);
        } catch (e) {
            console.warn('[MESH-HUNT] Could not calculate dynamic fields:', e);
        }
    }

    // Check if calculated fields are in the calculated fields debug panel
    console.log('[MESH-HUNT] Looking for calculated fields table...');
    const calculatedFieldsTableBody = document.querySelector('#calculated-fields-table-body');
    if (calculatedFieldsTableBody) {
        console.log('[MESH-HUNT] Found calculated fields table body, extracting calculated fields...');
        const debugRows = calculatedFieldsTableBody.querySelectorAll('tr');
        console.log('[MESH-HUNT] Found', debugRows.length, 'debug rows');

        debugRows.forEach((row, index) => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 2) {
                const fieldName = cells[0].textContent.trim();
                const fieldValue = cells[1].textContent.trim();

                console.log(`[MESH-HUNT] Row ${index}: ${fieldName} = ${fieldValue}`);

                if (fieldName.startsWith('_CALCULATED_')) {
                    // Try to parse as number, boolean, or keep as string
                    let parsedValue = fieldValue;
                    if (fieldValue === 'true') parsedValue = true;
                    else if (fieldValue === 'false') parsedValue = false;
                    else if (fieldValue === 'null') parsedValue = null;
                    else if (!isNaN(fieldValue) && fieldValue !== '') parsedValue = parseFloat(fieldValue);

                    configData[fieldName] = parsedValue;
                    console.log(`[MESH-HUNT] ✅ Added calculated field: ${fieldName} = ${parsedValue} (${typeof parsedValue})`);
                }
            }
        });
    } else {
        console.log('[MESH-HUNT] ❌ Calculated fields table body not found');

        // Try alternative selectors to debug what's available
        const altSelectors = [
            '#calculated-fields-panel',
            '#calculated-fields-table-body',
            '.table',
            'table tbody',
            '[id*="calculated"]'
        ];

        altSelectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                console.log(`[MESH-HUNT] Found element with selector "${selector}":`, element);
                if (selector === '.table' || selector === 'table tbody') {
                    const rows = element.querySelectorAll('tr');
                    console.log(`[MESH-HUNT] This table has ${rows.length} rows`);
                }
            }
        });
    }

    console.log('[MESH-HUNT] Final config data:', configData);
    return configData;
}

function extractMeshRequirements(configData) {
    console.log('[MESH-HUNT] Extracting mesh requirements from config:', configData);
    console.log('[MESH-HUNT] Looking for calculated fields:');
    console.log('[MESH-HUNT] _CALCULATED_mesh_width:', configData['_CALCULATED_mesh_width']);
    console.log('[MESH-HUNT] _CALCULATED_mesh_height:', configData['_CALCULATED_mesh_height']);
    console.log('[MESH-HUNT] _CALCULATED_mesh_required:', configData['_CALCULATED_mesh_required']);
    console.log('[MESH-HUNT] _CALCULATED_mesh_series:', configData['_CALCULATED_mesh_series']);

    const requirements = {
        required: false,
        width: 0,
        height: 0,
        series: 'saltwater',
        area: 0,
        source_fields: []
    };

    // FIRST: Check for calculated mesh fields (highest priority)
    const calcWidth = configData['_CALCULATED_mesh_width'];
    const calcHeight = configData['_CALCULATED_mesh_height'];

    console.log('[MESH-HUNT] Calculated width check:', calcWidth, typeof calcWidth);
    console.log('[MESH-HUNT] Calculated height check:', calcHeight, typeof calcHeight);

    if (calcWidth !== undefined && calcWidth !== null && calcWidth !== 0 &&
        calcHeight !== undefined && calcHeight !== null && calcHeight !== 0) {

        requirements.width = parseFloat(calcWidth) || 0;
        requirements.height = parseFloat(calcHeight) || 0;
        requirements.source_fields.push('Calculated mesh fields');

        // Check for calculated series
        if (configData['_CALCULATED_mesh_series']) {
            requirements.series = configData['_CALCULATED_mesh_series'];
        }

        // Check for calculated required flag
        if (configData['_CALCULATED_mesh_required'] !== undefined) {
            requirements.required = Boolean(configData['_CALCULATED_mesh_required']);
        }

        console.log('[MESH-HUNT] ✅ Using calculated mesh fields:', {
            width: requirements.width,
            height: requirements.height,
            series: requirements.series,
            required: requirements.required
        });
    } else {
        console.log('[MESH-HUNT] ❌ Calculated mesh fields not found or zero, falling back to manual detection');
    }

    // FALLBACK: Try to find door dimensions from various field patterns if calculated fields not available
    if (requirements.width === 0 || requirements.height === 0) {
        const dimensionFields = [
            // Manual door dimensions
            'bx_dbl_hinge_make_left_door_height_mm_manual',
            'bx_dbl_hinge_make_right_door_height_mm_manual',
            'bx_dbl_hinge_make_left_door_top_width_mm_manual',
            'bx_dbl_hinge_make_right_door_top_width_mm_manual',
            // Generic patterns
            'door_width', 'width', 'opening_width',
            'door_height', 'height', 'opening_height'
        ];

        // Extract height (use the largest door height)
        const leftHeight = parseFloat(configData['bx_dbl_hinge_make_left_door_height_mm_manual']) || 0;
        const rightHeight = parseFloat(configData['bx_dbl_hinge_make_right_door_height_mm_manual']) || 0;

        if (leftHeight > 0 || rightHeight > 0) {
            requirements.height = Math.max(leftHeight, rightHeight);
            requirements.source_fields.push('Manual door heights');
        }

        // Extract width (use the largest door width)
        const leftWidth = parseFloat(configData['bx_dbl_hinge_make_left_door_top_width_mm_manual']) || 0;
        const rightWidth = parseFloat(configData['bx_dbl_hinge_make_right_door_top_width_mm_manual']) || 0;

        if (leftWidth > 0 || rightWidth > 0) {
            requirements.width = Math.max(leftWidth, rightWidth);
            requirements.source_fields.push('Manual door widths');
        }

        // Fallback to generic fields if manual fields not found
        if (requirements.width === 0) {
            for (const field of ['door_width', 'width', 'opening_width']) {
                const value = parseFloat(configData[field]) || 0;
                if (value > 0) {
                    requirements.width = value;
                    requirements.source_fields.push(field);
                    break;
                }
            }
        }

        if (requirements.height === 0) {
            for (const field of ['door_height', 'height', 'opening_height']) {
                const value = parseFloat(configData[field]) || 0;
                if (value > 0) {
                    requirements.height = value;
                    requirements.source_fields.push(field);
                    break;
                }
            }
        }
    }

    // Set defaults if no dimensions found (changed from 900x2100 to 100x100)
    if (requirements.width === 0) requirements.width = 100;
    if (requirements.height === 0) requirements.height = 100;

    // Calculate area and determine if mesh is required
    requirements.area = requirements.width * requirements.height;
    requirements.required = requirements.width > 0 && requirements.height > 0;

    console.log('[MESH-HUNT] Extracted requirements:', requirements);
    return requirements;
}

function displayMeshRequirements(requirements) {
    const requirementsList = document.getElementById('mesh-requirements-list');
    if (!requirementsList) return;

    let html = '';
    
    if (requirements.required) {
        html = `
            <div class="alert alert-success mb-2">
                <strong><i class="fas fa-check-circle"></i> Mesh Required</strong>
            </div>
            <table class="table table-sm">
                <tr>
                    <td><strong>Width:</strong></td>
                    <td>${requirements.width.toFixed(0)}mm</td>
                </tr>
                <tr>
                    <td><strong>Height:</strong></td>
                    <td>${requirements.height.toFixed(0)}mm</td>
                </tr>
                <tr>
                    <td><strong>Area:</strong></td>
                    <td>${(requirements.area / 1000000).toFixed(2)}m²</td>
                </tr>
                <tr>
                    <td><strong>Series:</strong></td>
                    <td>${requirements.series}</td>
                </tr>
            </table>
        `;
        
        if (requirements.source_fields.length > 0) {
            html += `
                <div class="mt-2">
                    <small class="text-muted">
                        <strong>Source:</strong> ${requirements.source_fields.join(', ')}
                    </small>
                </div>
            `;
        }
    } else {
        html = `
            <div class="alert alert-warning">
                <strong><i class="fas fa-exclamation-triangle"></i> No Mesh Required</strong>
                <p class="mb-0 mt-2">No valid door dimensions found in configuration.</p>
            </div>
        `;
    }

    requirementsList.innerHTML = html;
}

function huntMeshComponents() {
    console.log('[MESH-HUNT] Hunting for mesh components');
    
    const componentsList = document.getElementById('mesh-components-list');
    if (!componentsList) return;

    // Show loading state
    componentsList.innerHTML = `
        <div class="text-muted text-center p-3">
            <i class="fas fa-spinner fa-spin"></i> Hunting mesh components...
        </div>
    `;

    // Get mesh requirements
    const configData = getCurrentConfigurationData();
    const requirements = extractMeshRequirements(configData);

    if (!requirements.required) {
        componentsList.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> No mesh hunting needed - no valid dimensions found.
            </div>
        `;
        return;
    }

    // Simulate mesh hunting (in real implementation, this would call the backend)
    setTimeout(() => {
        displayMeshHuntResults(requirements);
    }, 1000);
}

function displayMeshHuntResults(requirements) {
    const componentsList = document.getElementById('mesh-components-list');
    if (!componentsList) return;

    // This is a mock implementation - in reality, this would call the backend mesh hunting system
    const mockResults = [
        {
            type: 'master',
            product_name: 'Saltwater Master Sheet 1100x620mm',
            dimensions: '1100x620mm',
            efficiency: 85,
            waste: '15%',
            available_qty: 5
        },
        {
            type: 'planned',
            product_name: 'Saltwater Planned Off-cut 900x800mm',
            dimensions: '900x800mm',
            efficiency: 92,
            waste: '8%',
            available_qty: 2
        }
    ];

    let html = `
        <div class="alert alert-success mb-2">
            <strong><i class="fas fa-search"></i> Found ${mockResults.length} suitable options</strong>
        </div>
    `;

    mockResults.forEach((result, index) => {
        const typeIcon = result.type === 'master' ? 'fa-square' : 'fa-cut';
        const typeColor = result.type === 'master' ? 'primary' : 'success';
        
        html += `
            <div class="card mb-2">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas ${typeIcon} text-${typeColor}"></i>
                                ${result.product_name}
                            </h6>
                            <small class="text-muted">${result.dimensions}</small>
                        </div>
                        <span class="badge bg-${typeColor}">${result.type}</span>
                    </div>
                    <div class="mt-2">
                        <div class="row">
                            <div class="col-6">
                                <small><strong>Efficiency:</strong> ${result.efficiency}%</small>
                            </div>
                            <div class="col-6">
                                <small><strong>Available:</strong> ${result.available_qty}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
        <div class="mt-3">
            <button class="btn btn-primary btn-sm" onclick="selectMeshComponent(0)">
                <i class="fas fa-check"></i> Select Best Option
            </button>
        </div>
    `;

    componentsList.innerHTML = html;
}

function selectMeshComponent(index) {
    console.log('[MESH-HUNT] Selected mesh component at index:', index);
    
    // Show success message
    const componentsList = document.getElementById('mesh-components-list');
    if (componentsList) {
        const successHtml = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> 
                <strong>Mesh component selected!</strong>
                <p class="mb-0 mt-2">The optimal mesh component has been assigned to this configuration.</p>
            </div>
        `;
        componentsList.innerHTML = successHtml;
    }
}

// Auto-refresh mesh panel when calculated fields change
function setupAutoRefresh() {
    // Listen for calculated fields updates
    document.addEventListener('calculatedFieldsUpdated', function() {
        console.log('[MESH-HUNT] Calculated fields updated, refreshing mesh panel');
        const meshPanel = document.getElementById('mesh-panel');
        if (meshPanel && meshPanel.style.display !== 'none') {
            loadMeshRequirements();
        }
    });

    // Also refresh when form fields change (with debouncing)
    let refreshTimeout;
    document.addEventListener('input', function(e) {
        if (e.target.form) {
            clearTimeout(refreshTimeout);
            refreshTimeout = setTimeout(() => {
                const meshPanel = document.getElementById('mesh-panel');
                if (meshPanel && meshPanel.style.display !== 'none') {
                    loadMeshRequirements();
                }
            }, 500); // 500ms debounce
        }
    });
}

// Initialize auto-refresh when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setupAutoRefresh();
});

// Make functions available globally
window.meshHuntPanel = {
    initializeMeshPanel,
    loadMeshRequirements,
    huntMeshComponents,
    selectMeshComponent,
    setupAutoRefresh
};
