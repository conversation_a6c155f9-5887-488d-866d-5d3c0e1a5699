/**
 * Mesh Hunt Panel JavaScript
 * Handles the mesh hunting functionality in the Product Configurator
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[MESH-HUNT] Initializing mesh hunt panel');

    // Initialize mesh panel functionality
    initializeMeshPanel();
});

function initializeMeshPanel() {
    // Show/hide mesh panel
    const showMeshButton = document.getElementById('show-mesh-panel');
    const hideMeshButton = document.getElementById('hide-mesh-panel');
    const meshPanel = document.getElementById('mesh-panel');
    const refreshButton = document.getElementById('mesh-hunt-refresh');

    if (showMeshButton && meshPanel) {
        showMeshButton.addEventListener('click', function() {
            meshPanel.style.display = meshPanel.style.display === 'none' ? 'block' : 'none';
            if (meshPanel.style.display === 'block') {
                loadMeshRequirements();
            }
        });
    }

    if (hideMeshButton && meshPanel) {
        hideMeshButton.addEventListener('click', function() {
            meshPanel.style.display = 'none';
        });
    }

    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            loadMeshRequirements();
            huntMeshComponents();
        });
    }
}

function loadMeshRequirements() {
    console.log('[MESH-HUNT] Loading mesh requirements');

    const requirementsList = document.getElementById('mesh-requirements-list');
    if (!requirementsList) return;

    // Show loading state
    requirementsList.innerHTML = `
        <div class="text-muted text-center p-3">
            <i class="fas fa-spinner fa-spin"></i> Analyzing configuration...
        </div>
    `;

    // Get configuration ID
    const configId = getConfigurationId();
    if (!configId) {
        // Fallback to client-side calculation
        setTimeout(() => {
            const configData = getCurrentConfigurationData();
            const meshRequirements = extractMeshRequirements(configData);
            displayMeshRequirements(meshRequirements);
        }, 100);
        return;
    }

    // Call backend to get mesh panel requirements
    fetch('/config_matrix/get_mesh_panel_requirements', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            jsonrpc: '2.0',
            method: 'call',
            params: {
                config_id: configId
            }
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.result && data.result.success) {
            displayBackendMeshRequirements(data.result.requirements);
        } else {
            // Fallback to client-side calculation
            const configData = getCurrentConfigurationData();
            const meshRequirements = extractMeshRequirements(configData);
            displayMeshRequirements(meshRequirements);
        }
    })
    .catch(error => {
        console.error('[MESH-HUNT] Error loading requirements:', error);
        // Fallback to client-side calculation
        const configData = getCurrentConfigurationData();
        const meshRequirements = extractMeshRequirements(configData);
        displayMeshRequirements(meshRequirements);
    });
}

function getCurrentConfigurationData() {
    const configData = {};

    // Collect all form field values
    const form = document.querySelector('form');
    if (form) {
        const formData = new FormData(form);
        for (let [key, value] of formData.entries()) {
            configData[key] = value;
        }
    }

    // Also collect from any JavaScript variables if available
    if (window.currentConfigData) {
        Object.assign(configData, window.currentConfigData);
    }

    // Try to get calculated fields from the main configurator if available
    if (window.configurator && window.configurator.getCalculatedFields) {
        try {
            const calculatedFields = window.configurator.getCalculatedFields();
            Object.assign(configData, calculatedFields);
            console.log('[MESH-HUNT] ✅ Added calculated fields from configurator:', calculatedFields);
        } catch (e) {
            console.warn('[MESH-HUNT] Could not get calculated fields from configurator:', e);
        }
    }

    // Also try to get from global calculated fields cache
    if (window.calculatedFieldsCache) {
        console.log('[MESH-HUNT] Found global calculated fields cache:', window.calculatedFieldsCache);
        Object.assign(configData, window.calculatedFieldsCache);
    }

    // Try to get from visibility conditions system
    if (window.calculateDynamicFields && typeof window.calculateDynamicFields === 'function') {
        try {
            const dynamicFields = window.calculateDynamicFields(configData);
            Object.assign(configData, dynamicFields);
            console.log('[MESH-HUNT] ✅ Added dynamic fields:', dynamicFields);
        } catch (e) {
            console.warn('[MESH-HUNT] Could not calculate dynamic fields:', e);
        }
    }

    // Check if calculated fields are in the calculated fields debug panel
    console.log('[MESH-HUNT] Looking for calculated fields table...');
    const calculatedFieldsTableBody = document.querySelector('#calculated-fields-table-body');
    if (calculatedFieldsTableBody) {
        console.log('[MESH-HUNT] Found calculated fields table body, extracting calculated fields...');
        const debugRows = calculatedFieldsTableBody.querySelectorAll('tr');
        console.log('[MESH-HUNT] Found', debugRows.length, 'debug rows');

        debugRows.forEach((row, index) => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 2) {
                const fieldName = cells[0].textContent.trim();
                const fieldValue = cells[1].textContent.trim();

                console.log(`[MESH-HUNT] Row ${index}: ${fieldName} = ${fieldValue}`);

                if (fieldName.startsWith('_CALCULATED_')) {
                    // Try to parse as number, boolean, or keep as string
                    let parsedValue = fieldValue;
                    if (fieldValue === 'true') parsedValue = true;
                    else if (fieldValue === 'false') parsedValue = false;
                    else if (fieldValue === 'null') parsedValue = null;
                    else if (!isNaN(fieldValue) && fieldValue !== '') parsedValue = parseFloat(fieldValue);

                    configData[fieldName] = parsedValue;
                    console.log(`[MESH-HUNT] ✅ Added calculated field: ${fieldName} = ${parsedValue} (${typeof parsedValue})`);
                }
            }
        });
    } else {
        console.log('[MESH-HUNT] ❌ Calculated fields table body not found');

        // Try alternative selectors to debug what's available
        const altSelectors = [
            '#calculated-fields-panel',
            '#calculated-fields-table-body',
            '.table',
            'table tbody',
            '[id*="calculated"]'
        ];

        altSelectors.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                console.log(`[MESH-HUNT] Found element with selector "${selector}":`, element);
                if (selector === '.table' || selector === 'table tbody') {
                    const rows = element.querySelectorAll('tr');
                    console.log(`[MESH-HUNT] This table has ${rows.length} rows`);
                }
            }
        });
    }

    console.log('[MESH-HUNT] Final config data:', configData);
    return configData;
}

function getPanelQuantity(configData) {
    console.log('[MESH-HUNT] Getting panel quantity...');

    // Check if there's a panel_quantity field in config
    if (configData['panel_quantity']) {
        const qty = parseInt(configData['panel_quantity']) || 1;
        console.log('[MESH-HUNT] Panel quantity from config:', qty);
        return qty;
    }

    // Check calculated fields for panel count
    if (configData['_CALCULATED_panel_count']) {
        const qty = parseInt(configData['_CALCULATED_panel_count']) || 1;
        console.log('[MESH-HUNT] Panel quantity from calculated field:', qty);
        return qty;
    }

    // Check template data if available
    if (window.templateData && window.templateData.panel_quantity) {
        const qty = parseInt(window.templateData.panel_quantity) || 1;
        console.log('[MESH-HUNT] Panel quantity from templateData:', qty);
        return qty;
    }

    // NEW: Try to get template ID and fetch panel quantity from backend
    const templateId = getTemplateIdFromUrl();
    if (templateId) {
        console.log('[MESH-HUNT] Fetching panel quantity from template ID:', templateId);
        const qty = getTemplatePanelQuantitySync(templateId);
        if (qty > 1) {
            console.log('[MESH-HUNT] ✅ Panel quantity from backend template:', qty);
            return qty;
        }
    }

    // Default to single panel
    console.log('[MESH-HUNT] ❌ Defaulting to 1 panel');
    return 1;
}

function getTemplateIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('template_id') || urlParams.get('matrix_id');
}

function getTemplatePanelQuantitySync(templateId) {
    try {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/web/dataset/call_kw', false); // synchronous
        xhr.setRequestHeader('Content-Type', 'application/json');
        const requestData = {
            jsonrpc: '2.0',
            method: 'call',
            params: {
                model: 'config.matrix.template',
                method: 'read',
                args: [[parseInt(templateId)], ['panel_quantity']],
                kwargs: {}
            }
        };
        xhr.send(JSON.stringify(requestData));
        if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.result && response.result.length > 0) {
                const panelQty = parseInt(response.result[0].panel_quantity) || 1;
                return panelQty;
            }
        }
    } catch (e) {
        console.warn('[MESH-HUNT] Failed to fetch panel quantity from backend:', e);
    }
    return 1; // fallback
}

function extractMultiPanelRequirements(configData, panelQuantity) {
    console.log('[MESH-HUNT] Extracting multi-panel requirements for', panelQuantity, 'panels');

    const requirements = {
        required: false,
        panels: [],
        total_panels: panelQuantity,
        series: 'saltwater',
        source_fields: []
    };

    // Try multiple possible field names for door dimensions
    const doorWidthFields = [
        '_CALCULATED_largest_door_width',
        '_CALCULATED_door_width',
        '_CALCULATED_width',
        '_CALCULATED_right_door_width_with_stile',
        '_CALCULATED_left_door_width_with_stile'
    ];

    const doorHeightFields = [
        '_CALCULATED_largest_door_height',
        '_CALCULATED_door_height',
        '_CALCULATED_height',
        '_CALCULATED_right_door_height',
        '_CALCULATED_left_door_height'
    ];

    const midrailHeightFields = [
        '_CALCULATED_midrail_height',
        '_CALCULATED_manual_left_height',
        '_CALCULATED_manual_right_height'
    ];

    // Find door width
    let doorWidth = 0;
    let doorWidthField = '';
    for (const field of doorWidthFields) {
        const value = parseFloat(configData[field]) || 0;
        if (value > 0) {
            doorWidth = value;
            doorWidthField = field;
            break;
        }
    }

    // Find door height
    let doorHeight = 0;
    let doorHeightField = '';
    for (const field of doorHeightFields) {
        const value = parseFloat(configData[field]) || 0;
        if (value > 0) {
            doorHeight = value;
            doorHeightField = field;
            break;
        }
    }

    // Find midrail height
    let midrailHeight = 0;
    let midrailHeightField = '';
    for (const field of midrailHeightFields) {
        const value = parseFloat(configData[field]) || 0;
        if (value > 0) {
            midrailHeight = value;
            midrailHeightField = field;
            break;
        }
    }

    console.log('[MESH-HUNT] Found dimensions:', {
        doorWidth, doorWidthField,
        doorHeight, doorHeightField,
        midrailHeight, midrailHeightField
    });

    if (!doorWidth || !doorHeight) {
        console.log('[MESH-HUNT] ❌ Missing door dimensions:', {doorWidth, doorHeight});
        console.log('[MESH-HUNT] Available config data keys:', Object.keys(configData).filter(k => k.includes('CALCULATED')));
        console.log('[MESH-HUNT] All width-related fields:', Object.keys(configData).filter(k => k.toLowerCase().includes('width')));
        console.log('[MESH-HUNT] All height-related fields:', Object.keys(configData).filter(k => k.toLowerCase().includes('height')));
        return requirements;
    }

    // Get mesh series
    if (configData['_CALCULATED_mesh_series']) {
        requirements.series = configData['_CALCULATED_mesh_series'];
    }

    // Calculate panel dimensions based on quantity
    const panels = calculatePanelDimensions(panelQuantity, doorWidth, doorHeight, midrailHeight);

    requirements.panels = panels;
    requirements.required = panels.length > 0;

    // Add source field information
    const sourceFields = ['Multi-panel calculation'];
    if (doorWidthField) sourceFields.push(`Width: ${doorWidthField}`);
    if (doorHeightField) sourceFields.push(`Height: ${doorHeightField}`);
    if (midrailHeightField) sourceFields.push(`Midrail: ${midrailHeightField}`);
    requirements.source_fields = sourceFields;

    console.log('[MESH-HUNT] ✅ Multi-panel requirements:', requirements);
    return requirements;
}

function calculatePanelDimensions(panelQuantity, doorWidth, doorHeight, midrailHeight) {
    const panels = [];

    if (panelQuantity === 1) {
        // Single panel - full door
        panels.push({
            panel_number: 1,
            total_panels: 1,
            width: doorWidth,
            height: doorHeight,
            position: 'full',
            door_reference: 'Door',
            name: `Door Full Panel (${doorWidth.toFixed(0)}x${doorHeight.toFixed(0)}mm)`,
            area: doorWidth * doorHeight
        });
    } else if (panelQuantity === 2) {
        // Two panels - split at midrail
        const topHeight = midrailHeight || (doorHeight / 2);
        const bottomHeight = doorHeight - topHeight;

        panels.push({
            panel_number: 1,
            total_panels: 2,
            width: doorWidth,
            height: topHeight,
            position: 'top',
            door_reference: 'Door',
            name: `Door Top Panel (${doorWidth.toFixed(0)}x${topHeight.toFixed(0)}mm)`,
            area: doorWidth * topHeight
        });

        panels.push({
            panel_number: 2,
            total_panels: 2,
            width: doorWidth,
            height: bottomHeight,
            position: 'bottom',
            door_reference: 'Door',
            name: `Door Bottom Panel (${doorWidth.toFixed(0)}x${bottomHeight.toFixed(0)}mm)`,
            area: doorWidth * bottomHeight
        });
    } else if (panelQuantity === 4) {
        // Four panels - two doors, each split at midrail
        const topHeight = midrailHeight || (doorHeight / 2);
        const bottomHeight = doorHeight - topHeight;

        for (let doorNum = 1; doorNum <= 2; doorNum++) {
            const doorLabel = `Door ${doorNum}`;
            const panelOffset = (doorNum - 1) * 2;

            panels.push({
                panel_number: panelOffset + 1,
                total_panels: 4,
                width: doorWidth,
                height: topHeight,
                position: 'top',
                door_reference: doorLabel,
                name: `${doorLabel} Top Panel (${doorWidth.toFixed(0)}x${topHeight.toFixed(0)}mm)`,
                area: doorWidth * topHeight
            });

            panels.push({
                panel_number: panelOffset + 2,
                total_panels: 4,
                width: doorWidth,
                height: bottomHeight,
                position: 'bottom',
                door_reference: doorLabel,
                name: `${doorLabel} Bottom Panel (${doorWidth.toFixed(0)}x${bottomHeight.toFixed(0)}mm)`,
                area: doorWidth * bottomHeight
            });
        }
    } else {
        // Other quantities - create identical panels
        for (let i = 1; i <= panelQuantity; i++) {
            const doorNum = Math.ceil(i / 2);
            const doorLabel = panelQuantity > 2 ? `Door ${doorNum}` : 'Door';

            panels.push({
                panel_number: i,
                total_panels: panelQuantity,
                width: doorWidth,
                height: doorHeight,
                position: `panel_${i}`,
                door_reference: doorLabel,
                name: `Panel ${i} of ${panelQuantity} (${doorWidth.toFixed(0)}x${doorHeight.toFixed(0)}mm)`,
                area: doorWidth * doorHeight
            });
        }
    }

    return panels;
}

function getConfigurationId() {
    // Try to get configuration ID from various sources

    // Check URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('config_id')) {
        return urlParams.get('config_id');
    }

    // Check global variables
    if (window.configurationId) {
        return window.configurationId;
    }

    if (window.currentConfig && window.currentConfig.id) {
        return window.currentConfig.id;
    }

    // Check form data
    const configIdInput = document.querySelector('input[name="config_id"]');
    if (configIdInput && configIdInput.value) {
        return configIdInput.value;
    }

    // Check if we're in a configuration form
    const configForm = document.querySelector('form[data-config-id]');
    if (configForm) {
        return configForm.getAttribute('data-config-id');
    }

    return null;
}

function displayMeshOperationsResults(result) {
    const componentsList = document.getElementById('mesh-components-list');
    if (!componentsList) return;

    const operationsCount = result.operations_created || 0;
    const operations = result.operations || [];

    let html = `
        <div class="alert alert-success mb-3">
            <strong><i class="fas fa-check-circle"></i> Created ${operationsCount} Mesh Operations</strong>
        </div>
    `;

    if (operations.length > 0) {
        html += `
            <div class="mb-3">
                <h6><i class="fas fa-list"></i> Mesh Cut Operations:</h6>
            </div>
        `;

        operations.forEach((operation, index) => {
            const statusBadge = operation.mesh_found ?
                '<span class="badge badge-success"><i class="fas fa-check"></i> Mesh Found</span>' :
                '<span class="badge badge-warning"><i class="fas fa-search"></i> Hunting Required</span>';

            html += `
                <div class="card mb-2">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge badge-primary">Panel ${operation.panel_number}</span>
                                <strong>${operation.name}</strong>
                            </div>
                            <div>
                                ${statusBadge}
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-cog"></i> Operation ID: ${operation.id} |
                                <i class="fas fa-info-circle"></i> State: ${operation.state}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
            <div class="mt-3">
                <button type="button" class="btn btn-primary btn-sm" onclick="viewMeshOperations()">
                    <i class="fas fa-eye"></i> View All Operations
                </button>
                <button type="button" class="btn btn-secondary btn-sm ml-2" onclick="huntMeshComponents()">
                    <i class="fas fa-redo"></i> Recreate Operations
                </button>
            </div>
        `;
    }

    componentsList.innerHTML = html;
}

function viewMeshOperations() {
    const configId = getConfigurationId();
    if (configId) {
        // Open mesh operations in a new window/tab
        window.open(`/web#model=mesh.cut.operation&view_type=list&domain=[('configuration_id','=',${configId})]`, '_blank');
    }
}

function displayMultiPanelRequirements(requirements) {
    const totalPanels = requirements.panels.length;
    const totalArea = requirements.panels.reduce((sum, panel) => sum + panel.area, 0);

    let html = `
        <div class="alert alert-success mb-2">
            <strong><i class="fas fa-check-circle"></i> Mesh Required - ${totalPanels} Panels</strong>
        </div>
        <div class="mb-3">
            <table class="table table-sm">
                <tr>
                    <td><strong>Total Panels:</strong></td>
                    <td>${totalPanels}</td>
                </tr>
                <tr>
                    <td><strong>Total Area:</strong></td>
                    <td>${(totalArea / 1000000).toFixed(2)}m²</td>
                </tr>
                <tr>
                    <td><strong>Series:</strong></td>
                    <td>${requirements.series}</td>
                </tr>
            </table>
        </div>
        <div class="panel-list">
            <h6><i class="fas fa-list"></i> Individual Panels:</h6>
    `;

    requirements.panels.forEach((panel, index) => {
        const badgeClass = index % 2 === 0 ? 'badge-primary' : 'badge-secondary';
        html += `
            <div class="card mb-2">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge ${badgeClass}">Panel ${panel.panel_number}</span>
                            <strong>${panel.name}</strong>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">${panel.door_reference} - ${panel.position}</small><br>
                            <small><strong>${(panel.area / 1000000).toFixed(3)}m²</strong></small>
                        </div>
                    </div>
                    <div class="mt-1">
                        <small class="text-muted">
                            <i class="fas fa-ruler-combined"></i> ${panel.width.toFixed(0)}mm × ${panel.height.toFixed(0)}mm
                        </small>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    return html;
}

function displayBackendMeshRequirements(requirements) {
    const requirementsList = document.getElementById('mesh-requirements-list');
    if (!requirementsList) return;

    let html = '';

    if (!requirements.required) {
        html = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> No mesh required for this configuration.
            </div>
        `;
    } else {
        const totalPanels = requirements.total_panels || 0;
        const panels = requirements.panels || [];
        const totalArea = panels.reduce((sum, panel) => sum + (panel.area || 0), 0);

        html = `
            <div class="alert alert-success mb-2">
                <strong><i class="fas fa-check-circle"></i> Mesh Required - ${totalPanels} Panel${totalPanels > 1 ? 's' : ''}</strong>
            </div>
            <div class="mb-3">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Total Panels:</strong></td>
                        <td>${totalPanels}</td>
                    </tr>
                    <tr>
                        <td><strong>Total Area:</strong></td>
                        <td>${(totalArea / 1000000).toFixed(2)}m²</td>
                    </tr>
                    <tr>
                        <td><strong>Series:</strong></td>
                        <td>${requirements.mesh_series || 'Unknown'}</td>
                    </tr>
                </table>
            </div>
        `;

        if (panels.length > 0) {
            html += `
                <div class="panel-list">
                    <h6><i class="fas fa-list"></i> Individual Panels:</h6>
            `;

            panels.forEach((panel, index) => {
                const badgeClass = index % 2 === 0 ? 'badge-primary' : 'badge-secondary';
                html += `
                    <div class="card mb-2">
                        <div class="card-body p-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="badge ${badgeClass}">Panel ${panel.panel_number}</span>
                                    <strong>${panel.name}</strong>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">${panel.door_reference} - ${panel.panel_position}</small><br>
                                    <small><strong>${((panel.area || 0) / 1000000).toFixed(3)}m²</strong></small>
                                </div>
                            </div>
                            <div class="mt-1">
                                <small class="text-muted">
                                    <i class="fas fa-ruler-combined"></i> ${(panel.width || 0).toFixed(0)}mm × ${(panel.height || 0).toFixed(0)}mm
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
        }
    }

    requirementsList.innerHTML = html;
}

function displayMultiPanelHuntResults(requirements) {
    const componentsList = document.getElementById('mesh-components-list');
    if (!componentsList) return;

    const totalPanels = requirements.panels.length;

    let html = `
        <div class="alert alert-success mb-2">
            <strong><i class="fas fa-search"></i> Hunting Results - ${totalPanels} Panels</strong>
        </div>
        <div class="mb-3">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i> Each panel is hunted individually for optimal material usage
            </small>
        </div>
    `;

    // Hunt each panel individually (mock results for now)
    requirements.panels.forEach((panel, index) => {
        const huntResult = generateMockHuntResult(panel, requirements.series);
        const badgeClass = huntResult.success ? 'badge-success' : 'badge-warning';
        const statusIcon = huntResult.success ? 'fa-check-circle' : 'fa-exclamation-triangle';

        html += `
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge badge-primary">Panel ${panel.panel_number}</span>
                        <strong>${panel.name}</strong>
                    </div>
                    <span class="badge ${badgeClass}">
                        <i class="fas ${statusIcon}"></i> ${huntResult.status}
                    </span>
                </div>
                <div class="card-body">
                    ${huntResult.success ? displayPanelHuntSuccess(huntResult) : displayPanelHuntFailure(huntResult)}
                </div>
            </div>
        `;
    });

    html += `
        <div class="mt-3">
            <button type="button" class="btn btn-primary btn-sm" onclick="confirmAllPanelSelections()">
                <i class="fas fa-check"></i> Confirm All Selections
            </button>
            <button type="button" class="btn btn-secondary btn-sm ml-2" onclick="huntMeshComponents()">
                <i class="fas fa-redo"></i> Re-hunt All
            </button>
        </div>
    `;

    componentsList.innerHTML = html;
}

function extractMeshRequirements(configData) {
    console.log('[MESH-HUNT] Extracting mesh requirements from config:', configData);
    console.log('[MESH-HUNT] Looking for calculated fields:');
    console.log('[MESH-HUNT] _CALCULATED_mesh_width:', configData['_CALCULATED_mesh_width']);
    console.log('[MESH-HUNT] _CALCULATED_mesh_height:', configData['_CALCULATED_mesh_height']);
    console.log('[MESH-HUNT] _CALCULATED_mesh_required:', configData['_CALCULATED_mesh_required']);
    console.log('[MESH-HUNT] _CALCULATED_mesh_series:', configData['_CALCULATED_mesh_series']);

    // Check if individual panel hunting is enabled
    const panelQuantity = getPanelQuantity(configData);
    console.log('[MESH-HUNT] Panel quantity detected:', panelQuantity);

    if (panelQuantity > 1) {
        return extractMultiPanelRequirements(configData, panelQuantity);
    }

    // Single panel requirements (legacy)
    const requirements = {
        required: false,
        width: 0,
        height: 0,
        series: 'saltwater',
        area: 0,
        source_fields: [],
        panels: []
    };

    // FIRST: Check for calculated mesh fields (highest priority)
    const calcWidth = configData['_CALCULATED_mesh_width'];
    const calcHeight = configData['_CALCULATED_mesh_height'];

    console.log('[MESH-HUNT] Calculated width check:', calcWidth, typeof calcWidth);
    console.log('[MESH-HUNT] Calculated height check:', calcHeight, typeof calcHeight);

    if (calcWidth !== undefined && calcWidth !== null && calcWidth !== 0 &&
        calcHeight !== undefined && calcHeight !== null && calcHeight !== 0) {

        requirements.width = parseFloat(calcWidth) || 0;
        requirements.height = parseFloat(calcHeight) || 0;
        requirements.source_fields.push('Calculated mesh fields');

        // Check for calculated series
        if (configData['_CALCULATED_mesh_series']) {
            requirements.series = configData['_CALCULATED_mesh_series'];
        }

        // Check for calculated required flag
        if (configData['_CALCULATED_mesh_required'] !== undefined) {
            requirements.required = Boolean(configData['_CALCULATED_mesh_required']);
        }

        console.log('[MESH-HUNT] ✅ Using calculated mesh fields:', {
            width: requirements.width,
            height: requirements.height,
            series: requirements.series,
            required: requirements.required
        });
    } else {
        console.log('[MESH-HUNT] ❌ Calculated mesh fields not found or zero, falling back to manual detection');
    }

    // FALLBACK: Try to find door dimensions from various field patterns if calculated fields not available
    if (requirements.width === 0 || requirements.height === 0) {
        const dimensionFields = [
            // Manual door dimensions
            'bx_dbl_hinge_make_left_door_height_mm_manual',
            'bx_dbl_hinge_make_right_door_height_mm_manual',
            'bx_dbl_hinge_make_left_door_top_width_mm_manual',
            'bx_dbl_hinge_make_right_door_top_width_mm_manual',
            // Generic patterns
            'door_width', 'width', 'opening_width',
            'door_height', 'height', 'opening_height'
        ];

        // Extract height (use the largest door height)
        const leftHeight = parseFloat(configData['bx_dbl_hinge_make_left_door_height_mm_manual']) || 0;
        const rightHeight = parseFloat(configData['bx_dbl_hinge_make_right_door_height_mm_manual']) || 0;

        if (leftHeight > 0 || rightHeight > 0) {
            requirements.height = Math.max(leftHeight, rightHeight);
            requirements.source_fields.push('Manual door heights');
        }

        // Extract width (use the largest door width)
        const leftWidth = parseFloat(configData['bx_dbl_hinge_make_left_door_top_width_mm_manual']) || 0;
        const rightWidth = parseFloat(configData['bx_dbl_hinge_make_right_door_top_width_mm_manual']) || 0;

        if (leftWidth > 0 || rightWidth > 0) {
            requirements.width = Math.max(leftWidth, rightWidth);
            requirements.source_fields.push('Manual door widths');
        }

        // Fallback to generic fields if manual fields not found
        if (requirements.width === 0) {
            for (const field of ['door_width', 'width', 'opening_width']) {
                const value = parseFloat(configData[field]) || 0;
                if (value > 0) {
                    requirements.width = value;
                    requirements.source_fields.push(field);
                    break;
                }
            }
        }

        if (requirements.height === 0) {
            for (const field of ['door_height', 'height', 'opening_height']) {
                const value = parseFloat(configData[field]) || 0;
                if (value > 0) {
                    requirements.height = value;
                    requirements.source_fields.push(field);
                    break;
                }
            }
        }
    }

    // Set defaults if no dimensions found (changed from 900x2100 to 100x100)
    if (requirements.width === 0) requirements.width = 100;
    if (requirements.height === 0) requirements.height = 100;

    // Calculate area and determine if mesh is required
    requirements.area = requirements.width * requirements.height;
    requirements.required = requirements.width > 0 && requirements.height > 0;

    console.log('[MESH-HUNT] Extracted requirements:', requirements);
    return requirements;
}

function displayMeshRequirements(requirements) {
    const requirementsList = document.getElementById('mesh-requirements-list');
    if (!requirementsList) return;

    let html = '';

    if (requirements.required) {
        // Check if this is multi-panel requirements
        if (requirements.panels && requirements.panels.length > 0) {
            html = displayMultiPanelRequirements(requirements);
        } else {
            // Single panel display (legacy)
            html = `
                <div class="alert alert-success mb-2">
                    <strong><i class="fas fa-check-circle"></i> Mesh Required</strong>
                </div>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Width:</strong></td>
                        <td>${requirements.width.toFixed(0)}mm</td>
                    </tr>
                    <tr>
                        <td><strong>Height:</strong></td>
                        <td>${requirements.height.toFixed(0)}mm</td>
                    </tr>
                    <tr>
                        <td><strong>Area:</strong></td>
                        <td>${(requirements.area / 1000000).toFixed(2)}m²</td>
                    </tr>
                    <tr>
                        <td><strong>Series:</strong></td>
                        <td>${requirements.series}</td>
                    </tr>
                </table>
            `;
        }
        
        if (requirements.source_fields.length > 0) {
            html += `
                <div class="mt-2">
                    <small class="text-muted">
                        <strong>Source:</strong> ${requirements.source_fields.join(', ')}
                    </small>
                </div>
            `;
        }
    } else {
        html = `
            <div class="alert alert-warning">
                <strong><i class="fas fa-exclamation-triangle"></i> No Mesh Required</strong>
                <p class="mb-0 mt-2">No valid door dimensions found in configuration.</p>
            </div>
        `;
    }

    requirementsList.innerHTML = html;
}

async function huntMeshComponents() {
    console.log('[MESH-HUNT] Hunting for mesh components');

    const componentsList = document.getElementById('mesh-components-list');
    if (!componentsList) return;

    // Show loading state
    componentsList.innerHTML = `
        <div class="text-muted text-center p-3">
            <i class="fas fa-spinner fa-spin"></i> Creating mesh operations...
        </div>
    `;

    // Get configuration ID
    let configId = getConfigurationId();
    if (!configId) {
        console.log('[MESH-HUNT] No configuration ID found, attempting to auto-save configuration');

        // Try to auto-save the configuration first
        if (window.configuratorComponent && window.configuratorComponent.saveConfiguration) {
            try {
                componentsList.innerHTML = `
                    <div class="text-muted text-center p-3">
                        <i class="fas fa-spinner fa-spin"></i> Saving configuration...
                    </div>
                `;

                await window.configuratorComponent.saveConfiguration();
                configId = getConfigurationId();

                if (!configId) {
                    throw new Error('Configuration ID still not available after save');
                }

                console.log('[MESH-HUNT] Configuration auto-saved successfully, config ID:', configId);

                // Update loading message
                componentsList.innerHTML = `
                    <div class="text-muted text-center p-3">
                        <i class="fas fa-spinner fa-spin"></i> Creating mesh operations...
                    </div>
                `;

            } catch (error) {
                console.error('[MESH-HUNT] Failed to auto-save configuration:', error);
                componentsList.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> Please save the configuration first using the "Save Configuration" button, then try again.
                    </div>
                `;
                return;
            }
        } else {
            componentsList.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> Please save the configuration first using the "Save Configuration" button, then try again.
                </div>
            `;
            return;
        }
    }

    // Call backend to create mesh panel operations
    fetch('/config_matrix/create_mesh_panel_operations', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            jsonrpc: '2.0',
            method: 'call',
            params: {
                config_id: configId
            }
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.result && data.result.success) {
            displayMeshOperationsResults(data.result);
        } else {
            componentsList.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error: ${data.result?.error || 'Unknown error'}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('[MESH-HUNT] Error creating mesh operations:', error);
        componentsList.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> Network error: ${error.message}
            </div>
        `;
    });
}

function displayMeshHuntResults(requirements) {
    const componentsList = document.getElementById('mesh-components-list');
    if (!componentsList) return;

    // This is a mock implementation - in reality, this would call the backend mesh hunting system
    const mockResults = [
        {
            type: 'master',
            product_name: 'Saltwater Master Sheet 1100x620mm',
            dimensions: '1100x620mm',
            efficiency: 85,
            waste: '15%',
            available_qty: 5
        },
        {
            type: 'planned',
            product_name: 'Saltwater Planned Off-cut 900x800mm',
            dimensions: '900x800mm',
            efficiency: 92,
            waste: '8%',
            available_qty: 2
        }
    ];

    let html = `
        <div class="alert alert-success mb-2">
            <strong><i class="fas fa-search"></i> Found ${mockResults.length} suitable options</strong>
        </div>
    `;

    mockResults.forEach((result, index) => {
        const typeIcon = result.type === 'master' ? 'fa-square' : 'fa-cut';
        const typeColor = result.type === 'master' ? 'primary' : 'success';
        
        html += `
            <div class="card mb-2">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas ${typeIcon} text-${typeColor}"></i>
                                ${result.product_name}
                            </h6>
                            <small class="text-muted">${result.dimensions}</small>
                        </div>
                        <span class="badge bg-${typeColor}">${result.type}</span>
                    </div>
                    <div class="mt-2">
                        <div class="row">
                            <div class="col-6">
                                <small><strong>Efficiency:</strong> ${result.efficiency}%</small>
                            </div>
                            <div class="col-6">
                                <small><strong>Available:</strong> ${result.available_qty}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
        <div class="mt-3">
            <button class="btn btn-primary btn-sm" onclick="selectMeshComponent(0)">
                <i class="fas fa-check"></i> Select Best Option
            </button>
        </div>
    `;

    componentsList.innerHTML = html;
}

function selectMeshComponent(index) {
    console.log('[MESH-HUNT] Selected mesh component at index:', index);
    
    // Show success message
    const componentsList = document.getElementById('mesh-components-list');
    if (componentsList) {
        const successHtml = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> 
                <strong>Mesh component selected!</strong>
                <p class="mb-0 mt-2">The optimal mesh component has been assigned to this configuration.</p>
            </div>
        `;
        componentsList.innerHTML = successHtml;
    }
}

// Auto-refresh mesh panel when calculated fields change
function setupAutoRefresh() {
    // Listen for calculated fields updates
    document.addEventListener('calculatedFieldsUpdated', function() {
        console.log('[MESH-HUNT] Calculated fields updated, refreshing mesh panel');
        const meshPanel = document.getElementById('mesh-panel');
        if (meshPanel && meshPanel.style.display !== 'none') {
            loadMeshRequirements();
        }
    });

    // Also refresh when form fields change (with debouncing)
    let refreshTimeout;
    document.addEventListener('input', function(e) {
        if (e.target.form) {
            clearTimeout(refreshTimeout);
            refreshTimeout = setTimeout(() => {
                const meshPanel = document.getElementById('mesh-panel');
                if (meshPanel && meshPanel.style.display !== 'none') {
                    loadMeshRequirements();
                }
            }, 500); // 500ms debounce
        }
    });
}

// Initialize auto-refresh when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setupAutoRefresh();
});

// Make functions available globally
window.meshHuntPanel = {
    initializeMeshPanel,
    loadMeshRequirements,
    huntMeshComponents,
    selectMeshComponent,
    setupAutoRefresh
};
