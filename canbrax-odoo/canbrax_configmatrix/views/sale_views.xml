<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Sale Order Form View Extension -->
    <record id="view_order_form_configmatrix" model="ir.ui.view">
        <field name="name">sale.order.form.configmatrix</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <!-- Add BOM button to button box (next to delivery) -->
            <xpath expr="//div[hasclass('oe_button_box')]" position="inside">
                <button name="action_view_boms" type="object"
                        class="oe_stat_button"
                        icon="fa-list-alt"
                        invisible="state not in ('sale', 'done')"
                        groups="mrp.group_mrp_user">
                    <div class="o_stat_info">
                        <span class="o_stat_text">Bills of Materials</span>
                    </div>
                </button>
                <button name="action_manage_mesh_components" type="object"
                        class="oe_stat_button"
                        icon="fa-th"
                        invisible="state not in ('review', 'sale', 'done')">
                    <div class="o_stat_info">
                        <span class="o_stat_text">Manage Mesh</span>
                    </div>
                </button>
            </xpath>

            <!-- Add controls to form view -->
            <xpath expr="//field[@name='order_line']/form//field[@name='product_id']" position="after">
                <field name="is_configurable" invisible="1"/>
                <field name="is_configured" invisible="1"/>
                <button name="action_configure_product" type="object" string="Configure"
                        class="btn-sm btn-primary" icon="fa-cogs"
                        invisible="[('is_configurable', '=', False)]"/>
                <button name="action_view_configuration" type="object" string="View Config"
                        class="btn-sm btn-secondary" icon="fa-eye"
                        invisible="[('is_configured', '=', False)]"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/form//field[@name='price_unit']" position="before">
                <field name="config_id" invisible="[('is_configurable', '=', False)]" readonly="1"
                       options="{'no_create': True}"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/form//field[@name='name']" position="after">
                <field name="configuration_summary" invisible="[('is_configured', '=', False)]" readonly="1"/>
            </xpath>

            <!-- Add buttons to list view using the same approach as canbrax_cpq -->
            <xpath expr="//field[@name='order_line']/list" position="inside">
                <field name="is_configurable" invisible="1"/>
                <field name="is_configured" invisible="1"/>
                <field name="config_id" invisible="1"/>
                <button name="action_configure_product"
                        string="Configure"
                        type="object"
                        icon="fa-cogs"
                        class="btn-secondary btn-sm"
                        invisible="is_configurable == False"/>
                <button name="action_view_configuration"
                        string="View"
                        type="object"
                        icon="fa-eye"
                        class="btn-secondary btn-sm"
                        invisible="is_configured == False"/>
            </xpath>
        </field>
    </record>

    <!-- Remove the separate order line tree view that's causing issues -->
</odoo>
