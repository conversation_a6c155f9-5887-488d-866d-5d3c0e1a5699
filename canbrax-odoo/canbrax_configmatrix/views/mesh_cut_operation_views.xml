<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Mesh Cut Operation Views -->
    
    <!-- Tree View -->
    <record id="view_mesh_cut_operation_tree" model="ir.ui.view">
        <field name="name">mesh.cut.operation.tree</field>
        <field name="model">mesh.cut.operation</field>
        <field name="arch" type="xml">
            <list string="Mesh Cut Operations" decoration-info="state == 'draft'"
                  decoration-warning="state == 'confirmed'" decoration-success="state == 'done'"
                  decoration-muted="not source_product_id">
                <field name="name"/>
                <field name="required_width" string="Req. Width"/>
                <field name="required_height" string="Req. Height"/>
                <field name="mesh_series" string="Series"/>
                <field name="source_type" string="Source Type"/>
                <field name="source_product_id" string="Assigned Mesh"/>
                <field name="source_lot_id" string="Lot/Serial"
                       invisible="source_type != 'unplanned'"/>
                <field name="actual_source_width" string="Source Width"/>
                <field name="actual_source_height" string="Source Height"/>
                <field name="efficiency" widget="percentage" string="Efficiency"/>
                <field name="waste_width" string="Waste W"/>
                <field name="waste_height" string="Waste H"/>
                <field name="state" widget="badge"
                       decoration-info="state == 'draft'"
                       decoration-warning="state in ('confirmed', 'cutting')"
                       decoration-success="state == 'done'"
                       decoration-danger="state == 'cancelled'"/>
            </list>
        </field>
    </record>
    
    <!-- Form View -->
    <record id="view_mesh_cut_operation_form" model="ir.ui.view">
        <field name="name">mesh.cut.operation.form</field>
        <field name="model">mesh.cut.operation</field>
        <field name="arch" type="xml">
            <form string="Mesh Cut Operation">
                <header>
                    <button name="action_find_mesh" type="object" string="Find Mesh"
                            class="btn-primary" invisible="state not in ['draft', 'auto_selected', 'confirmed']"/>
                    <button name="action_confirm" type="object" string="Confirm"
                            class="btn-primary" invisible="state not in ['draft', 'auto_selected'] or not source_product_id"/>
                    <button name="action_start_cutting" type="object" string="Start Cutting"
                            class="btn-primary" invisible="state != 'confirmed'"/>
                    <button name="action_complete_cutting" type="object" string="Complete"
                            class="btn-success" invisible="state != 'cutting'"/>
                    <button name="action_cancel" type="object" string="Cancel" 
                            invisible="state in ('done', 'cancelled')"/>
                    <field name="state" widget="statusbar" 
                           statusbar_visible="draft,confirmed,cutting,done"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <!-- Source product info shown in fields below -->
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="display_name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="Requirements">
                            <field name="name"/>
                            <field name="sale_order_id"/>
                            <field name="config_id"/>
                            <field name="required_width"/>
                            <field name="required_height"/>
                            <field name="required_qty"/>
                            <field name="mesh_series"/>
                        </group>
                        <group string="Source Details">
                            <field name="source_type" readonly="1"/>
                            <field name="source_product_id" readonly="1"/>
                            <field name="source_lot_id" readonly="1"
                                   invisible="source_type != 'unplanned'"/>
                            <field name="actual_width" readonly="1"
                                   invisible="not source_product_id"/>
                            <field name="actual_height" readonly="1"
                                   invisible="not source_product_id"/>
                            <field name="efficiency" readonly="1" widget="percentage"
                                   invisible="not source_product_id"/>
                            <field name="selected_option_id" readonly="1"
                                   invisible="not selected_option_id"
                                   string="Selected Option"/>
                        </group>
                    </group>
                    
                    <group string="Waste Analysis" invisible="not source_product_id">
                        <group>
                            <field name="waste_width" readonly="1"/>
                            <field name="waste_height" readonly="1"/>
                        </group>
                        <group>
                            <!-- Placeholder for additional waste analysis fields -->
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Available Options" name="options_analysis">

                            <field name="available_options_ids" mode="list">
                                <list string="Available Options" create="false" edit="false" delete="false"
                                      decoration-success="is_selected">
                                    <field name="option_type" string="Type"/>
                                    <field name="product_id" string="Product"/>
                                    <field name="lot_id" string="Lot/Serial"/>
                                    <field name="available_width" string="Width (mm)"/>
                                    <field name="available_height" string="Height (mm)"/>
                                    <field name="efficiency_percentage" string="Efficiency %"
                                           decoration-success="efficiency_percentage >= 80"
                                           decoration-warning="efficiency_percentage >= 50 and efficiency_percentage &lt; 80"
                                           decoration-danger="efficiency_percentage &lt; 50"/>
                                    <field name="waste_width" string="Waste W (mm)"/>
                                    <field name="waste_height" string="Waste H (mm)"/>
                                    <field name="available_qty" string="Stock Qty"/>
                                    <field name="byproduct_count" string="Byproducts"/>
                                    <field name="selection_status" string="Status"/>
                                    <field name="is_selected" invisible="1"/>
                                    <button name="action_select_this_option" type="object"
                                            string="Select" class="btn-primary"
                                            invisible="is_selected"
                                            confirm="Are you sure you want to select this option? This will override the automatic selection."/>
                                </list>
                            </field>
                        </page>

                        <page string="Byproducts" name="byproducts">
                            <field name="byproduct_ids" mode="list">
                                <list string="Byproducts" editable="bottom">
                                    <field name="name"/>
                                    <field name="width"/>
                                    <field name="height"/>
                                    <field name="quantity"/>
                                    <field name="area"/>
                                    <field name="total_area"/>
                                    <field name="product_id"/>
                                </list>
                            </field>
                        </page>

                        <page string="Cut Operations" name="cut_operations"
                              invisible="not selected_option_id or not matrix_cell_height">
                            <group>
                                <group string="Matrix Cell Information">
                                    <field name="assigned_matrix_id" string="Assigned Matrix:" readonly="1"
                                           widget="many2one" options="{'no_create': True}"/>
                                    <field name="selected_option_display" string="Selected Option:" readonly="1"/>
                                    <field name="matrix_cell_height" string="Cell Height (mm):" readonly="1"/>
                                    <field name="matrix_cell_width" string="Cell Width (mm):" readonly="1"/>
                                    <field name="matrix_arrow_path" string="Arrow Path:" readonly="1"/>
                                </group>

                                <group string="Cut Plan Details">
                                    <field name="selected_cut_plan_id" string="Cut Plan:" readonly="1"
                                           widget="many2one" options="{'no_create': True}"/>
                                    <field name="source_product_id" string="Master Sheet:" readonly="1"/>
                                    <field name="selected_byproduct_count" string="Byproducts:" readonly="1"/>
                                    <div class="mt-2" invisible="not selected_cut_plan_id">
                                        <button name="action_view_cutting_diagram" type="object"
                                                string="View Cutting Layout Diagram" class="btn btn-primary btn-sm"
                                                icon="fa-image" title="Open the cutting layout diagram"/>
                                    </div>
                                </group>
                            </group>
                        </page>

                        <page string="Cutting Diagram" name="cutting_diagram"
                              invisible="not selected_cut_plan_id">
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h5 class="mb-0">
                                                <i class="fa fa-image mr-2"></i>
                                                Cutting Layout Diagram
                                                <span class="badge badge-info ml-2" invisible="not selected_cut_plan_id">
                                                    <field name="selected_cut_plan_id" readonly="1" nolabel="1"/>
                                                </span>
                                            </h5>
                                        </div>
                                        <div class="card-body text-center" style="min-height: 600px; padding: 20px;">
                                            <div style="width: 98%; height: 550px; margin: 0 auto; border: 1px solid #ddd; overflow: auto; display: flex; align-items: center; justify-content: center;">
                                                <field name="cut_diagram_display" nolabel="1"
                                                       style="width: 90%; height: 90%; object-fit: contain;"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </page>

                        <page string="Stock Moves" name="stock_moves"
                              invisible="state not in ('cutting', 'done')">
                            <group>
                                <field name="consumption_move_id" readonly="1"/>
                            </group>
                            <field name="production_move_ids" mode="list" readonly="1">
                                <list string="Production Moves">
                                    <field name="name"/>
                                    <field name="product_id"/>
                                    <field name="product_uom_qty"/>
                                    <field name="state"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Search View -->
    <record id="view_mesh_cut_operation_search" model="ir.ui.view">
        <field name="name">mesh.cut.operation.search</field>
        <field name="model">mesh.cut.operation</field>
        <field name="arch" type="xml">
            <search string="Search Mesh Cut Operations">
                <field name="name"/>
                <field name="sale_order_id"/>
                <field name="source_product_id"/>
                <field name="mesh_series"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Confirmed" name="confirmed" domain="[('state', '=', 'confirmed')]"/>
                <filter string="Cutting" name="cutting" domain="[('state', '=', 'cutting')]"/>
                <filter string="Done" name="done" domain="[('state', '=', 'done')]"/>
                <separator/>
                <filter string="Unplanned Source" name="unplanned" domain="[('source_type', '=', 'unplanned')]"/>
                <filter string="Planned Source" name="planned" domain="[('source_type', '=', 'planned')]"/>
                <filter string="Master Source" name="master" domain="[('source_type', '=', 'master')]"/>
                <separator/>
                <filter string="Saltwater Series" name="saltwater" domain="[('mesh_series', '=', 'saltwater')]"/>
                <filter string="Diamond Grill" name="diamond" domain="[('mesh_series', '=', 'diamond')]"/>
                <filter string="Fly Screen" name="flyscreen" domain="[('mesh_series', '=', 'flyscreen')]"/>
                <group expand="0" string="Group By">
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Mesh Series" name="group_mesh_series" context="{'group_by': 'mesh_series'}"/>
                    <filter string="Source Type" name="group_source_type" context="{'group_by': 'source_type'}"/>
                    <filter string="Sale Order" name="group_sale_order" context="{'group_by': 'sale_order_id'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- Action -->
    <record id="action_mesh_cut_operation" model="ir.actions.act_window">
        <field name="name">Mesh Cut Operations</field>
        <field name="res_model">mesh.cut.operation</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_mesh_cut_operation_search"/>
        <field name="context">{'search_default_draft': 1, 'search_default_confirmed': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first mesh cutting operation!
            </p>
            <p>
                Mesh cutting operations track the process of cutting mesh materials
                from master sheets or off-cuts to create specific sizes for orders.
            </p>
        </field>
    </record>

    <!-- Mesh Assignment Summary View -->
    <record id="view_mesh_cut_operation_summary_tree" model="ir.ui.view">
        <field name="name">mesh.cut.operation.summary.tree</field>
        <field name="model">mesh.cut.operation</field>
        <field name="arch" type="xml">
            <list string="Mesh Assignment Summary" create="false" edit="false"
                  decoration-success="source_product_id and efficiency >= 0.8"
                  decoration-warning="source_product_id and efficiency >= 0.5 and efficiency &lt; 0.8"
                  decoration-danger="source_product_id and efficiency &lt; 0.5"
                  decoration-muted="not source_product_id">
                <field name="name" string="Operation"/>
                <field name="required_width" string="Required W"/>
                <field name="required_height" string="Required H"/>
                <field name="mesh_series" string="Series"/>
                <field name="source_type" string="Source Type"
                       decoration-info="source_type == 'unplanned'"
                       decoration-warning="source_type == 'planned'"
                       decoration-success="source_type == 'master'"/>
                <field name="source_product_id" string="Assigned Mesh"/>
                <field name="actual_source_width" string="Source W"/>
                <field name="actual_source_height" string="Source H"/>
                <field name="efficiency" widget="percentage" string="Efficiency"
                       decoration-success="efficiency >= 0.8"
                       decoration-warning="efficiency >= 0.5 and efficiency &lt; 0.8"
                       decoration-danger="efficiency &lt; 0.5"/>
                <field name="waste_width" string="Waste W"/>
                <field name="waste_height" string="Waste H"/>
                <field name="state" widget="badge"/>
            </list>
        </field>
    </record>

    <!-- Action for Mesh Assignment Summary -->
    <record id="action_mesh_cut_operation_summary" model="ir.actions.act_window">
        <field name="name">Mesh Assignment Summary</field>
        <field name="res_model">mesh.cut.operation</field>
        <field name="view_mode">list</field>
        <field name="view_id" ref="view_mesh_cut_operation_summary_tree"/>
        <field name="domain">[('source_product_id', '!=', False)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No mesh assignments found!
            </p>
            <p>
                Create cut operations and assign mesh to see the summary here.
            </p>
        </field>
    </record>

    <!-- Mesh Cut Byproduct Line Views -->
    
    <!-- Tree View -->
    <record id="view_mesh_cut_byproduct_line_tree" model="ir.ui.view">
        <field name="name">mesh.cut.byproduct.line.tree</field>
        <field name="model">mesh.cut.byproduct.line</field>
        <field name="arch" type="xml">
            <list string="Mesh Cut Byproduct Lines">
                <field name="name"/>
                <field name="cut_operation_id"/>
                <field name="width"/>
                <field name="height"/>
                <field name="quantity"/>
                <field name="area"/>
                <field name="total_area"/>
                <field name="product_id"/>
                <field name="lot_id"/>
            </list>
        </field>
    </record>
    
    <!-- Form View -->
    <record id="view_mesh_cut_byproduct_line_form" model="ir.ui.view">
        <field name="name">mesh.cut.byproduct.line.form</field>
        <field name="model">mesh.cut.byproduct.line</field>
        <field name="arch" type="xml">
            <form string="Mesh Cut Byproduct Line">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="cut_operation_id"/>
                            <field name="product_id"/>
                            <field name="lot_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="width"/>
                            <field name="height"/>
                            <field name="quantity"/>
                        </group>
                    </group>
                    
                    <group>
                        <group>
                            <field name="area" readonly="1"/>
                            <field name="total_area" readonly="1"/>
                        </group>
                        <group>
                            <field name="stock_move_id" readonly="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <!-- Action -->
    <record id="action_mesh_cut_byproduct_line" model="ir.actions.act_window">
        <field name="name">Mesh Cut Byproduct Lines</field>
        <field name="res_model">mesh.cut.byproduct.line</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No byproduct lines yet!
            </p>
            <p>
                Byproduct lines track the actual off-cuts created during cutting operations.
            </p>
        </field>
    </record>
    
</odoo>
