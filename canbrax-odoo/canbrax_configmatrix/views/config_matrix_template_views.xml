<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Template Tree View -->
    <record id="view_config_matrix_template_tree" model="ir.ui.view">
        <field name="name">config.matrix.template.tree</field>
        <field name="model">config.matrix.template</field>
        <field name="arch" type="xml">
            <list string="Configuration Templates">
                <field name="name"/>
                <field name="product_template_id"/>
                <field name="code"/>
                <field name="version"/>
                <field name="state"/>
                <field name="configuration_count"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Template Form View -->
    <record id="view_config_matrix_template_form" model="ir.ui.view">
        <field name="name">config.matrix.template.form</field>
        <field name="model">config.matrix.template</field>
        <field name="arch" type="xml">
            <form string="Configuration Template">
                <header>
                    <button name="action_set_to_draft" string="Set to Draft" type="object"
                            invisible="state == 'draft'" class="btn-secondary"/>
                    <button name="action_set_to_testing" string="Set to Testing" type="object"
                            invisible="state == 'testing'" class="btn-secondary"/>
                    <button name="action_set_to_active" string="Activate" type="object"
                            invisible="state == 'active'" class="btn-primary"/>
                    <button name="action_export_simple" string="Export" type="object"
                            class="btn-secondary" icon="fa-download"/>
                    <button name="action_open_import_wizard" type="object" class="oe_stat_button" string="Replace Technical Names" icon="fa-refresh"/>

                    <field name="state" widget="statusbar" statusbar_visible="draft,testing,active"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_configurations" type="object" class="oe_stat_button" icon="fa-list">
                            <field name="configuration_count" widget="statinfo" string="Configurations"/>
                        </button>
                        <button name="action_convert_all_visibility_conditions"
                                string="Update Visibility Conditions"
                                type="object"
                                class="oe_stat_button" icon="fa-refresh"/>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Template Name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="product_template_id" options="{'no_create': True}"/>
                            <field name="code"/>
                            <field name="version"/>
                        </group>
                        <group>
                            <field name="active"/>
                            <field name="admin_notes"/>

                        </group>
                    </group>
                    <group string="Use Cases">
                        <field name="enable_check_measure" string="Check Measure"/>
                        <field name="enable_sales" string="Sales/Quoting"/>
                        <field name="enable_online" string="Online Sales"/>
                    </group>
                    <notebook>
                        <page string="Sections" name="sections">
                            <field name="section_ids" mode="list" context="{'default_matrix_id': id}">
                                <list string="Sections" editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="collapsible"/>
                                    <field name="collapsed_by_default"
                                           help="If checked, this section will be collapsed when the configurator is first loaded"/>
                                    <field name="show_header"
                                           help="If unchecked, the section name header will be hidden in the configurator"/>
                                    <field name="field_count"/>
                                    <button name="action_view_fields" type="object" string="View Fields"
                                            class="btn-sm btn-secondary"/>
                                    <button name="action_add_field" type="object" string="Add Field"
                                            class="btn-sm btn-primary"/>
                                </list>
                            </field>
                        </page>
                        <page string="Base Components" name="components">
                            <field name="bom_product_ids">
                                <list string="Base Components">
                                    <field name="default_code"/>
                                    <field name="name"/>
                                    <field name="uom_id"/>
                                    <field name="list_price"/>
                                </list>
                            </field>
                        </page>
                        <page string="Component Mappings" name="mappings">
                            <field name="option_component_mapping_ids" readonly="1">
                                <list string="Option Component Mappings">
                                    <field name="section_name"/>
                                    <field name="field_name"/>
                                    <field name="option_name"/>
                                    <field name="component_product_id"/>
                                    <field name="quantity_formula"/>
                                    <button name="action_view_option" type="object" string="View Option" class="btn-sm btn-secondary"/>
                                </list>
                            </field>
                            <div class="mt-3 mb-3">
                                <p class="text-muted">To add or modify component products, go to the respective field's options and set the component product there.</p>
                            </div>
                        </page>
                        <page string="Product Visualization" name="visualization">
                            <group>
                                <field name="has_svg_preview" invisible="1"/>
                                <div class="alert alert-info" role="alert" invisible="has_svg_preview">
                                    <p><strong>No Base SVG Found</strong></p>
                                    <p>You need to create at least one Base SVG component for the product visualization to work.</p>
                                </div>
                            </group>
                            <field name="svg_component_ids">
                                <list string="SVG Components" default_order="z_index">
                                    <field name="name"/>
                                    <field name="template_id" invisible="1"/>
                                    <field name="component_type" decoration-info="component_type == 'base'" decoration-warning="component_type == 'layer'"/>
                                    <field name="z_index" decoration-danger="z_index &lt; 10" decoration-warning="z_index &gt;= 10 and z_index &lt; 50" decoration-info="z_index &gt;= 50"/>
                                    <field name="condition"/>
                                    <button name="action_preview_svg" type="object" icon="fa-eye" title="Preview"/>
                                </list>
                            </field>
                        </page>


                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Template Search View -->
    <record id="view_config_matrix_template_search" model="ir.ui.view">
        <field name="name">config.matrix.template.search</field>
        <field name="model">config.matrix.template</field>
        <field name="arch" type="xml">
            <search string="Search Templates">
                <field name="name"/>
                <field name="code"/>
                <field name="product_template_id"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Testing" name="testing" domain="[('state', '=', 'testing')]"/>
                <filter string="Active" name="active_state" domain="[('state', '=', 'active')]"/>
                <filter string="Archived" name="archived" domain="[('state', '=', 'archived')]"/>
                <group expand="0" string="Group By">
                    <filter string="Product" name="group_by_product" context="{'group_by': 'product_template_id'}"/>
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Template Action -->
    <record id="action_config_matrix_template" model="ir.actions.act_window">
        <field name="name">Configuration Templates</field>
        <field name="res_model">config.matrix.template</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first configuration template!
            </p>
            <p>
                Configuration templates define the structure of questions for configurable products.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_config_matrix_root" name="ConfigMatrix" sequence="50"/>
    <menuitem id="menu_config_matrix_templates" name="Templates" parent="menu_config_matrix_root" action="action_config_matrix_template" sequence="10"/>
</odoo>
