<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Product Template Form View Extension -->
    <record id="view_product_template_form_configmatrix" model="ir.ui.view">
        <field name="name">product.template.form.configmatrix</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='options']" position="inside">
                <div>
                    <field name="is_configurable"/>
                    <label for="is_configurable"/>
                </div>
                <div>
                    <field name="is_mesh_product"/>
                    <label for="is_mesh_product"/>
                </div>
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="Configuration" name="configuration" invisible="not is_configurable">
                    <group>
                        <field name="matrix_id" options="{'no_create': True}"/>
                        <field name="configuration_count" invisible="1"/>
                    </group>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_configurations" type="object" class="oe_stat_button" icon="fa-list"
                                invisible="not matrix_id">
                            <field name="configuration_count" widget="statinfo" string="Configurations"/>
                        </button>
                        <button name="action_create_matrix" type="object" class="btn btn-primary"
                                invisible="matrix_id">
                            Create Configuration Matrix
                        </button>
                    </div>
                </page>
                <page string="Mesh Properties" name="mesh_properties" invisible="not is_mesh_product">
                    <group>
                        <group string="Mesh Details">
                            <field name="mesh_type"/>
                            <field name="cut_matrix_id" options="{'no_create': True}"/>
                            <field name="mesh_series" string="Mesh Series"/>
                            <field name="mesh_series_computed" invisible="1"/>
                            <field name="mesh_width"/>
                            <field name="mesh_height"/>
                        </group>
                        <group string="Tracking">
                            <field name="tracking"
                                   help="Unplanned off-cuts use lot/serial tracking to store dimensions. Master sheets and planned off-cuts use standard quantity tracking."/>
                        </group>
                    </group>

                    <group string="Product Code Information" invisible="not is_mesh_product">
                        <div class="alert alert-info" role="alert">
                            <h5>Automatic Code Generation</h5>
                            <p>Product codes are automatically generated based on mesh type and dimensions:</p>
                            <ul>
                                <li><strong>Master Sheets:</strong> [Series]MMS[Width][Height] (e.g., SWMMS1100620)</li>
                                <li><strong>Planned Offcuts:</strong> [Series][Width]X[Height]PO (e.g., BX900X1200PO)</li>
                                <li><strong>Unplanned Offcuts:</strong> [Series][Width]X[Height]UO (e.g., SW500X300UO)</li>
                            </ul>
                            <p><field name="default_code" readonly="1" class="fw-bold"/></p>
                        </div>
                    </group>

                    <div class="oe_button_box" name="mesh_button_box">
                        <button name="action_view_mesh_operations" type="object"
                                class="oe_stat_button" icon="fa-scissors"
                                invisible="mesh_type != 'master'">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Cut</span>
                                <span class="o_stat_text">Plans</span>
                            </div>
                        </button>
                        <button name="action_regenerate_mesh_code" type="object"
                                class="oe_stat_button" icon="fa-refresh">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Regenerate</span>
                                <span class="o_stat_text">Code</span>
                            </div>
                        </button>
                        <button name="action_view_mesh_stock_by_dimensions" type="object"
                                class="oe_stat_button" icon="fa-cubes">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Stock by</span>
                                <span class="o_stat_text">Dimensions</span>
                            </div>
                        </button>
                    </div>

                </page>
            </xpath>
        </field>
    </record>

    <!-- Product Template Tree View Extension -->
    <record id="view_product_template_tree_configmatrix" model="ir.ui.view">
        <field name="name">product.template.tree.configmatrix</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view"/>
        <field name="arch" type="xml">
            <field name="default_code" position="after">
                <field name="is_configurable" optional="show"/>
                <field name="is_mesh_product" optional="show"/>
                <field name="mesh_type" optional="show"/>
                <field name="mesh_series_computed" string="Mesh Series" optional="show"/>
                <field name="mesh_width" optional="show"/>
                <field name="mesh_height" optional="show"/>
            </field>
        </field>
    </record>

    <!-- Mesh Products Specific Tree View -->
    <record id="view_product_template_tree_mesh" model="ir.ui.view">
        <field name="name">product.template.tree.mesh</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="view_product_template_tree_configmatrix"/>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <!-- Hide configmatrix fields that aren't relevant for mesh products -->
            <field name="is_configurable" position="attributes">
                <attribute name="optional">hide</attribute>
            </field>
            <field name="is_mesh_product" position="attributes">
                <attribute name="optional">hide</attribute>
            </field>
            <!-- Ensure mesh fields are visible -->
            <field name="mesh_type" position="attributes">
                <attribute name="optional">show</attribute>
            </field>
            <field name="mesh_series_computed" position="attributes">
                <attribute name="optional">show</attribute>
            </field>
            <field name="mesh_width" position="attributes">
                <attribute name="optional">show</attribute>
            </field>
            <field name="mesh_height" position="attributes">
                <attribute name="optional">show</attribute>
            </field>
        </field>
    </record>

    <!-- Product Template Search View Extension -->
    <record id="view_product_template_search_configmatrix" model="ir.ui.view">
        <field name="name">product.template.search.configmatrix</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_search_view"/>
        <field name="arch" type="xml">
            <filter name="filter_to_sell" position="after">
                <filter string="Configurable" name="configurable" domain="[('is_configurable', '=', True)]"/>
                <separator/>
                <filter string="Mesh Products" name="mesh_products" domain="[('is_mesh_product', '=', True)]"/>
                <filter string="Master Sheets" name="master_sheets" domain="[('mesh_type', '=', 'master')]"/>
                <filter string="Planned Off-cuts" name="planned_offcuts" domain="[('mesh_type', '=', 'planned')]"/>
                <filter string="Unplanned Off-cuts" name="unplanned_offcuts" domain="[('mesh_type', '=', 'unplanned')]"/>
            </filter>
            <field name="categ_id" position="after">
                <field name="mesh_series"/>
                <field name="mesh_type"/>
                <field name="cut_matrix_id"/>
            </field>

        </field>
    </record>
</odoo>
