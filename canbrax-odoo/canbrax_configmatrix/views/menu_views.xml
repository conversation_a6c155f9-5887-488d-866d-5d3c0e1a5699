<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Configurable Products Action -->
    <record id="action_config_matrix_configurable_products" model="ir.actions.act_window">
        <field name="name">Configurable Products</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('is_configurable', '=', True)]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No configurable products found
            </p>
            <p>
                Create products and enable the "Configurable Product" checkbox to see them here.
            </p>
        </field>
    </record>
    <!-- Main Menu -->
    <menuitem id="menu_config_matrix_root"
              name="Matrix"
              web_icon="canbrax_configmatrix,static/src/img/matrix.png"
              sequence="50"/>

    <!-- Configuration Menu -->
    <menuitem id="menu_config_matrix_configuration"
              name="Configuration"
              parent="menu_config_matrix_root"
              sequence="10"/>

    <!-- Templates Menu -->
    <menuitem id="menu_config_matrix_templates"
              name="Templates"
              parent="menu_config_matrix_configuration"
              action="action_config_matrix_template"
              sequence="10"/>

    <!-- Operation Templates Menu (moved to BOMs section) -->
    <menuitem id="menu_config_matrix_operation_templates"
              name="Operation Templates"
              parent="menu_config_matrix_configurations_root"
              action="action_config_matrix_operation_template"
              sequence="5"/>

    <!-- Fixed Price Table Action -->
    <record id="action_config_matrix_operation_price" model="ir.actions.act_window">
        <field name="name">Fixed Price Table</field>
        <field name="res_model">config.matrix.operation.price</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No operation prices found
            </p>
            <p>
                Create fixed price entries for operations to standardize pricing across your configuration templates.
                These prices will be used to calculate the total cost of operations in product configurations.
            </p>
        </field>
    </record>

    <!-- Fixed Price Table Menu -->
    <menuitem id="menu_config_matrix_operation_price"
              name="Fixed Price Table"
              parent="menu_config_matrix_configurations_root"
              action="action_config_matrix_operation_price"
              sequence="6"/>

    <!-- Configurable Products Menu -->
    <menuitem id="menu_config_matrix_configurable_products"
              name="Products"
              parent="menu_config_matrix_configuration"
              action="action_config_matrix_configurable_products"
              sequence="20"/>

    <!-- BOMs Menu -->
    <menuitem id="menu_config_matrix_configurations_root"
              name="BOMs"
              parent="menu_config_matrix_root"
              sequence="20"/>

    <!-- Draft BOMs Menu -->
    <menuitem id="menu_config_matrix_configurations"
              name="Draft BOMs"
              parent="menu_config_matrix_configurations_root"
              action="action_config_matrix_configuration"
              sequence="10"/>

    <!-- Tools Menu -->
    <menuitem id="menu_config_matrix_tools"
              name="Tools"
              parent="menu_config_matrix_root"
              sequence="30"/>

    <!-- Pricing Menu (renamed from "Pricing Matrices") -->
    <menuitem id="menu_config_matrix_pricing"
              name="Pricing"
              parent="menu_config_matrix_root"
              sequence="25"/>

    <!-- Pricing Matrices Menu (renamed from "Price Matrices") -->
    <menuitem id="menu_config_matrix_price_matrices"
              name="Pricing Matrices"
              parent="menu_config_matrix_pricing"
              action="action_config_matrix_price_matrix"
              sequence="10"/>

    <!-- Labour Matrices Menu (renamed from "Labor Time Matrices") -->
    <menuitem id="menu_config_matrix_labor_time_matrices"
              name="Labour Matrices"
              parent="menu_config_matrix_pricing"
              action="action_config_matrix_labor_time_matrix"
              sequence="20"/>

    <!-- Matrix Categories Menu (moved to last position) -->
    <menuitem id="menu_config_matrix_categories"
              name="Matrix Categories"
              parent="menu_config_matrix_pricing"
              action="action_config_matrix_category"
              sequence="30"/>

    <!-- Consolidated Import/Export Tools -->
    <menuitem id="menu_config_matrix_import_export_tools"
              name="Manage Pricing"
              parent="menu_config_matrix_tools"
              action="action_config_matrix_import_export_tools"
              sequence="10"/>

    <!-- JSON Import Template -->
    <menuitem id="menu_config_matrix_json_import"
              name="Import Template"
              parent="menu_config_matrix_tools"
              action="action_config_matrix_json_import"
              sequence="20"/>

    <!-- Operation Migration Wizard Action -->
    <record id="action_config_matrix_operation_migration_wizard" model="ir.actions.act_window">
        <field name="name">Migrate Operation Templates</field>
        <field name="res_model">config.matrix.operation.migration.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Migrate Operation Templates
            </p>
            <p>
                This wizard helps migrate operation templates to separate duration and cost calculations.
            </p>
        </field>
    </record>

    <!-- Operation Migration Wizard Menu -->
    <menuitem id="menu_config_matrix_operation_migration"
              name="Migrate Operations"
              parent="menu_config_matrix_tools"
              action="action_config_matrix_operation_migration_wizard"
              sequence="30"/>

    <!-- Simple Test Menu - Temporarily disabled -->
    <!-- <menuitem id="menu_config_matrix_simple_test"
              name="Simple Matrix Test"
              parent="menu_config_matrix_import_tools"
              action="action_config_matrix_simple_test_wizard"
              sequence="35"/> -->

</odoo>
