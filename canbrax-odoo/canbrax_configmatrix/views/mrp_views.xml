<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- MRP BOM Form View Extension -->
    <record id="mrp_bom_form_view_configmatrix" model="ir.ui.view">
        <field name="name">mrp.bom.form.configmatrix</field>
        <field name="model">mrp.bom</field>
        <field name="inherit_id" ref="mrp.mrp_bom_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='code']" position="after">
                <field name="is_configured" invisible="1"/>
                <field name="config_id" readonly="1" invisible="[('is_configured', '=', False)]"/>
                <button name="action_view_configuration" type="object" string="View Configuration"
                        class="btn-sm btn-secondary" icon="fa-eye"
                        invisible="[('is_configured', '=', False)]"/>
            </xpath>


            <xpath expr="//notebook" position="inside">
                <page string="Configuration" name="configuration" invisible="[('is_configured', '=', False)]">
                    <field name="configuration_summary" readonly="1"/>
                </page>
            </xpath>
        </field>
    </record>

    <!-- MRP Production Form View Extension -->
    <record id="mrp_production_form_view_configmatrix" model="ir.ui.view">
        <field name="name">mrp.production.form.configmatrix</field>
        <field name="model">mrp.production</field>
        <field name="inherit_id" ref="mrp.mrp_production_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='bom_id']" position="after">
                <field name="is_configured" invisible="1"/>
                <field name="config_id" readonly="1" invisible="[('is_configured', '=', False)]"/>
                <button name="action_view_configuration" type="object" string="View Configuration"
                        class="btn-sm btn-secondary" icon="fa-eye"
                        invisible="[('is_configured', '=', False)]"/>
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="Configuration" name="configuration" invisible="[('is_configured', '=', False)]">
                    <field name="configuration_summary" readonly="1"/>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
