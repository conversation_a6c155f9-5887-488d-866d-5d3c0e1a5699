<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Consolidated Import/Export Tools Form View -->
    <record id="view_config_matrix_import_export_tools_form" model="ir.ui.view">
        <field name="name">config.matrix.import.export.tools.form</field>
        <field name="model">config.matrix.import.export.tools</field>
        <field name="arch" type="xml">
            <form string="Matrix Import/Export Tools">
                <sheet>
                    <div class="oe_title">
                        <h1>Matrix Import/Export Tools</h1>
                        <p class="text-muted">Comprehensive tools for importing and exporting matrix data</p>
                    </div>
                    
                    <notebook>
                        <!-- Excel Import Tab -->
                        <page string="Excel Import" name="excel_import">
                            <group>
                                <div class="alert alert-info" role="alert">
                                    <h4><i class="fa fa-file-excel-o"/> Excel Matrix Import</h4>
                                    <p>Import multiple matrices from Excel workbooks. Automatically detects sheets and creates corresponding matrices.</p>
                                </div>
                            </group>
                            
                            <group>
                                <group string="Import Settings">
                                    <field name="excel_import_product_template_id" options="{'no_create': True}"/>
                                    <field name="excel_import_mode" widget="radio"/>
                                </group>
                                <group string="File Upload">
                                    <field name="excel_import_file_data" filename="excel_import_file_name"/>
                                    <field name="excel_import_file_name" invisible="1"/>
                                </group>
                            </group>
                            
                            <group string="Category Settings">
                                <group>
                                    <field name="excel_target_category_id"/>
                                    <field name="excel_auto_create_categories"/>
                                </group>
                                <group string="Processing Options">
                                    <field name="excel_skip_empty_cells"/>
                                    <field name="excel_validate_data"/>
                                    <field name="excel_create_backup"/>
                                </group>
                            </group>
                            
                            <group string="Sheet Analysis" invisible="not excel_sheet_info">
                                <field name="excel_sheet_info" widget="text" nolabel="1"/>
                            </group>
                            
                            <div class="oe_button_box">
                                <button string="Analyze Excel File" name="action_excel_analyze" type="object" class="btn-secondary"/>
                                <button string="Import All Sheets" name="action_excel_import" type="object" class="btn-primary"/>
                            </div>
                        </page>
                        
                        <!-- Excel Export Tab -->
                        <page string="Excel Export" name="excel_export">
                            <group>
                                <div class="alert alert-success" role="alert">
                                    <h4><i class="fa fa-download"/> Excel Matrix Export</h4>
                                    <p>Export matrices to Excel format. Choose from templates, categories, or all matrices with customizable formatting options.</p>
                                </div>
                            </group>
                            
                            <group>
                                <group string="Export Scope">
                                    <field name="excel_export_type" widget="radio"/>
                                    <field name="excel_export_template_id" invisible="excel_export_type != 'template'"/>
                                    <field name="excel_export_category_id" invisible="excel_export_type != 'category'"/>
                                </group>
                                <group string="Format Options">
                                    <field name="excel_include_metadata"/>
                                    <field name="excel_include_empty_cells"/>
                                    <field name="excel_separate_sheets"/>
                                </group>
                            </group>
                            
                            <group string="Recent Downloads" invisible="not recent_exports">
                                <field name="recent_exports" widget="text" nolabel="1" readonly="1"/>
                            </group>
                            
                            <group string="Download" invisible="not excel_export_file_ready">
                                <field name="excel_export_file_data" filename="excel_export_file_name" readonly="1"/>
                                <field name="excel_export_file_name" readonly="1"/>
                                <field name="excel_export_file_ready" invisible="1"/>
                            </group>
                            
                            <div class="oe_button_box">
                                <button string="Generate Excel Export" name="action_excel_export" type="object" class="btn-primary"/>
                            </div>
                        </page>

                        <!-- Fixed Price Table Import Tab -->
                        <page string="Fixed Price Table" name="fixed_price_import">
                            <group>
                                <div class="alert alert-info" role="alert">
                                    <h4><i class="fa fa-table"/> Fixed Price Table Import</h4>
                                    <p>Import fixed price data from Excel files. This replaces the existing fixed price entries with your data.</p>
                                </div>
                            </group>

                            <group>
                                <div class="alert alert-warning" role="alert">
                                    <h5><i class="fa fa-info-circle me-2"></i>Excel Format Required</h5>
                                    <p class="mb-2">Upload an Excel file with the following columns:</p>
                                    <ul class="mb-2">
                                        <li><strong>Type:</strong> Fixed Time, Labour, Material, etc.</li>
                                        <li><strong>Code:</strong> Unique code (e.g., WageInP, RecMatlT)</li>
                                        <li><strong>Description:</strong> Main description</li>
                                        <li><strong>Sub-Description:</strong> Additional details (e.g., $/hr, hr/item)</li>
                                        <li><strong>Value / Cost:</strong> Numeric value</li>
                                        <li><strong>Work Centre:</strong> Work centre code (or "." for none)</li>
                                    </ul>
                                    <p class="mb-0">Download the template below to see the exact format required.</p>
                                </div>
                            </group>

                            <group>
                                <group string="File Upload">
                                    <field name="csv_file_data" filename="csv_file_name" string="Excel File"/>
                                    <field name="csv_file_name" invisible="1"/>
                                </group>
                                <group string="Import Options">
                                    <field name="fixed_price_clear_existing"/>
                                    <field name="fixed_price_update_existing"/>
                                </group>
                            </group>

                            <div class="oe_button_box">
                                <button string="Download Template" name="action_download_fixed_price_template" type="object"
                                        class="btn-secondary me-3" icon="fa-download"
                                        title="Download Excel template with sample data"/>
                                <button string="Import Fixed Price Data" name="action_csv_import" type="object"
                                        class="btn-primary" icon="fa-upload"
                                        context="{'csv_import_type': 'fixed_price'}"
                                        title="Import fixed price data from Excel file"/>
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Import/Export Tools Action -->
    <record id="action_config_matrix_import_export_tools" model="ir.actions.act_window">
        <field name="name">Matrix Import/Export Tools</field>
        <field name="res_model">config.matrix.import.export.tools</field>
        <field name="view_mode">form</field>
        <field name="target">current</field>
    </record>
</odoo>
