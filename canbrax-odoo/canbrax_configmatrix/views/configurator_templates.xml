<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main configurator template -->
        <template id="configurator_template" t-name="configurator_template">
            <t t-set="doctype" t-value="'&lt;!DOCTYPE html&gt;'" />
            <t t-raw="doctype" />
            <html>
                <head>
                    <title>Product Configurator</title>
                    <meta charset="utf-8"/>
                    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
                    <meta name="csrf-token" t-att-content="request.csrf_token()"/>

                    <!-- Bootstrap CSS -->
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"/>
                    <!-- Font Awesome -->
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>

                    <!-- Load issues_panel.js first to ensure it's available for form submission -->
                    <script type="text/javascript" src="/canbrax_configmatrix/static/src/js/issues_panel.js"></script>

                        <style>
                            body {
                                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                                background-color: #f8f9fa;
                            }
                            .configurator-container {
                                width: 100%;
                                margin: 0 auto;
                                padding: 20px;
                                background-color: #fff;
                                border-radius: 8px;
                                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                            }
                            .btn-primary {
                                background-color: #7C7BAD;
                                border-color: #7C7BAD;
                            }
                            .btn-primary:hover {
                                background-color: #645CB3;
                                border-color: #645CB3;
                            }
                            /* Excel-like styling */
                            .table-sm td {
                                padding: 0.3rem 0.5rem;
                            }
                            /* Removed conditional field highlighting */

                            /* Question number styling */
                            .question-number {
                                font-size: 13px;
                                z-index: 5;
                                opacity: 0.7;
                                transition: opacity 0.2s ease;
                            }

                            /* Subtle hover effect */
                            tr:hover .question-number {
                                opacity: 1;
                            }

                            /* Component badges for dynamic/static components */
                            .badge {
                                display: inline-block;
                                padding: 0.25em 0.5em;
                                font-size: 0.75em;
                                font-weight: 700;
                                line-height: 1;
                                text-align: center;
                                white-space: nowrap;
                                vertical-align: baseline;
                                border-radius: 0.25rem;
                                text-decoration: none;
                            }
                            .badge.bg-info {
                                color: #fff;
                                background-color: #0dcaf0 !important;
                            }
                            .badge.bg-primary {
                                color: #fff;
                                background-color: #0d6efd !important;
                            }
                            .badge.bg-success {
                                color: #fff;
                                background-color: #198754 !important;
                            }
                            .badge.bg-warning {
                                color: #000;
                                background-color: #ffc107 !important;
                            }
                            .badge.bg-dark {
                                color: #fff;
                                background-color: #212529 !important;
                            }
                            .badge.bg-light {
                                color: #000;
                                background-color: #f8f9fa !important;
                            }
                            .badge.bg-secondary {
                                color: #fff;
                                background-color: #6c757d !important;
                            }
                            .badge.me-1 {
                                margin-right: 0.25rem !important;
                            }
                            .fw-bold {
                                font-weight: 700 !important;
                            }
                            .text-muted {
                                color: #6c757d !important;
                            }
                            .text-success {
                                color: #198754 !important;
                            }
                            .small {
                                font-size: 0.875em;
                            }
                            .ms-2 {
                                margin-left: 0.5rem !important;
                            }
                            .rounded-pill {
                                border-radius: 50rem !important;
                            }

                            /* Constraints info styling removed per customer request */

                            /* Visibility conditions styling */
                            .visibility-conditions-info {
                                font-size: 0.8em;
                                color: #495057;
                                background-color: #f8f9fa;
                                border: 1px solid #e9ecef;
                                border-radius: 4px;
                                padding: 8px;
                                margin-top: 5px;
                                display: none;
                            }

                            .visibility-conditions-info.show {
                                display: block;
                            }

                            .visibility-condition-item {
                                margin-bottom: 4px;
                                padding: 3px 6px;
                                background-color: #ffffff;
                                border-radius: 3px;
                                border-left: 3px solid #007bff;
                            }

                            .visibility-condition-item:last-child {
                                margin-bottom: 0;
                            }

                            .condition-type {
                                font-weight: 600;
                                color: #007bff;
                                font-size: 0.75em;
                                text-transform: uppercase;
                            }

                            .condition-expression {
                                font-family: 'Courier New', monospace;
                                background-color: #f1f3f4;
                                padding: 2px 4px;
                                border-radius: 2px;
                                margin-top: 2px;
                                word-break: break-all;
                            }

                            .option-visibility-info {
                                font-size: 0.75em;
                                color: #6c757d;
                                margin-left: 8px;
                                font-style: italic;
                            }

                            /* Enhanced dropdown option styling for visibility conditions */
                            .dropdown-option-with-condition {
                                position: relative;
                            }

                            .dropdown-option-condition {
                                display: block;
                                font-size: 0.7em;
                                color: #6c757d;
                                font-style: italic;
                                margin-top: 2px;
                                padding-left: 4px;
                                border-left: 2px solid #dee2e6;
                            }

                            .dropdown-option-condition.condition-met {
                                color: #28a745;
                                border-left-color: #28a745;
                            }

                            .dropdown-option-condition.condition-not-met {
                                color: #dc3545;
                                border-left-color: #dc3545;
                            }

                            /* Styling for select options with conditions */
                            select.config-field option.has-condition {
                                padding-top: 4px;
                                padding-bottom: 8px;
                                line-height: 1.2;
                            }

                            /* Searchable dropdown condition styling */
                            .searchable-dropdown-item.has-condition {
                                padding-bottom: 12px;
                            }

                            .searchable-dropdown-item .option-condition-text {
                                display: block;
                                font-size: 0.7em;
                                color: #6c757d;
                                font-style: italic;
                                margin-top: 3px;
                                padding-left: 4px;
                                border-left: 2px solid #dee2e6;
                            }

                            .searchable-dropdown-item .option-condition-text.condition-met {
                                color: #28a745;
                                border-left-color: #28a745;
                            }

                            .searchable-dropdown-item .option-condition-text.condition-not-met {
                                color: #dc3545;
                                border-left-color: #dc3545;
                            }

                            /* Add padding to the left side of the label column for question numbers */
                            .table-sm td:first-child {
                                padding-left: 35px !important;
                            }

                            /* Styling for conditionally hidden fields */
                            .field-condition-hidden td:first-child {
                                opacity: 0.7;
                            }

                            .field-condition-hidden .question-number {
                                opacity: 0.5;
                            }

                            /* Styling for conditional fields that are pending (hidden with yellow background) */
                            .conditional-field-pending {
                                background-color: #fff3cd !important; /* Yellow background for hidden fields */
                            }

                            /* Styling for conditional fields that are visible (green background) */
                            .conditional-visible {
                                background-color: #e6ffe6 !important; /* Green background for visible fields */
                            }

                            /* Validation message styles */
                            .validation-message {
                                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                                margin-bottom: 10px !important;
                                animation: fadeIn 0.3s ease-in-out;
                                max-width: 100%;
                                overflow: visible;
                            }

                            .validation-message .message-text {
                                display: block;
                                padding-right: 25px;
                                word-wrap: break-word;
                            }



                            @keyframes fadeIn {
                                from { opacity: 0; transform: translateY(-10px); }
                                to { opacity: 1; transform: translateY(0); }
                            }

                            /* Issues panel styling */
                            #issues-panel {
                                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                                border-color: #ffeeba !important;
                            }

                            #issues-panel h4 {
                                color: #856404;
                            }

                            .clear-field-btn {
                                transition: all 0.2s ease;
                            }

                            .clear-field-btn:hover {
                                transform: scale(1.05);
                            }

                            /* Toggle switch styling */
                            .toggle-container {
                                display: flex;
                                align-items: center;
                                background-color: #f8f9fa;
                                border-radius: 8px;
                                padding: 10px 15px;
                                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                            }

                            .toggle-switch {
                                position: relative;
                                margin-right: 12px;
                            }

                            .toggle-input {
                                opacity: 0;
                                width: 0;
                                height: 0;
                                position: absolute;
                            }

                            .toggle-label {
                                display: block;
                                width: 48px;
                                height: 24px;
                                border-radius: 12px;
                                background-color: #e9ecef;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                position: relative;
                                margin: 0;
                            }

                            .toggle-label:after {
                                content: '';
                                position: absolute;
                                top: 2px;
                                left: 2px;
                                width: 20px;
                                height: 20px;
                                border-radius: 50%;
                                background-color: white;
                                transition: all 0.3s ease;
                                box-shadow: 0 1px 3px rgba(0,0,0,0.2);
                            }

                            .toggle-input:checked + .toggle-label {
                                background-color: #28a745;
                            }

                            .toggle-input:checked + .toggle-label:after {
                                left: calc(100% - 22px);
                            }

                            .toggle-text {
                                display: flex;
                                flex-direction: column;
                            }

                            .toggle-title {
                                font-weight: 600;
                                font-size: 14px;
                                color: #495057;
                            }

                            .toggle-description {
                                font-size: 12px;
                                color: #6c757d;
                            }

                            /* All fields are required by default */

                            /* Floating save button */
                            .floating-save-btn {
                                position: fixed;
                                bottom: 30px;
                                right: 30px;
                                width: 60px;
                                height: 60px;
                                border-radius: 50%;
                                background-color: #645CB3;
                                color: white;
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                z-index: 1000;
                                border: none;
                            }

                            .floating-save-btn:hover {
                                background-color: #534a9e;
                                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
                                transform: translateY(-2px);
                            }

                            .floating-save-btn:active {
                                transform: translateY(1px);
                                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                            }

                            .floating-save-btn i {
                                font-size: 24px;
                            }



                            /* List group styling for components */
                            .list-group {
                                padding-left: 0;
                                margin-bottom: 0;
                            }

                            .list-group-item {
                                position: relative;
                                display: block;
                                padding: 10px 15px;
                                margin-bottom: -1px;
                                background-color: #fff;
                                border: none;
                            }

                            .list-group-flush {
                                border-radius: 0;
                            }

                            .list-group-flush .list-group-item {
                                border-right: 0;
                                border-left: 0;
                                border-radius: 0;
                            }

                            .list-group-flush .list-group-item:first-child {
                                border-top: 0;
                            }

                            .list-group-flush .list-group-item:last-child {
                                border-bottom: 0;
                            }

                            .badge {
                                display: inline-block;
                                padding: 0.25em 0.4em;
                                font-size: 75%;
                                font-weight: 700;
                                line-height: 1;
                                text-align: center;
                                white-space: nowrap;
                                vertical-align: baseline;
                                border-radius: 0.25rem;
                            }

                            .badge-secondary {
                                color: #fff;
                                background-color: #6c757d;
                            }

                            .rounded-pill {
                                border-radius: 50rem;
                            }

                            .product-preview {
                                text-align: center;
                                margin-bottom: 15px;
                            }

                            .product-preview img {
                                max-height: 150px;
                                object-fit: contain;
                            }



                            /* Section highlight effect */
                            @keyframes highlight-section-animation {
                                0% { background-color: rgba(100, 92, 179, 0.1); }
                                50% { background-color: rgba(100, 92, 179, 0.3); }
                                100% { background-color: rgba(100, 92, 179, 0.1); }
                            }

                            .highlight-section {
                                animation: highlight-section-animation 2s ease;
                            }

                            @keyframes errorPulse {
                                0% { transform: scale(1); }
                                50% { transform: scale(1.02); }
                                100% { transform: scale(1); }
                            }

                            /* Error message styling improvements */
                            .error-message-container {
                                transition: all 0.3s ease;
                            }

                            .error-message-container .alert {
                                border-left: 4px solid #dc3545;
                                font-weight: 500;
                            }

                            /* Persistent information messages */
                            .persistent-info-message {
                                background-color: #e7f3ff;
                                border: 1px solid #b3d9ff;
                                border-left: 4px solid #0066cc;
                                border-radius: 0.375rem;
                                padding: 0.75rem;
                                margin: 0.5rem 0;
                                font-size: 0.9rem;
                                color: #004085;
                            }

                            .persistent-info-message .info-icon {
                                color: #0066cc;
                                margin-right: 0.5rem;
                            }

                            .persistent-info-message strong {
                                color: #003366;
                            }

                            /* Searchable select styling */
                            .searchable-select-wrapper {
                                position: relative;
                                display: inline-block;
                                width: 100%;
                            }

                            .searchable-select {
                                width: 100%;
                                padding-right: 30px !important;
                                background-image: none !important;
                                cursor: text;
                            }

                            .searchable-select-icon {
                                position: absolute;
                                right: 10px;
                                top: 50%;
                                transform: translateY(-50%);
                                pointer-events: none;
                                color: #6c757d;
                                font-size: 12px;
                            }

                            /* Enhanced select styling for better UX */
                            .searchable-select:focus {
                                border-color: #80bdff;
                                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                            }

                            /* Style for options with conditions */
                            .searchable-select option.has-condition {
                                font-style: italic;
                                color: #6c757d;
                            }

                            .searchable-select option:disabled {
                                color: #6c757d;
                                font-style: italic;
                            }

                            /* Search overlay styles */
                            .searchable-select-wrapper .search-overlay {
                                border: 1px solid #ced4da;
                                border-radius: 0.25rem;
                                font-size: 0.875rem;
                                height: calc(1.5em + 0.5rem + 2px);
                                padding: 0.25rem 0.5rem;
                            }

                            .searchable-select-wrapper .search-overlay:focus {
                                border-color: #007bff;
                                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                                outline: none;
                            }

                            /* When in search mode, make the select dropdown appear above other elements */
                            .searchable-select-wrapper .searchable-select[style*="z-index"] {
                                position: relative;
                                background: white;
                                border: 1px solid #007bff;
                                max-height: 250px;
                                overflow-y: auto;
                                padding-top: 40px; /* Add space for the search overlay */
                            }

                            /* Ensure the search overlay appears above the select options */
                            .searchable-select-wrapper .search-overlay {
                                z-index: 20;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="container-fluid mt-4 mb-4" style="width: 95%;">
                            <div class="configurator-container">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h3 class="mb-0">Product Configurator</h3>
                                </div>



                                <div id="configurator-content">
                                    <!-- Show error message if any -->
                                    <t t-if="error">
                                        <div class="alert alert-danger">
                                            <h4 class="alert-heading">Configuration Error</h4>
                                            <p><t t-esc="error"/></p>
                                        </div>
                                    </t>

                                    <!-- Show template data if available -->
                                    <t t-if="template">
                                        <div class="row mb-4">
                                            <div class="col-12">
                                                <h4 class="mb-2"><t t-esc="template.name"/></h4>
                                                <t t-if="template.description">
                                                    <p class="text-muted small mb-3"><t t-esc="template.description"/></p>
                                                </t>
                                            </div>
                                        </div>

                                        <!-- 3-column layout -->
                                        <div class="row d-flex">
                                            <!-- Left column - Product Preview -->
                                            <div class="col-md-3 sticky-column left-column" style="width: 25%;">
                                                <div class="card mb-3">
                                                    <div class="card-header py-2">
                                                        <h5 class="mb-0 fs-6">Product Preview</h5>
                                                    </div>
                                                    <div class="card-body p-2 text-center">
                                                        <div id="svg-container" class="svg-container" style="min-height: 250px; display: flex; align-items: center; justify-content: center;">
                                                            <div class="text-muted small">
                                                                <i class="fas fa-image fa-3x mb-2 d-block"></i>
                                                                Product visualization will appear here
                                                            </div>
                                                        </div>
                                                        <!-- Container for instructional images that should appear in preview -->
                                                        <div id="instructional-image-preview" class="mt-3" style="display: none;">
                                                            <div class="border-top pt-3">
                                                                <h6 class="mb-2 text-muted">Instructional Image</h6>
                                                                <div id="instructional-image-preview-content"></div>
                                                            </div>
                                                        </div>
                                                        <script>
                                                            // Initialize SVG container with template ID
                                                            document.addEventListener('DOMContentLoaded', function() {
                                                                console.log('SVG container initialized with template ID: <t t-esc="template.id"/>');

                                                                // Directly fetch and display the base SVG
                                                                fetch('/config_matrix/get_svg_components', {
                                                                    method: 'POST',
                                                                    headers: {
                                                                        'Content-Type': 'application/json',
                                                                        'X-Requested-With': 'XMLHttpRequest'
                                                                    },
                                                                    body: JSON.stringify({
                                                                        jsonrpc: "2.0",
                                                                        method: "call",
                                                                        params: {
                                                                            template_id: <t t-esc="template.id"/>
                                                                        },
                                                                        id: new Date().getTime()
                                                                    })
                                                                })
                                                                .then(response => response.json())
                                                                .then(data => {
                                                                    if (data.result &amp;&amp; data.result.success &amp;&amp; data.result.components &amp;&amp; data.result.components.length > 0) {
                                                                        // Find base component
                                                                        const baseComponent = data.result.components.find(comp => comp.component_type === 'base');
                                                                        if (baseComponent) {
                                                                            // Display the base SVG
                                                                            const svgContainer = document.getElementById('svg-container');
                                                                            if (svgContainer) {
                                                                                svgContainer.innerHTML = baseComponent.svg_content;
                                                                                console.log('Base SVG loaded directly from template');
                                                                            }
                                                                        }
                                                                    }
                                                                })
                                                                .catch(error => {
                                                                    console.error('Error loading SVG:', error);
                                                                });
                                                            });
                                                        </script>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Middle column - Configuration Fields -->
                                            <div class="col-md-6" style="width: 50%;">
                                                <!-- Sections and fields -->
                                                <t t-if="template.section_ids">
                                                    <t t-foreach="template.section_ids" t-as="section">
                                                        <div class="card mb-4">
                                                            <div class="card-header py-2" t-if="section.show_header">
                                                                <div class="d-flex justify-content-between align-items-center">
                                                                    <h5 class="mb-0 fw-bold"><t t-esc="section.name"/></h5>
                                                                    <button type="button" class="btn btn-sm btn-link section-collapse-toggle"
                                                                            t-if="section.collapsible"
                                                                            t-att-data-bs-target="'#section-' + str(section.id)">
                                                                        <t t-if="section.collapsed_by_default">
                                                                            <i class="fas fa-chevron-down"></i>
                                                                        </t>
                                                                        <t t-else="">
                                                                            <i class="fas fa-chevron-up"></i>
                                                                        </t>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div t-att-class="'card-body p-0 ' + ('collapse ' + ('' if section.collapsed_by_default else 'show') if section.collapsible else 'show')" t-att-id="'section-' + str(section.id)">
                                                                <t t-if="section.field_ids">
                                                                    <!-- Excel-like table layout -->
                                                                    <table class="table table-sm table-striped table-bordered mb-0" style="table-layout: fixed;">
                                                                        <!-- Only render fields that are visible for the current use case -->
                                                                        <t t-foreach="section.field_ids.filtered(lambda f: (active_use_case == 'check_measure' and f.check_measure_visible) or
                                                                                                                          (active_use_case == 'sales' and f.sales_visible) or
                                                                                                                          (active_use_case == 'online' and f.online_visible))" t-as="field">
                                                                            <tr class="config-field-container"
                                                                                t-att-id="'field_container_' + str(field.id)"
                                                                                t-att-data-field-id="field.id"
                                                                                t-att-data-technical-name="field.technical_name"
                                                                                t-att-data-visibility-condition="field.visibility_condition or 'true'"
                                                                                t-att-data-is-conditional="field.visibility_condition and field.visibility_condition != 'true'"
                                                                                t-att-data-use-dynamic-default="((active_use_case == 'check_measure' and field.check_measure_use_dynamic_default) or
                                                                                                                 (active_use_case == 'sales' and field.sales_use_dynamic_default) or
                                                                                                                 (active_use_case == 'online' and field.online_use_dynamic_default)) and 'true' or 'false'"
                                                                                t-att-data-dynamic-default-template="active_use_case == 'check_measure' and field.check_measure_dynamic_default_template or
                                                                                                                     active_use_case == 'sales' and field.sales_dynamic_default_template or
                                                                                                                     active_use_case == 'online' and field.online_dynamic_default_template or ''">
                                                                                <td style="width: 30%; vertical-align: middle; background-color: #f8f9fa; text-align: right; padding-right: 15px; position: relative;">
                                                                                    <!-- Question number -->
                                                                                    <span class="question-number" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 13px;">
                                                                                        <t t-esc="field.question_number"/>
                                                                                    </span>
                                                                                    <label class="form-label mb-0" t-att-for="'field_' + str(field.id)">
                                                                                        <t t-esc="field.name"/>
                                                                                    </label>
                                                                                </td>
                                                                                <td style="width: 70%;">
                                                                                    <!-- Render field based on type -->
                                                                                    <t t-if="field.field_type == 'text'">
                                                                                        <div class="position-relative">
                                                                                            <input type="text" class="form-control form-control-sm config-field"
                                                                                                t-att-id="'field_' + str(field.id)"
                                                                                                t-att-name="'field_' + str(field.id)"
                                                                                                t-att-data-field-id="field.id"
                                                                                                t-att-data-technical-name="field.technical_name"
                                                                                                t-att-data-field-type="field.field_type"
                                                                                                t-att-data-default-value="active_use_case == 'check_measure' and field.check_measure_default_value or
                                                                                                                          active_use_case == 'sales' and field.sales_default_value or
                                                                                                                          active_use_case == 'online' and field.online_default_value or ''"
                                                                                                t-att-value="config_values.get(str(field.id), request.env['config.matrix.configuration'].sudo()._get_field_default_value(field, active_use_case))"/>
                                                                                        </div>
                                                                                        <!-- Validation message container (auto-hides when valid value is entered) -->
                                                                                        <div t-att-id="'validation_message_' + str(field.id)" class="validation-message mt-2 alert alert-warning py-2 px-3" style="display: none; position: relative; z-index: 1000; width: 100%; font-size: 14px; font-weight: 500; border-left: 4px solid #ffc107;">
                                                                                            <span class="message-text"></span>
                                                                                        </div>
                                                                                    </t>

                                                                                    <t t-elif="field.field_type == 'number'">
                                                                                        <div class="position-relative">
                                                                                            <input type="text" inputmode="numeric"
                                                                                                   t-att-pattern="field.decimal_precision == 0 and '[0-9]*' or '[0-9]*\.?[0-9]*'"
                                                                                                   class="form-control form-control-sm config-field"
                                                                                                   t-att-id="'field_' + str(field.id)"
                                                                                                   t-att-name="'field_' + str(field.id)"
                                                                                                   t-att-data-field-id="field.id"
                                                                                                   t-att-data-technical-name="field.technical_name"
                                                                                                   t-att-data-field-type="field.field_type"
                                                                                                   t-att-data-decimal-precision="field.decimal_precision or 0"
                                                                                                   t-att-data-min="active_use_case == 'check_measure' and field.check_measure_min_value or
                                                                                                             active_use_case == 'sales' and field.sales_min_value or
                                                                                                             active_use_case == 'online' and field.online_min_value"
                                                                                                   t-att-data-max="active_use_case == 'check_measure' and field.check_measure_max_value or
                                                                                                             active_use_case == 'sales' and field.sales_max_value or
                                                                                                             active_use_case == 'online' and field.online_max_value"
                                                                                                   t-att-data-step="field.decimal_precision and (10 ** -field.decimal_precision) or 1"
                                                                                                   t-att-data-default-value="active_use_case == 'check_measure' and field.check_measure_default_value or
                                                                                                                          active_use_case == 'sales' and field.sales_default_value or
                                                                                                                          active_use_case == 'online' and field.online_default_value or ''"
                                                                                                   t-att-value="config_values.get(str(field.id), request.env['config.matrix.configuration'].sudo()._get_field_default_value(field, active_use_case))"/>
                                                                                            <!-- Add up/down buttons to mimic number input -->
                                                                                            <div class="number-controls" style="display:none; position: absolute; right: 5px; top: 50%; transform: translateY(-50%);">
                                                                                                <button type="button" class="btn btn-sm btn-light number-increment" style="padding: 0 5px; font-size: 12px;">+</button>
                                                                                                <button type="button" class="btn btn-sm btn-light number-decrement" style="padding: 0 5px; font-size: 12px;">-</button>
                                                                                            </div>
                                                                                        </div>
                                                                                        <!-- Validation message container (auto-hides when valid value is entered) -->
                                                                                        <div t-att-id="'validation_message_' + str(field.id)" class="validation-message mt-2 alert alert-warning py-2 px-3" style="display: none; position: relative; z-index: 1000; width: 100%; font-size: 14px; font-weight: 500; border-left: 4px solid #ffc107;">
                                                                                            <span class="message-text"></span>
                                                                                        </div>
                                                                                    </t>

                                                                                    <t t-elif="field.field_type == 'boolean'">
                                                                                        <div class="form-check mb-0">
                                                                                            <input class="form-check-input config-field" type="checkbox"
                                                                                                   t-att-id="'field_' + str(field.id)"
                                                                                                   t-att-name="'field_' + str(field.id)"
                                                                                                   t-att-data-field-id="field.id"
                                                                                                   t-att-data-technical-name="field.technical_name"
                                                                                                   t-att-data-field-type="field.field_type"
                                                                                                   t-att-data-default-value="active_use_case == 'check_measure' and field.check_measure_default_value or
                                                                                                                          active_use_case == 'sales' and field.sales_default_value or
                                                                                                                          active_use_case == 'online' and field.online_default_value or ''"
                                                                                                   t-att-checked="request.env['config.matrix.configuration'].sudo()._is_field_checked(field, config_values, active_use_case)"/>
                                                                                            <label class="form-check-label" t-att-for="'field_' + str(field.id)">Yes</label>
                                                                                        </div>
                                                                                    </t>

                                                                                    <t t-elif="field.field_type == 'selection'">
                                                                                        <!-- Check if field should be searchable -->
                                                                                        <t t-if="field.is_searchable_dropdown">
                                                                                            <!-- Simple searchable select with CSS styling -->
                                                                                            <div class="searchable-select-wrapper">
                                                                                                <select class="form-select form-select-sm config-field searchable-select"
                                                                                                        t-att-id="'field_' + str(field.id)"
                                                                                                        t-att-name="'field_' + str(field.id)"
                                                                                                        t-att-data-field-id="field.id"
                                                                                                        t-att-data-technical-name="field.technical_name"
                                                                                                        t-att-data-field-type="field.field_type"
                                                                                                        t-att-data-default-value="active_use_case == 'check_measure' and (field.check_measure_default_option_id and field.check_measure_default_option_id.value or field.check_measure_default_value) or
                                                                                                                                    active_use_case == 'sales' and (field.sales_default_option_id and field.sales_default_option_id.value or field.sales_default_value) or
                                                                                                                                    active_use_case == 'online' and (field.online_default_option_id and field.online_default_option_id.value or field.online_default_value) or ''"
                                                                                                        data-live-search="true">
                                                                                                    <!-- Add a placeholder option only if no default value is set -->
                                                                                                    <option value="" disabled="disabled"
                                                                                                            t-att-selected="not request.env['config.matrix.configuration'].sudo()._has_default_value(field, config_values, active_use_case) and 'selected'"
                                                                                                            t-att-style="request.env['config.matrix.configuration'].sudo()._has_default_value(field, config_values, active_use_case) and 'display: none;' or ''">-- Type to search or select --</option>
                                                                                                    <t t-foreach="field.option_ids" t-as="option">
                                                                                                        <option t-att-value="option.value"
                                                                                                                t-att-class="option.visibility_condition and option.visibility_condition != 'true' and 'has-condition' or ''"
                                                                                                                t-att-selected="request.env['config.matrix.configuration'].sudo()._is_option_selected(option, field, config_values, active_use_case)"
                                                                                                                t-att-data-option-id="option.id"
                                                                                                                t-att-data-option-value="option.value"
                                                                                                                t-att-data-visibility-condition="option.visibility_condition or 'true'"
                                                                                                                t-att-data-condition-text="option.condition_display_text or ''"
                                                                                                                t-att-title="option.condition_display_text and ('Condition: ' + option.condition_display_text) or ''">
                                                                                                            <t t-esc="option.name"/>
                                                                                                        </option>
                                                                                                    </t>
                                                                                                </select>
                                                                                                <i class="fas fa-search searchable-select-icon"></i>
                                                                                            </div>
                                                                                        </t>
                                                                                        <t t-else="">
                                                                                            <!-- Regular dropdown -->
                                                                                            <select class="form-select form-select-sm config-field"
                                                                                                    t-att-id="'field_' + str(field.id)"
                                                                                                    t-att-name="'field_' + str(field.id)"
                                                                                                    t-att-data-field-id="field.id"
                                                                                                    t-att-data-technical-name="field.technical_name"
                                                                                                    t-att-data-field-type="field.field_type"
                                                                                                    t-att-data-default-value="active_use_case == 'check_measure' and (field.check_measure_default_option_id and field.check_measure_default_option_id.value or field.check_measure_default_value) or
                                                                                                                                active_use_case == 'sales' and (field.sales_default_option_id and field.sales_default_option_id.value or field.sales_default_value) or
                                                                                                                                active_use_case == 'online' and (field.online_default_option_id and field.online_default_option_id.value or field.online_default_value) or ''">
                                                                                                <!-- Add a placeholder option only if no default value is set -->
                                                                                                <option value="" disabled="disabled"
                                                                                                        t-att-selected="not request.env['config.matrix.configuration'].sudo()._has_default_value(field, config_values, active_use_case) and 'selected'"
                                                                                                        t-att-style="request.env['config.matrix.configuration'].sudo()._has_default_value(field, config_values, active_use_case) and 'display: none;' or ''">-- Select an option --</option>
                                                                                                <t t-foreach="field.option_ids" t-as="option">
                                                                                                    <option t-att-value="option.value"
                                                                                                            t-att-class="option.visibility_condition and option.visibility_condition != 'true' and 'has-condition' or ''"
                                                                                                            t-att-selected="request.env['config.matrix.configuration'].sudo()._is_option_selected(option, field, config_values, active_use_case)"
                                                                                                            t-att-data-option-id="option.id"
                                                                                                            t-att-data-option-value="option.value"
                                                                                                            t-att-data-visibility-condition="option.visibility_condition or 'true'"
                                                                                                            t-att-data-condition-text="option.condition_display_text or ''"
                                                                                                            t-att-title="option.condition_display_text and ('Condition: ' + option.condition_display_text) or ''">
                                                                                                        <t t-esc="option.name"/>
                                                                                                    </option>
                                                                                                </t>
                                                                                            </select>
                                                                                        </t>
                                                                                    </t>

                                                                                    <t t-elif="field.field_type == 'date'">
                                                                                        <input type="date" class="form-control form-control-sm config-field"
                                                                                               t-att-id="'field_' + str(field.id)"
                                                                                               t-att-name="'field_' + str(field.id)"
                                                                                               t-att-data-field-id="field.id"
                                                                                               t-att-data-technical-name="field.technical_name"
                                                                                               t-att-data-field-type="field.field_type"
                                                                                               t-att-data-default-value="active_use_case == 'check_measure' and field.check_measure_default_value or
                                                                                                                         active_use_case == 'sales' and field.sales_default_value or
                                                                                                                         active_use_case == 'online' and field.online_default_value or ''"
                                                                                               t-att-value="config_values.get(str(field.id),
                                                                                                          active_use_case == 'check_measure' and field.check_measure_default_value or
                                                                                                          active_use_case == 'sales' and field.sales_default_value or
                                                                                                          active_use_case == 'online' and field.online_default_value or '')"/>
                                                                                    </t>

                                                                                    <t t-else="">
                                                                                        <input type="text" class="form-control form-control-sm config-field"
                                                                                               t-att-id="'field_' + str(field.id)"
                                                                                               t-att-name="'field_' + str(field.id)"
                                                                                               t-att-data-field-id="field.id"
                                                                                               t-att-data-technical-name="field.technical_name"
                                                                                               t-att-data-field-type="field.field_type"
                                                                                               t-att-data-default-value="active_use_case == 'check_measure' and field.check_measure_default_value or
                                                                                                                         active_use_case == 'sales' and field.sales_default_value or
                                                                                                                         active_use_case == 'online' and field.online_default_value or ''"
                                                                                               t-att-value="config_values.get(str(field.id),
                                                                                                          active_use_case == 'check_measure' and field.check_measure_default_value or
                                                                                                          active_use_case == 'sales' and field.sales_default_value or
                                                                                                          active_use_case == 'online' and field.online_default_value or '')"/>
                                                                                    </t>

                                                                                    <!-- Help text and constraints are included in the field data if available -->
                                                                                    <div class="form-text text-muted small mt-1"
                                                                                         t-att-data-use-dynamic-help="((active_use_case == 'check_measure' and field.check_measure_use_dynamic_help) or
                                                                                                                      (active_use_case == 'sales' and field.sales_use_dynamic_help) or
                                                                                                                      (active_use_case == 'online' and field.online_use_dynamic_help)) and 'true' or 'false'"
                                                                                         t-att-data-dynamic-help-template="active_use_case == 'check_measure' and field.check_measure_dynamic_help_template or
                                                                                                                          active_use_case == 'sales' and field.sales_dynamic_help_template or
                                                                                                                          active_use_case == 'online' and field.online_dynamic_help_template or ''"
                                                                                         t-att-data-use-case="active_use_case">
                                                                                        <!-- Dynamic help text container -->
                                                                                        <div class="dynamic-help-text" style="display: none;"></div>

                                                                                        <!-- Static help text based on active use case -->
                                                                                        <div class="static-help-text">
                                                                                            <t t-if="active_use_case == 'check_measure' and field.check_measure_help_text">
                                                                                                <div><t t-esc="field.check_measure_help_text"/></div>
                                                                                            </t>
                                                                                            <t t-elif="active_use_case == 'sales' and field.sales_help_text">
                                                                                                <div><t t-esc="field.sales_help_text"/></div>
                                                                                            </t>
                                                                                            <t t-elif="active_use_case == 'online' and field.online_help_text">
                                                                                                <div><t t-esc="field.online_help_text"/></div>
                                                                                            </t>
                                                                                        </div>

                                                                                        <!-- Error Message Container -->
                                                                                        <div class="error-message-container mt-1"
                                                                                             t-att-id="'error_message_' + str(field.id)"
                                                                                             t-att-data-field-id="field.id"
                                                                                             t-att-data-use-dynamic-error="((active_use_case == 'check_measure' and field.check_measure_use_dynamic_error) or
                                                                                                                          (active_use_case == 'sales' and field.sales_use_dynamic_error) or
                                                                                                                          (active_use_case == 'online' and field.online_use_dynamic_error)) and 'true' or 'false'"
                                                                                             t-att-data-dynamic-error-template="active_use_case == 'check_measure' and field.check_measure_dynamic_error_template or
                                                                                                                              active_use_case == 'sales' and field.sales_dynamic_error_template or
                                                                                                                              active_use_case == 'online' and field.online_dynamic_error_template or ''"
                                                                                             t-att-data-error-condition="active_use_case == 'check_measure' and field.check_measure_error_condition or
                                                                                                                        active_use_case == 'sales' and field.sales_error_condition or
                                                                                                                        active_use_case == 'online' and field.online_error_condition or ''"
                                                                                             t-att-data-use-case="active_use_case"
                                                                                             style="display: none;">
                                                                                            <div class="alert alert-danger fade show" role="alert">
                                                                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                                                                <!-- Dynamic error text container -->
                                                                                                <span class="dynamic-error-text"></span>
                                                                                                <!-- Static error text based on active use case -->
                                                                                                <span class="static-error-text">
                                                                                                    <t t-if="active_use_case == 'check_measure' and field.check_measure_error_text">
                                                                                                        <t t-esc="field.check_measure_error_text"/>
                                                                                                    </t>
                                                                                                    <t t-elif="active_use_case == 'sales' and field.sales_error_text">
                                                                                                        <t t-esc="field.sales_error_text"/>
                                                                                                    </t>
                                                                                                    <t t-elif="active_use_case == 'online' and field.online_error_text">
                                                                                                        <t t-esc="field.online_error_text"/>
                                                                                                    </t>
                                                                                                </span>
                                                                                                <!-- No close button - error messages auto-hide when condition is resolved -->
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- Instructional image if available -->
                                                                                        <t t-if="field.instructional_image">
                                                                                            <div class="mt-2">
                                                                                                <a href="#" class="show-instructions-link text-primary"
                                                                                                   t-att-data-field-id="field.id"
                                                                                                   t-att-data-show-in-preview="field.show_in_preview and 'true' or 'false'">
                                                                                                    <i class="fas fa-info-circle me-1"></i>Show Instructions
                                                                                                </a>
                                                                                                <!-- No thumbnail under "Show Instructions" - only show in preview panel -->
                                                                                            </div>
                                                                                        </t>

                                                                                        <!-- Min/Max constraints removed per customer request -->

                                                                                        <!-- Visibility conditions info -->
                                                                                        <div class="visibility-conditions-info" t-att-id="'visibility_conditions_' + str(field.id)">
                                                                                            <div class="visibility-condition-item">
                                                                                                <div class="condition-type">Field Condition</div>
                                                                                                <div class="condition-expression" t-att-data-condition="field.visibility_condition or 'true'">
                                                                                                    <t t-esc="field.visibility_condition or 'Always visible'"/>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </td>
                                                                            </tr>
                                                                        </t>
                                                                    </table>
                                                                </t>
                                                                <t t-else="">
                                                                    <div class="p-3 text-muted">
                                                                        No fields defined for this section.
                                                                    </div>
                                                                </t>
                                                            </div>
                                                        </div>
                                                    </t>
                                                </t>
                                            </div>
                                            <!-- Right column - Toggle and Components -->
                                            <div class="col-md-3 sticky-column right-column" style="width: 25%;">
                                                <!-- Global Color Selector -->
                                                <div class="card mb-3" id="global-color-selector" style="display: none;">
                                                    <div class="card-header py-2">
                                                        <h5 class="mb-0 fs-6">Global Color Selection</h5>
                                                    </div>
                                                    <div class="card-body p-3">
                                                        <p class="mb-2 small text-muted">Select a default color to apply to all color fields.</p>
                                                        <select id="global-color-value" class="form-select form-select-sm">
                                                            <option value="">-- Select a default color --</option>
                                                            <!-- Color options will be populated dynamically -->
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- Show All Fields Toggle Card -->
                                                <div class="card mb-3">
                                                    <div class="card-header py-2">
                                                        <h5 class="mb-0 fs-6">Display Options</h5>
                                                    </div>
                                                    <div class="card-body p-3">
                                                        <div class="d-flex align-items-center mb-3">
                                                            <div class="toggle-switch me-3">
                                                                <input type="checkbox" id="show-all-fields" class="toggle-input" checked="false" />
                                                                <label for="show-all-fields" class="toggle-label"></label>
                                                            </div>
                                                            <div>
                                                                <span class="toggle-title">Show All Fields</span>
                                                                <div class="toggle-description">Show fields even when conditions aren't met</div>
                                                            </div>
                                                        </div>
                                                        <div class="d-flex align-items-center">
                                                            <div class="toggle-switch me-3">
                                                                <input type="checkbox" id="show-visibility-conditions" class="toggle-input" checked="false" />
                                                                <label for="show-visibility-conditions" class="toggle-label"></label>
                                                            </div>
                                                            <div>
                                                                <span class="toggle-title">Show Visibility Conditions</span>
                                                                <div class="toggle-description">Display conditions for fields and options</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Components List -->
                                                <div class="card mb-3">
                                                    <div class="card-header py-2">
                                                        <h5 class="mb-0 fs-6">Components</h5>
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <div id="components-list" class="components-list" style="max-height: 250px; overflow-y: auto;">
                                                            <div class="text-muted small p-3 text-center">
                                                                No components selected yet
                                                            </div>
                                                        </div>
                                                        <div class="p-3 border-top">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <strong>Total:</strong>
                                                                <span id="configuration-price" class="text-success fw-bold">$0.00</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>



                                                <!-- Operation Costs -->
                                                <div class="card mb-3">
                                                    <div class="card-header py-2">
                                                        <h5 class="mb-0 fs-6">Operation Costs</h5>
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <div id="operation-costs-list" class="operation-costs-list" style="max-height: 250px; overflow-y: auto;">
                                                            <table class="table table-sm table-striped">
                                                                <thead>
                                                                    <tr>
                                                                        <th style="width: 60%;">Operation</th>
                                                                        <th style="width: 40%;" class="text-end">Cost</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="operation-costs-table-body">
                                                                    <tr>
                                                                        <td colspan="2" class="text-center text-muted">
                                                                            <i class="fas fa-info-circle me-1"></i>
                                                                            No operations calculated yet
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                        <div class="p-3 border-top">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <strong>Total:</strong>
                                                                <span id="configuration-operation-costs" class="text-success fw-bold">$0.00</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- Sale Prices -->
                                                <div class="card">
                                                    <div class="card-header py-2">
                                                        <h5 class="mb-0 fs-6">Sale Prices</h5>
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <div id="sale-prices-list" class="sale-prices-list" style="max-height: 250px; overflow-y: auto;">
                                                            <table class="table table-sm table-striped">
                                                                <thead>
                                                                    <tr>
                                                                        <th style="width: 60%;">Sale Price Matrix</th>
                                                                        <th style="width: 40%;" class="text-end">Price</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="sale-prices-table-body">
                                                                    <tr>
                                                                        <td colspan="2" class="text-center text-muted">
                                                                            <i class="fas fa-info-circle me-1"></i>
                                                                            No sale prices calculated yet
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                        <div class="p-3 border-top">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <strong>Total:</strong>
                                                                <span id="configuration-sale-prices" class="text-success fw-bold">$0.00</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Total Aggregated Price -->
                                                <div class="card mt-3 border-primary">
                                                    <div class="card-header py-2 bg-primary text-white">
                                                        <h5 class="mb-0 fs-6"><i class="fas fa-calculator me-2"></i>Total Aggregated Price</h5>
                                                    </div>
                                                    <div class="card-body p-3">
                                                        <div class="row">
                                                            <div class="col-4">
                                                                <small class="text-muted">Components:</small><br/>
                                                                <span id="total-components" class="fw-bold">$0.00</span>
                                                            </div>
                                                            <div class="col-4">
                                                                <small class="text-muted">Operations:</small><br/>
                                                                <span id="total-operations" class="fw-bold">$0.00</span>
                                                            </div>
                                                            <div class="col-4">
                                                                <small class="text-muted">Sale Prices:</small><br/>
                                                                <span id="configuration-price-matrix" class="fw-bold">$0.00</span>
                                                            </div>
                                                        </div>
                                                        <hr class="my-2"/>
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <strong class="fs-5">Grand Total:</strong>
                                                            <span id="configuration-total-price" class="text-primary fw-bold fs-4">$0.00</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Validation summary -->
                                        <div id="validation-summary" class="alert alert-danger mb-4" style="display: none;">
                                            <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Please fix the following issues:</h5>
                                            <ul id="validation-errors" class="mb-0 ps-3">
                                                <!-- Validation errors will be added here dynamically -->
                                            </ul>
                                        </div>

                                        <form id="config-form" method="post" action="/config_matrix/save_config">
                                            <!-- CSRF token -->
                                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                            <!-- Hidden fields for parameters -->
                                            <input type="hidden" name="template_id" t-att-value="template.id"/>
                                            <t t-if="product">
                                                <input type="hidden" name="product_id" t-att-value="product.id"/>
                                            </t>
                                            <t t-if="order_line">
                                                <input type="hidden" name="order_line_id" t-att-value="order_line.id"/>
                                            </t>
                                            <t t-if="params.get('config_id')">
                                                <input type="hidden" name="config_id" t-att-value="params.get('config_id')"/>
                                            </t>
                                            <input type="hidden" name="config_data" id="config-data-field" value=""/>

                                            <input type="hidden" name="configuration_price" id="field-price" value="0.00"/>
                                            <input type="hidden" name="configuration_price_matrix" id="field-price-matrix" value="0.00"/>
                                            <input type="hidden" name="total_price" id="total-price-field" value="0.00"/>

                                            <!-- Hidden fields for form submission - the visible fields are already rendered in the middle column -->
                                            <div style="display: none;">
                                                <t t-if="template.section_ids">
                                                    <t t-foreach="template.section_ids" t-as="section">
                                                        <t t-if="section.field_ids">
                                                            <t t-foreach="section.field_ids.filtered(lambda f: (active_use_case == 'check_measure' and f.check_measure_visible) or
                                                                                                              (active_use_case == 'sales' and f.sales_visible) or
                                                                                                              (active_use_case == 'online' and f.online_visible))" t-as="field">
                                                                <!-- Hidden input for each field to ensure form submission works -->
                                                                <input type="hidden" t-att-name="'field_' + str(field.id)" t-att-id="'hidden_field_' + str(field.id)" />
                                                            </t>
                                                        </t>
                                                    </t>
                                                </t>
                                            </div>

                                            <!-- Submit buttons -->
                                            <div class="mt-4">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <t t-if="order_line and order_line.order_id">
                                                            <a t-att-href="'/web#id=%s&amp;view_type=form&amp;model=sale.order' % order_line.order_id.id" class="btn btn-secondary">Back to Sales Order</a>
                                                        </t>
                                                        <t t-else="">
                                                            <a href="/web" class="btn btn-secondary">Back to Dashboard</a>
                                                        </t>
                                                        <button type="button" id="show-issues-panel" class="btn btn-warning ms-2">
                                                            <i class="fas fa-exclamation-triangle me-1"></i> Show Issues
                                                        </button>
                                                        <button type="button" id="show-debug-panel" class="btn btn-info ms-2">Show Conditions</button>
                                                        <button type="button" id="show-calculated-fields-panel" class="btn btn-success ms-2">
                                                            <i class="fas fa-calculator me-1"></i> Calculated Fields
                                                        </button>
                                                        <button type="button" id="showSvgButtonInline" class="btn btn-info ms-2">Show SVG</button>
                                                        <button type="button" id="show-mesh-panel" class="btn btn-primary ms-2">
                                                            <i class="fas fa-th me-1"></i> Mesh
                                                        </button>
                                                        <button type="button" id="show-conditions-debug-panel" class="btn btn-warning ms-2">
                                                            <i class="fas fa-bug me-1"></i> Debug Conditions
                                                        </button>
                                                        <button type="button" id="show-multi-components-panel" class="btn btn-success ms-2">
                                                            <i class="fas fa-cogs me-1"></i> Multi Components
                                                        </button>
                                                    </div>
                                                    <div class="col-md-6 text-end">
                                                        <button type="submit" class="btn btn-primary" id="save-config-btn">Save Configuration</button>
                                                    </div>
                                                </div>
                                            </div>



                                            <!-- Floating save button -->
                                            <button type="button" class="floating-save-btn" id="floating-save-btn" title="Save Configuration">
                                                <i class="fas fa-check"></i>
                                            </button>

                                            <!-- Modal for expanded instructional image -->
                                            <div class="modal fade" id="instructionalImageModal" tabindex="-1" aria-labelledby="instructionalImageModalLabel" aria-hidden="true">
                                                <div class="modal-dialog modal-dialog-centered modal-lg">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="instructionalImageModalLabel">Instructional Image</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body text-center">
                                                            <img id="expandedInstructionalImage" class="img-fluid" style="max-height: 80vh;" src="" alt="Instructional Image"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Debug Panel -->
                                            <div id="debug-panel" class="mt-4 p-3 border rounded bg-light" style="display: none;">
                                                <h4>Debug Information</h4>
                                                <div class="mb-3">
                                                    <button type="button" id="refresh-debug" class="btn btn-sm btn-secondary">Refresh</button>
                                                    <button type="button" id="hide-debug-panel" class="btn btn-sm btn-danger float-end">Close</button>
                                                </div>
                                                <h5>Field Values &amp; Calculated Values</h5>
                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    <strong>Calculated values</strong> (prefixed with _CALCULATED_) show the results of ternary operations and condition evaluations for testing.
                                                </div>
                                                <pre id="field-values-debug" class="p-2 bg-white border rounded" style="max-height: 400px; overflow-y: auto; font-size: 12px;"></pre>
                                                <h5>Visibility Conditions</h5>
                                                <div id="visibility-conditions-debug" class="p-2 bg-white border rounded" style="max-height: 300px; overflow-y: auto;">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th>Field/Option</th>
                                                                <th>Condition</th>
                                                                <th>Visible</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="conditions-table-body">
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>

                                            <!-- Calculated Fields Only Panel -->
                                            <div id="calculated-fields-panel" class="mt-4 p-3 border rounded bg-light" style="display: none;">
                                                <h4>Calculated Fields Only</h4>
                                                <div class="mb-3">
                                                    <button type="button" id="refresh-calculated-fields" class="btn btn-sm btn-secondary">Refresh</button>
                                                    <button type="button" id="hide-calculated-fields-panel" class="btn btn-sm btn-danger float-end">Close</button>
                                                </div>
                                                <div class="alert alert-success">
                                                    <i class="fas fa-calculator me-1"></i>
                                                    <strong>Calculated Fields Only</strong> - Shows only fields prefixed with _CALCULATED_ with their technical names for easy debugging.
                                                </div>
                                                <div class="p-2 bg-white border rounded" style="max-height: 400px; overflow-y: auto;">
                                                    <table class="table table-sm table-striped">
                                                        <thead class="table-dark">
                                                            <tr>
                                                                <th style="width: 40%;">Technical Name</th>
                                                                <th style="width: 20%;">Value</th>
                                                                <th style="width: 15%;">Type</th>
                                                                <th style="width: 25%;">Description</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="calculated-fields-table-body">
                                                            <tr>
                                                                <td colspan="4" class="text-center text-muted">
                                                                    <i class="fas fa-info-circle me-1"></i>
                                                                    No calculated fields found. Import calculated fields or configure some values to see results.
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>

                                            <!-- Conditions Debug Panel -->
                                            <div id="conditions-debug-panel" class="mt-4 p-3 border rounded bg-light" style="display: none;">
                                                <h4>Conditions Debug - Multi-Condition Analysis</h4>
                                                <div class="mb-3">
                                                    <button type="button" id="refresh-conditions-debug" class="btn btn-sm btn-secondary">Refresh</button>
                                                    <button type="button" id="hide-conditions-debug-panel" class="btn btn-sm btn-danger float-end">Close</button>
                                                </div>
                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    This panel shows fields/options that have multiple visibility conditions.
                                                    All conditions must be TRUE for the option to be visible.
                                                </div>
                                                <div class="p-2 bg-white border rounded" style="max-height: 400px; overflow-y: auto;">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th style="width: 20%;">Field/Option</th>
                                                                <th style="width: 50%;">Condition Details</th>
                                                                <th style="width: 15%;">Result</th>
                                                                <th style="width: 15%;">Overall Status</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="conditions-debug-table-body">
                                                            <tr>
                                                                <td colspan="4" class="text-center">
                                                                    <i class="fas fa-spinner fa-spin"></i> Analyzing conditions...
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>

                                            </div>

                                            <!-- Issues Panel -->
                                            <div id="issues-panel" class="mt-4 p-3 border rounded bg-light" style="display: none;">
                                                <h4>Hidden Fields with Values</h4>
                                                <div class="mb-3">
                                                    <button type="button" id="issues-refresh" class="btn btn-sm btn-secondary">Refresh</button>
                                                    <button type="button" id="clear-all-issues" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-eraser me-1"></i> Clear All
                                                    </button>
                                                    <button type="button" id="clear-dependency-chain" class="btn btn-sm btn-info">
                                                        <i class="fas fa-link me-1"></i> Clear Chain
                                                    </button>
                                                    <button type="button" id="hide-issues-panel" class="btn btn-sm btn-danger float-end">Close</button>
                                                </div>
                                                <div class="p-2 bg-white border rounded" style="max-height: 300px; overflow-y: auto;">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th>Field ID</th>
                                                                <th>Technical Name</th>
                                                                <th>Value</th>
                                                                <th>Action</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="issues-table-body">
                                                            <tr>
                                                                <td colspan="4" class="text-center">
                                                                    <i class="fas fa-spinner fa-spin"></i> Scanning for issues...
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div class="mt-3 small text-muted">
                                                    <p><i class="fas fa-info-circle me-1"></i> This panel shows hidden fields that have values. These fields are automatically detected regardless of the "Show Hidden Fields" toggle state.</p>
                                                    <p><i class="fas fa-link me-1"></i> <strong>Clear Chain</strong> button triggers dependency chain clearing - when a field becomes hidden, it clears its value and any dependent fields.</p>
                                                </div>
                                            </div>

                                            <!-- SVG Components Panel -->
                                            <div id="svg-panel" class="mt-4 p-3 border rounded bg-light" style="display: none;">
                                                <h4>SVG Components</h4>
                                                <div class="mb-3">
                                                    <button type="button" id="svg-components-refresh" class="btn btn-sm btn-secondary">Refresh</button>
                                                    <button type="button" id="hide-svg-panel" class="btn btn-sm btn-danger float-end">Close</button>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <h5>Components List</h5>
                                                        <div class="p-2 bg-white border rounded" style="max-height: 300px; overflow-y: auto;">
                                                            <table class="table table-sm">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Component Name</th>
                                                                        <th>Type</th>
                                                                        <th>Z-Index</th>
                                                                        <th>Visibility Condition</th>
                                                                        <th>Status</th>
                                                                        <th>Action</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="svg-components-table-body">
                                                                    <tr>
                                                                        <td colspan="6" class="text-center">
                                                                            <i class="fas fa-spinner fa-spin"></i> Loading components...
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <h5>SVG Preview</h5>
                                                        <div id="svg-preview-container" class="p-2 bg-white border rounded" style="min-height: 200px; display: flex; align-items: center; justify-content: center;">
                                                            <div class="text-muted">
                                                                <i class="fas fa-image fa-3x mb-2 d-block"></i>
                                                                Select a component to preview
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Multi Components Helper Panel -->
                                            <div id="multi-components-panel" class="mt-4 p-3 border rounded bg-light" style="display: none;">
                                                <h4>Multi Components Helper</h4>
                                                <div class="mb-3">
                                                    <button type="button" id="multi-components-refresh" class="btn btn-sm btn-secondary">Refresh</button>
                                                    <button type="button" id="hide-multi-components-panel" class="btn btn-sm btn-danger float-end">Close</button>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h5>Dynamic Mapping Questions</h5>
                                                        <div class="p-2 bg-white border rounded" style="max-height: 300px; overflow-y: auto;">
                                                            <div id="dynamic-questions-list">
                                                                <div class="text-muted text-center p-3">
                                                                    <i class="fas fa-spinner fa-spin"></i> Loading dynamic mappings...
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h5>Filtered Product Matches</h5>
                                                        <div class="p-2 bg-white border rounded" style="max-height: 300px; overflow-y: auto;">
                                                            <div id="filtered-products-list">
                                                                <div class="text-muted text-center p-3">
                                                                    Select a dynamic mapping to see filtered products
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Mesh Hunt Panel -->
                                            <div id="mesh-panel" class="mt-4 p-3 border rounded bg-light" style="display: none;">
                                                <h4>Mesh Hunt Operations</h4>
                                                <div class="mb-3">
                                                    <button type="button" id="mesh-hunt-refresh" class="btn btn-sm btn-secondary">Refresh</button>
                                                    <button type="button" id="hide-mesh-panel" class="btn btn-sm btn-danger float-end">Close</button>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h5>Mesh Requirements</h5>
                                                        <div class="p-2 bg-white border rounded" style="max-height: 300px; overflow-y: auto;">
                                                            <div id="mesh-requirements-list">
                                                                <div class="text-muted text-center p-3">
                                                                    <i class="fas fa-spinner fa-spin"></i> Loading mesh requirements...
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h5>Component Assignment</h5>
                                                        <div class="p-2 bg-white border rounded" style="max-height: 300px; overflow-y: auto;">
                                                            <div id="mesh-components-list">
                                                                <div class="text-muted text-center p-3">
                                                                    Click "Refresh" to hunt for mesh components
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </t>

                                    <!-- Show error if no template -->
                                    <t t-if="not template and not error">
                                        <div class="alert alert-warning">
                                            <h4 class="alert-heading">No Template Available</h4>
                                            <p>No configuration template was found for this product.</p>
                                        </div>
                                        <div class="mt-4">
                                            <t t-if="order_line and order_line.order_id">
                                                <a t-att-href="'/web#id=%s&amp;view_type=form&amp;model=sale.order' % order_line.order_id.id" class="btn btn-secondary">Back to Sales Order</a>
                                            </t>
                                            <t t-else="">
                                                <a href="/web" class="btn btn-secondary">Back to Dashboard</a>
                                            </t>    
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>

                        <!-- Bootstrap JS -->
                        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

                        <!-- SVG Components JavaScript -->
                        <script src="/canbrax_configmatrix/static/src/js/svg_components_bottom.js"></script>

                        <!-- Visibility Conditions JavaScript -->
                        <script type="text/javascript" t-att-src="'/canbrax_configmatrix/static/src/js/visibility_conditions.js?v=%s' % int(time.time())"/>
                        <!-- Add our new JavaScript files -->

                        <script type="text/javascript" t-att-src="'/canbrax_configmatrix/static/src/js/svg_conditional_layers.js?v=%s' % int(time.time())"/>
                        <script type="text/javascript" t-att-src="'/canbrax_configmatrix/static/src/js/components_list.js?v=%s' % int(time.time())"/>
                        <script type="text/javascript" t-att-src="'/canbrax_configmatrix/static/src/js/operation_costs_handler.js?v=%s' % int(time.time())"/>
                        <script type="text/javascript" t-att-src="'/canbrax_configmatrix/static/src/js/price_matrix_handler.js?v=%s' % int(time.time())"/>
                        <script type="text/javascript" t-att-src="'/canbrax_configmatrix/static/src/js/multi_components_helper.js?v=%s' % int(time.time())"/>
                        <script type="text/javascript" t-att-src="'/canbrax_configmatrix/static/src/js/mesh_hunt_panel.js?v=%s' % int(time.time())"/>
                        <script type="text/javascript" t-att-src="'/canbrax_configmatrix/static/src/js/sticky_panels.js?v=%s' % int(time.time())"/>

                        <!-- Load enhanced solutions after other configurator scripts -->
                        <script type="text/javascript" src="/canbrax_configmatrix/static/src/js/enhanced_configurator_solutions.js"></script>

                        <!-- This script must be loaded last to override all other scripts -->
                        <script type="text/javascript" t-att-src="'/canbrax_configmatrix/static/src/js/save_button.js?v=%s' % int(time.time())"/>

                        <!-- SIMPLE TOTAL PRICE UPDATER -->
                        <script type="text/javascript">
                        console.log('[TOTAL PRICE UPDATER] Script loaded');

                        function updateTotalPriceField() {
                            console.log('[TOTAL PRICE UPDATER] updateTotalPriceField called');

                            const totalPriceField = document.getElementById('total-price-field');
                            const componentPriceField = document.getElementById('field-price');
                            const matrixPriceField = document.getElementById('field-price-matrix');

                            if (!totalPriceField) {
                                console.error('[TOTAL PRICE UPDATER] total-price-field not found');
                                return;
                            }

                            // Get individual price components
                            const componentElement = document.getElementById('configuration-price');
                            const operationElement = document.getElementById('configuration-operation-costs');
                            const matrixElement = document.getElementById('configuration-price-matrix');

                            const componentPrice = componentElement ? (parseFloat(componentElement.textContent.replace('$', '').replace(',', '').trim()) || 0) : 0;
                            const operationPrice = operationElement ? (parseFloat(operationElement.textContent.replace('$', '').replace(',', '').trim()) || 0) : 0;
                            const matrixPrice = matrixElement ? (parseFloat(matrixElement.textContent.replace('$', '').replace(',', '').trim()) || 0) : 0;

                            // Calculate total
                            const totalPrice = componentPrice + operationPrice + matrixPrice;

                            // Update all hidden fields
                            totalPriceField.value = totalPrice.toString();

                            if (componentPriceField) {
                                componentPriceField.value = componentPrice.toString();
                                console.log('[TOTAL PRICE UPDATER] Set component price field to:', componentPrice);
                            }

                            if (matrixPriceField) {
                                matrixPriceField.value = matrixPrice.toString();
                                console.log('[TOTAL PRICE UPDATER] Set matrix price field to:', matrixPrice);
                            }

                            console.log('[TOTAL PRICE UPDATER] Updated fields - Components:', componentPrice, 'Operations:', operationPrice, 'Matrix:', matrixPrice, 'Total:', totalPrice);
                        }

                        // Update on page load
                        document.addEventListener('DOMContentLoaded', function() {
                            console.log('[TOTAL PRICE UPDATER] DOM loaded, setting up...');
                            setTimeout(updateTotalPriceField, 1000);
                            setTimeout(updateTotalPriceField, 3000);
                            setTimeout(updateTotalPriceField, 5000);
                        });

                        // Update when operation costs change
                        if (window.operationCostsHandler) {
                            const originalUpdateDisplay = window.operationCostsHandler.updateDisplay;
                            window.operationCostsHandler.updateDisplay = function() {
                                originalUpdateDisplay.call(this);
                                setTimeout(updateTotalPriceField, 100);
                            };
                        }

                        // Update before form submission
                        document.addEventListener('submit', function(e) {
                            if (e.target.id === 'config-form') {
                                console.log('[TOTAL PRICE UPDATER] Form submitting, updating total price...');
                                updateTotalPriceField();
                            }
                        });
                        </script>
                    </body>
                </html>
        </template>
    </data>
</odoo>
