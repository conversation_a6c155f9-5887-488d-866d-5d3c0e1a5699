<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Configuration Tree View -->
    <record id="view_config_matrix_configuration_tree" model="ir.ui.view">
        <field name="name">config.matrix.configuration.tree</field>
        <field name="model">config.matrix.configuration</field>
        <field name="arch" type="xml">
            <list string="Sale History">
                <field name="name"/>
                <field name="customer_id"/>
                <field name="product_id"/>
                <field name="template_id"/>
                <field name="sale_order_id"/>
                <field name="create_date"/>
                <field name="state"/>
                <field name="price" widget="monetary"/>
            </list>
        </field>
    </record>

    <!-- Configuration Form View -->
    <record id="view_config_matrix_configuration_form" model="ir.ui.view">
        <field name="name">config.matrix.configuration.form</field>
        <field name="model">config.matrix.configuration</field>
        <field name="arch" type="xml">
            <form string="Sale Configuration">
                <header>
                    <button name="generate_bom" string="Generate BOM" type="object"
                            class="btn-primary" invisible="bom_id"/>
                    <button name="calculate_price" string="Calculate Price" type="object"
                            class="btn-secondary"/>
                    <button name="apply_to_sale_order_line" string="Apply to Order Line" type="object"
                            class="btn-primary" invisible="not sale_order_line_id or state == 'applied'"/>
                    <button name="action_create_mesh_cut_operation" string="Create Mesh Operation" type="object"
                            class="btn-secondary" invisible="not requires_mesh or mesh_cut_operation_id"/>
                    <button name="action_view_mesh_cut_operation" string="View Mesh Operation" type="object"
                            class="btn-secondary" invisible="not mesh_cut_operation_id"/>
                    <button name="action_test_panel_calculation" string="Test Panel Calculation" type="object"
                            class="btn-secondary" groups="base.group_system"/>
                    <button name="action_create_mesh_operations" string="Create Mesh Operations" type="object"
                            class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,configured,applied"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1><field name="name"/></h1>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <group>
                                <field name="product_id" options="{'no_create': True}"/>
                                <field name="template_id" options="{'no_create': True}"/>
                                <field name="create_date" readonly="1"/>
                            </group>
                        </div>
                        <div class="col-lg-6">
                            <group>
                                <field name="sale_order_line_id" readonly="1"/>
                                <field name="sale_order_id" readonly="1"/>
                                <field name="price" widget="monetary"/>
                            </group>
                            <group string="Mesh Requirements" invisible="not requires_mesh">
                                <field name="requires_mesh" invisible="1"/>
                                <field name="mesh_width_required" readonly="1"/>
                                <field name="mesh_height_required" readonly="1"/>
                                <field name="mesh_series_required" readonly="1"/>
                                <field name="mesh_cut_operation_id" readonly="1"/>
                            </group>
                        </div>
                    </div>
                    <notebook>
                        <page string="Configuration Data" name="config_data">
                            <div class="alert alert-info" role="alert">
                                <i class="fa fa-info-circle me-2"></i>
                                <span>This is the raw configuration data in JSON format. It contains all the answers to configuration questions and calculated fields.</span>
                            </div>
                            <field name="config_data" widget="json_text"/>
                        </page>
                        <page string="Configuration Summary" name="summary">
                            <div class="alert alert-info" role="alert">
                                <i class="fa fa-info-circle me-2"></i>
                                <span>This is a human-readable summary of the configuration, showing all visible fields and their values.</span>
                            </div>
                            <field name="configuration_summary" readonly="1" class="bg-light p-3 border rounded" style="font-family: monospace;"/>
                        </page>
                        <page string="Bill of Materials" name="bom">
                            <div class="alert alert-info" role="alert">
                                <i class="fa fa-info-circle me-2"></i>
                                <span>This is the Bill of Materials generated from the configuration. It contains all components needed to build the configured product.</span>
                            </div>

                            <group>
                                <field name="bom_id" readonly="0" options="{'no_create': True}" string="Bill of Materials"/>
                            </group>

                            <!-- Show when no BOM exists -->
                            <div class="alert alert-warning" role="alert" invisible="bom_id">
                                <i class="fa fa-exclamation-triangle me-2"></i>
                                <span>No Bill of Materials has been generated yet. Click the button below to generate a BOM.</span>
                                <div class="mt-3">
                                    <button name="generate_bom" string="Generate BOM" type="object"
                                            class="btn btn-primary" icon="fa-cogs"/>
                                </div>
                            </div>

                            <!-- Show when BOM exists -->
                            <div invisible="not bom_id">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h3 class="m-0">BOM Actions</h3>
                                    <div>
                                        <button name="generate_bom" string="Regenerate BOM" type="object"
                                                class="btn btn-secondary" icon="fa-refresh"/>
                                        <button name="action_view_bom" string="View BOM" type="object"
                                                class="btn btn-primary" icon="fa-list-alt"/>
                                    </div>
                                </div>
                                <field name="bom_line_ids" readonly="1">
                                    <list string="BOM Components">
                                        <field name="product_id"/>
                                        <field name="product_qty"/>
                                        <field name="product_uom_id"/>
                                        <field name="company_id" groups="base.group_multi_company" optional="show"/>
                                    </list>
                                </field>
                            </div>
                        </page>

                        <page string="Mesh Operations" name="mesh_operations" invisible="not requires_mesh">
                            <div class="alert alert-info" role="alert" invisible="mesh_cut_operation_id">
                                <i class="fa fa-info-circle me-2"></i>
                                <span>This configuration requires mesh cutting. Create a mesh operation to find and assign the appropriate mesh material.</span>
                            </div>

                            <div invisible="not mesh_cut_operation_id">
                                <div class="row">
                                    <div class="col-md-6">
                                        <group string="Mesh Requirements">
                                            <field name="mesh_width_required" readonly="1"/>
                                            <field name="mesh_height_required" readonly="1"/>
                                            <field name="mesh_series_required" readonly="1"/>
                                        </group>
                                    </div>
                                    <div class="col-md-6">
                                        <group string="Cut Operation">
                                            <field name="mesh_cut_operation_id" readonly="1"/>
                                        </group>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button name="action_view_mesh_cut_operation" string="View Cut Operation Details"
                                            type="object" class="btn btn-primary" icon="fa-scissors"/>
                                </div>
                            </div>

                            <div invisible="mesh_cut_operation_id">
                                <div class="text-center mt-4">
                                    <button name="action_create_mesh_cut_operation" string="Create Mesh Cut Operation"
                                            type="object" class="btn btn-primary btn-lg" icon="fa-plus"/>
                                    <p class="text-muted mt-2">Create a mesh cutting operation to find the best available mesh for this configuration.</p>
                                </div>
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Configuration Search View -->
    <record id="view_config_matrix_configuration_search" model="ir.ui.view">
        <field name="name">config.matrix.configuration.search</field>
        <field name="model">config.matrix.configuration</field>
        <field name="arch" type="xml">
            <search string="Search Sale History">
                <field name="name"/>
                <field name="customer_id"/>
                <field name="product_id"/>
                <field name="template_id"/>
                <field name="sale_order_id"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Configured" name="configured" domain="[('state', '=', 'configured')]"/>
                <filter string="Applied" name="applied" domain="[('state', '=', 'applied')]"/>
                <separator/>
                <filter string="With BOM" name="with_bom" domain="[('bom_id', '!=', False)]"/>
                <filter string="With Order" name="with_order" domain="[('sale_order_id', '!=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Customer" name="group_by_customer" context="{'group_by': 'customer_id'}"/>
                    <filter string="Product" name="group_by_product" context="{'group_by': 'product_id'}"/>
                    <filter string="Template" name="group_by_template" context="{'group_by': 'template_id'}"/>
                    <filter string="Sales Order" name="group_by_order" context="{'group_by': 'sale_order_id'}"/>
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Configuration Action -->
    <record id="action_config_matrix_configuration" model="ir.actions.act_window">
        <field name="name">Sale History</field>
        <field name="res_model">config.matrix.configuration</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No sale configurations yet!
            </p>
            <p>
                Sale configurations are created when customers configure products through the configurator.
                These track the configuration history and can generate BOMs when needed.
            </p>
        </field>
    </record>

    <!-- Menu Item moved to menu_views.xml to fix loading order -->
</odoo>
