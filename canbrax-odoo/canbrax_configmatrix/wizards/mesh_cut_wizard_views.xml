<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Mesh Cut Wizard Views -->
    
    <!-- Wizard Form View -->
    <record id="view_mesh_cut_wizard_form" model="ir.ui.view">
        <field name="name">mesh.cut.wizard.form</field>
        <field name="model">mesh.cut.wizard</field>
        <field name="arch" type="xml">
            <form string="Create Mesh Cut Operation">
                <sheet>
                    <div class="oe_title">
                        <h1>Create Mesh Cut Operation</h1>
                        <p>Find the best mesh option for your requirements</p>
                    </div>
                    
                    <group>
                        <group string="Requirements">
                            <field name="name"/>
                            <field name="required_width"/>
                            <field name="required_height"/>
                            <field name="required_qty"/>
                            <field name="mesh_series"/>
                        </group>
                        <group string="Context" invisible="not sale_order_id and not config_id">
                            <field name="sale_order_id" readonly="1"/>
                            <field name="config_id" readonly="1"/>
                        </group>
                    </group>
                    
                    <!-- Search Results Section -->
                    <group string="Search Results" invisible="not search_performed">
                        <div class="alert alert-success" invisible="no_options_found or not search_message">
                            <p><i class="fa fa-check-circle"/> <field name="search_message" nolabel="1"/></p>
                        </div>
                        
                        <div class="alert alert-warning" invisible="not no_options_found">
                            <p><i class="fa fa-exclamation-triangle"/> <strong>No suitable mesh found</strong></p>
                            <field name="search_message" nolabel="1" widget="text"/>
                        </div>
                        
                        <!-- Available Options Table -->
                        <field name="mesh_options_ids" mode="list" nolabel="1"
                               invisible="no_options_found">
                            <list string="Available Mesh Options" create="false" delete="false">
                                <field name="source_type" string="Type"/>
                                <field name="source_product_id" string="Product"/>
                                <field name="actual_width" string="Width (mm)"/>
                                <field name="actual_height" string="Height (mm)"/>
                                <field name="efficiency" widget="percentage" string="Efficiency"/>
                                <field name="available_qty" string="Available Qty"/>
                                <field name="waste_width" string="Waste W (mm)"/>
                                <field name="waste_height" string="Waste H (mm)"/>
                                <field name="source_lot_id" string="Lot/Serial"
                                       invisible="source_type != 'unplanned'"/>
                            </list>
                        </field>

                        <group string="Selected Option" invisible="no_options_found">
                            <field name="selected_option_id"
                                   domain="[('wizard_id', '=', id)]"
                                   string="Choose Option"
                                   help="Select the mesh option you want to use"/>
                        </group>
                    </group>
                    
                    <!-- Hidden fields -->
                    <field name="search_performed" invisible="1"/>
                    <field name="no_options_found" invisible="1"/>
                </sheet>
                
                <footer>
                    <button name="action_search_mesh" type="object" 
                            string="Search for Mesh" class="btn-primary"
                            invisible="search_performed and not no_options_found"/>
                    <button name="action_search_mesh" type="object" 
                            string="Search Again" class="btn-secondary"
                            invisible="not search_performed or not no_options_found"/>
                    <button name="action_create_operation" type="object" 
                            string="Create Operation" class="btn-success"
                            invisible="not selected_option_id"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    
    <!-- Wizard Action -->
    <record id="action_mesh_cut_wizard" model="ir.actions.act_window">
        <field name="name">Create Mesh Cut Operation</field>
        <field name="res_model">mesh.cut.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_mesh_cut_wizard_form"/>
    </record>
    
    <!-- Mesh Cut Wizard Option Views -->
    
    <!-- Option Tree View -->
    <record id="view_mesh_cut_wizard_option_tree" model="ir.ui.view">
        <field name="name">mesh.cut.wizard.option.tree</field>
        <field name="model">mesh.cut.wizard.option</field>
        <field name="arch" type="xml">
            <list string="Mesh Options">
                <field name="display_name"/>
                <field name="source_type"/>
                <field name="source_product_id"/>
                <field name="actual_width"/>
                <field name="actual_height"/>
                <field name="efficiency" widget="percentage"/>
                <field name="available_qty"/>
                <field name="waste_width"/>
                <field name="waste_height"/>
            </list>
        </field>
    </record>
    
    <!-- Option Form View -->
    <record id="view_mesh_cut_wizard_option_form" model="ir.ui.view">
        <field name="name">mesh.cut.wizard.option.form</field>
        <field name="model">mesh.cut.wizard.option</field>
        <field name="arch" type="xml">
            <form string="Mesh Option">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="display_name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="Source">
                            <field name="source_type"/>
                            <field name="source_product_id"/>
                            <field name="source_lot_id" invisible="source_type != 'unplanned'"/>
                            <field name="available_qty"/>
                        </group>
                        <group string="Dimensions">
                            <field name="actual_width"/>
                            <field name="actual_height"/>
                            <field name="waste_width"/>
                            <field name="waste_height"/>
                            <field name="efficiency" widget="percentage"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
</odoo>
