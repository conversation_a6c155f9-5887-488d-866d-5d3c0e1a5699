# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class SampleProductsWizard(models.TransientModel):
    _name = 'sample.products.wizard'
    _description = 'Create Sample Products'

    product_type = fields.Selection([
        ('configurable', 'Configurable Products'),
        ('mesh', 'Mesh Products'),
        ('both', 'Both Types'),
    ], string='Product Type', required=True, default='both')
    
    create_templates = fields.Boolean('Create Configuration Templates', default=True)
    create_stock = fields.Boolean('Create Sample Stock', default=True)

    def action_create_sample_products(self):
        """Create sample products for testing"""
        self.ensure_one()
        
        created_items = []
        
        try:
            # Create product categories first
            categories = self._create_product_categories()
            created_items.extend(categories)
            
            if self.product_type in ['configurable', 'both']:
                # Create configurable products
                configurable_products = self._create_configurable_products(categories, created_items)
                created_items.extend(configurable_products)
                
                if self.create_templates:
                    # Create basic configuration templates
                    templates = self._create_configuration_templates(configurable_products)
                    created_items.extend(templates)
            
            if self.product_type in ['mesh', 'both']:
                # Create mesh products
                mesh_products = self._create_mesh_products(categories, created_items)
                created_items.extend(mesh_products)
                
                if self.create_stock:
                    # Create sample stock for mesh products
                    self._create_mesh_stock(mesh_products)
            
            # Show success message
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Sample Products Created',
                    'message': f'Successfully created {len(created_items)} sample items.',
                    'type': 'success',
                    'sticky': False,
                }
            }
            
        except Exception as e:
            raise UserError(_("Error creating sample products: %s") % str(e))

    def _create_product_categories(self):
        """Create sample product categories"""
        categories = []
        
        # Main category
        main_category = self.env['product.category'].create({
            'name': 'Sample Products',
            'parent_id': self.env.ref('product.product_category_all').id,
        })
        categories.append(main_category)
        
        # Configurable products category
        config_category = self.env['product.category'].create({
            'name': 'Configurable Products',
            'parent_id': main_category.id,
        })
        categories.append(config_category)
        
        # Mesh products category
        mesh_category = self.env['product.category'].create({
            'name': 'Mesh Products',
            'parent_id': main_category.id,
        })
        categories.append(mesh_category)
        
        return categories

    def _create_configurable_products(self, categories, created_items):
        """Create sample configurable products"""
        products = []
        config_category = categories[1]  # Configurable Products category
        
        sample_products = [
            {
                'name': 'Sample Door Frame - Configurable',
                'default_code': 'SAMPLE-DOOR-001',
                'list_price': 250.00,
                'standard_price': 180.00,
                'type': 'consu',
                'categ_id': config_category.id,
                'is_configurable': True,
            },
            {
                'name': 'Sample Window Frame - Configurable',
                'default_code': 'SAMPLE-WINDOW-001',
                'list_price': 180.00,
                'standard_price': 120.00,
                'type': 'consu',
                'categ_id': config_category.id,
                'is_configurable': True,
            },
            {
                'name': 'Sample Screen Door - Configurable',
                'default_code': 'SAMPLE-SCREEN-001',
                'list_price': 320.00,
                'standard_price': 220.00,
                'type': 'consu',
                'categ_id': config_category.id,
                'is_configurable': True,
            },
        ]
        
        for product_data in sample_products:
            # Check if product already exists
            existing_product = self.env['product.template'].search([
                ('default_code', '=', product_data['default_code'])
            ], limit=1)

            if existing_product:
                products.append(existing_product)
                created_items.append(f"Found existing: {existing_product.name}")
            else:
                product = self.env['product.template'].create(product_data)
                products.append(product)
                created_items.append(f"Created: {product.name}")
        
        return [f"Configurable Products: {len(products)} items"]

    def _create_mesh_products(self, categories, created_items):
        """Create sample mesh products"""
        products = []
        mesh_category = categories[2]  # Mesh Products category
        
        # Master sheets
        master_products = [
            {
                'name': 'Sample Saltwater Master Sheet 1100x620mm',
                'default_code': 'SWMMS1100620',
                'list_price': 150.00,
                'standard_price': 120.00,
                'type': 'consu',
                'categ_id': mesh_category.id,
                'is_mesh_product': True,
                'mesh_type': 'master',
                'mesh_series': 'saltwaterseries',
                'mesh_width': 1100,
                'mesh_height': 620,
                'tracking': 'none',
            },
            {
                'name': 'Sample Saltwater Master Sheet 1250x1000mm',
                'default_code': 'SWMMS12501000',
                'list_price': 200.00,
                'standard_price': 160.00,
                'type': 'consu',
                'categ_id': mesh_category.id,
                'is_mesh_product': True,
                'mesh_type': 'master',
                'mesh_series': 'saltwaterseries',
                'mesh_width': 1250,
                'mesh_height': 1000,
                'tracking': 'none',
            },
        ]
        
        # Planned off-cuts
        planned_products = [
            {
                'name': 'Sample Saltwater Off-cut 600x400mm',
                'default_code': 'SW600X400PO',
                'list_price': 80.00,
                'standard_price': 60.00,
                'type': 'consu',
                'categ_id': mesh_category.id,
                'is_mesh_product': True,
                'mesh_type': 'planned',
                'mesh_series': 'saltwaterseries',
                'mesh_width': 600,
                'mesh_height': 400,
                'tracking': 'none',
            },
            {
                'name': 'Sample Saltwater Off-cut 500x300mm',
                'default_code': 'SW500X300PO',
                'list_price': 60.00,
                'standard_price': 45.00,
                'type': 'consu',
                'categ_id': mesh_category.id,
                'is_mesh_product': True,
                'mesh_type': 'planned',
                'mesh_series': 'saltwaterseries',
                'mesh_width': 500,
                'mesh_height': 300,
                'tracking': 'none',
            },
        ]
        
        # Unplanned off-cut template
        unplanned_product = {
            'name': 'Sample Saltwater Unplanned Off-cut',
            'default_code': 'SW-UNPLANNED',
            'list_price': 40.00,
            'standard_price': 30.00,
            'type': 'consu',
            'categ_id': mesh_category.id,
            'is_mesh_product': True,
            'mesh_type': 'unplanned',
            'mesh_series': 'saltwaterseries',
            'tracking': 'lot',
        }
        
        all_mesh_products = master_products + planned_products + [unplanned_product]
        
        for product_data in all_mesh_products:
            # Check if product already exists
            existing_product = self.env['product.template'].search([
                ('default_code', '=', product_data['default_code'])
            ], limit=1)

            if existing_product:
                products.append(existing_product)
                created_items.append(f"Found existing: {existing_product.name}")
            else:
                product = self.env['product.template'].create(product_data)
                products.append(product)
                created_items.append(f"Created: {product.name}")
        
        return [f"Mesh Products: {len(products)} items"]

    def _create_configuration_templates(self, products):
        """Create basic configuration templates for products"""
        templates = []
        
        for product in products:
            template = self.env['config.matrix.template'].create({
                'name': f'Sample Template - {product.name}',
                'product_id': product.product_variant_ids[0].id,
                'description': f'Sample configuration template for {product.name}',
                'state': 'active',
            })
            templates.append(template)
        
        return templates

    def _create_mesh_stock(self, mesh_products):
        """Create sample stock for mesh products"""
        stock_location = self.env.ref('stock.stock_location_stock')
        
        for product in mesh_products:
            if product.mesh_type == 'master':
                qty = 5.0
            elif product.mesh_type == 'planned':
                qty = 10.0
            else:  # unplanned
                qty = 0.0  # Unplanned off-cuts are created as lots
                
            if qty > 0:
                self.env['stock.quant']._update_available_quantity(
                    product.product_variant_ids[0],
                    stock_location,
                    qty
                )
