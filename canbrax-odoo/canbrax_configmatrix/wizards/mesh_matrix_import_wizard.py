# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import base64
import json
import logging

_logger = logging.getLogger(__name__)

try:
    import openpyxl
    from openpyxl import load_workbook
except ImportError:
    openpyxl = None
    _logger.warning("openpyxl not available. Excel import functionality will be limited.")

class MeshMatrixImportWizard(models.TransientModel):
    _name = 'mesh.matrix.import.wizard'
    _description = 'Mesh Cutting Matrix Import Wizard'
    
    name = fields.Char('Import Name', default='Mesh Matrix Import')
    
    # File upload
    excel_file = fields.Binary('Excel File', required=True,
                              help="Upload the Excel file containing cutting matrix data")
    filename = fields.Char('Filename')
    
    # Import options
    mesh_series = fields.Selection([
        ('saltwaterseries', 'SaltwaterSeries'),
        ('xceed', 'Xceed'),
        ('basix', 'BasiX'),
        ('diamond_7mm', '7mm Diamond Grille'),
        ('flyscreen', 'Flyscreen'),
        ('petscreen', 'Petscreen'),
        ('sandfly', 'Sandfly Mesh'),
        ('bal_insect', 'BAL Rated Insect Screen'),
        ('diamond_small', 'Small Diamond Grille'),
        ('colonial_castings', 'Colonial Castings Design'),
    ], string='Mesh Series', required=True, default='saltwaterseries')
    
    sheet_name = fields.Char('Sheet Name', default='Sheet1',
                           help="Name of the Excel sheet to import from")
    
    # Import mode
    import_mode = fields.Selection([
        ('create_new', 'Create New Matrix'),
        ('update_existing', 'Update Existing Matrix'),
    ], string='Import Mode', default='create_new', required=True)
    
    existing_matrix_id = fields.Many2one('mesh.cut.matrix', 'Existing Matrix',
                                       domain="[('mesh_series', '=', mesh_series)]")
    
    # Import results
    import_performed = fields.Boolean('Import Performed', default=False)
    import_log = fields.Text('Import Log', readonly=True)
    created_matrix_id = fields.Many2one('mesh.cut.matrix', 'Created Matrix', readonly=True)
    
    @api.onchange('import_mode')
    def _onchange_import_mode(self):
        if self.import_mode == 'create_new':
            self.existing_matrix_id = False
    
    def action_import_matrix(self):
        """Import cutting matrix from Excel file"""
        self.ensure_one()
        
        if not openpyxl:
            raise UserError(_("openpyxl library is required for Excel import. Please install it."))
        
        if not self.excel_file:
            raise UserError(_("Please upload an Excel file."))
        
        if self.import_mode == 'update_existing' and not self.existing_matrix_id:
            raise UserError(_("Please select an existing matrix to update."))
        
        try:
            # Decode the file
            file_data = base64.b64decode(self.excel_file)
            
            # Load workbook
            workbook = load_workbook(filename=None, data=file_data, read_only=True)
            
            # Get the specified sheet
            if self.sheet_name in workbook.sheetnames:
                worksheet = workbook[self.sheet_name]
            else:
                # Try to find a sheet that looks like cutting matrix data
                worksheet = workbook.active
                _logger.info(f"Sheet '{self.sheet_name}' not found, using active sheet: {worksheet.title}")
            
            # Parse the Excel data
            matrix_data = self._parse_excel_data(worksheet)
            
            # Create or update matrix
            if self.import_mode == 'create_new':
                matrix = self._create_matrix(matrix_data)
                self.created_matrix_id = matrix.id
                log_message = f"Successfully created new cutting matrix: {matrix.name}"
            else:
                matrix = self._update_matrix(self.existing_matrix_id, matrix_data)
                log_message = f"Successfully updated cutting matrix: {matrix.name}"
            
            self.import_performed = True
            self.import_log = log_message + "\n\n" + matrix_data.get('import_summary', '')
            
            workbook.close()
            
            return self._return_wizard()
            
        except Exception as e:
            error_msg = f"Error importing Excel file: {str(e)}"
            _logger.error(error_msg, exc_info=True)
            self.import_log = error_msg
            raise UserError(_(error_msg))
    
    def _parse_excel_data(self, worksheet):
        """Parse Excel worksheet to extract cutting matrix data"""
        matrix_data = {
            'master_sheets': {},
            'cut_plans': [],
            'import_summary': ''
        }
        
        # This is a simplified parser - you'll need to customize this based on your Excel format
        # Looking at your files: SWS1100x620MS.xlsx, SWS1250x1000MS.xlsx, etc.
        
        try:
            # Try to detect master sheet dimensions from filename or sheet content
            sheet_title = worksheet.title or ''
            
            # Look for dimension patterns like "1100x620" in the sheet
            import re
            dimension_match = re.search(r'(\d+)x(\d+)', sheet_title)
            
            if dimension_match:
                width = int(dimension_match.group(1))
                height = int(dimension_match.group(2))
                
                matrix_data['master_sheets'][f"{width}x{height}"] = {
                    'width': width,
                    'height': height,
                    'source_sheet': sheet_title
                }
                
                # Create a basic cut plan
                cut_plan = {
                    'master_width': width,
                    'master_height': height,
                    'byproducts': []
                }
                
                # Try to extract cutting information from the sheet
                # This would need to be customized based on your Excel format
                for row in worksheet.iter_rows(min_row=1, max_row=50, values_only=True):
                    if row and any(cell for cell in row if cell):
                        # Look for dimension patterns in cells
                        for cell in row:
                            if isinstance(cell, str):
                                cell_match = re.search(r'(\d+)\s*x\s*(\d+)', str(cell))
                                if cell_match:
                                    byproduct_width = int(cell_match.group(1))
                                    byproduct_height = int(cell_match.group(2))
                                    
                                    # Only add if it's smaller than master sheet
                                    if byproduct_width < width and byproduct_height < height:
                                        cut_plan['byproducts'].append({
                                            'width': byproduct_width,
                                            'height': byproduct_height,
                                            'quantity': 1
                                        })
                
                matrix_data['cut_plans'].append(cut_plan)
            
            matrix_data['import_summary'] = f"Parsed {len(matrix_data['cut_plans'])} cut plans from Excel file"
            
        except Exception as e:
            _logger.warning(f"Error parsing Excel data: {e}")
            matrix_data['import_summary'] = f"Warning: Limited data extracted due to parsing error: {e}"
        
        return matrix_data
    
    def _create_matrix(self, matrix_data):
        """Create new cutting matrix from parsed data"""
        matrix_name = f"{self.mesh_series.title()} Series Cutting Matrix"
        if self.filename:
            matrix_name += f" (from {self.filename})"
        
        matrix = self.env['mesh.cut.matrix'].create({
            'name': matrix_name,
            'mesh_series': self.mesh_series,
            'description': f"Imported from Excel file: {self.filename or 'Unknown'}",
            'cut_rules': json.dumps(matrix_data),
        })
        
        # Create cut plans
        for plan_data in matrix_data.get('cut_plans', []):
            cut_plan = self.env['mesh.cut.plan'].create({
                'matrix_id': matrix.id,
                'master_width': plan_data['master_width'],
                'master_height': plan_data['master_height'],
                'cut_instructions': f"Cut plan for {plan_data['master_width']}x{plan_data['master_height']}mm master sheet",
            })
            
            # Create byproducts
            for byproduct_data in plan_data.get('byproducts', []):
                self.env['mesh.cut.byproduct'].create({
                    'cut_plan_id': cut_plan.id,
                    'width': byproduct_data['width'],
                    'height': byproduct_data['height'],
                    'quantity': byproduct_data.get('quantity', 1),
                })
        
        return matrix
    
    def _update_matrix(self, matrix, matrix_data):
        """Update existing cutting matrix with parsed data"""
        # Update the cut rules
        existing_rules = json.loads(matrix.cut_rules or '{}')
        existing_rules.update(matrix_data)
        
        matrix.write({
            'cut_rules': json.dumps(existing_rules),
            'description': (matrix.description or '') + f"\nUpdated from Excel file: {self.filename or 'Unknown'}"
        })
        
        return matrix
    
    def action_view_matrix(self):
        """View the created/updated matrix"""
        self.ensure_one()
        
        matrix_id = self.created_matrix_id.id if self.created_matrix_id else self.existing_matrix_id.id
        
        return {
            'name': _('Cutting Matrix'),
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.matrix',
            'view_mode': 'form',
            'res_id': matrix_id,
            'target': 'current',
        }
    
    def _return_wizard(self):
        """Return action to keep wizard open"""
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.matrix.import.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
        }
