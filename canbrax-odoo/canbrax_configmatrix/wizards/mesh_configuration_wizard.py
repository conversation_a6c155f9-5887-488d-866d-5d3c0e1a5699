# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from ..models.mesh_constants import MESH_SERIES_SELECTION, DEFAULT_MESH_SERIES
from odoo.exceptions import UserError

class MeshConfigurationWizard(models.TransientModel):
    _name = 'mesh.configuration.wizard'
    _description = 'Mesh Configuration Setup Wizard'
    
    template_id = fields.Many2one('config.matrix.template', 'Configuration Template', required=True)
    
    # Mesh detection rules
    mesh_detection_method = fields.Selection([
        ('field_names', 'Detect by Field Names'),
        ('field_values', 'Detect by Field Values'),
        ('manual', 'Manual Configuration'),
    ], string='Detection Method', default='field_names', required=True)
    
    # Field name patterns for detection
    mesh_field_patterns = fields.Text('Field Name Patterns',
        default='mesh,screen,grill,flyscreen,security',
        help="Comma-separated list of field name patterns that indicate mesh requirement")
    
    # Value patterns for detection  
    mesh_value_patterns = fields.Text('Value Patterns',
        default='mesh,screen,grill,saltwater,diamond,flyscreen',
        help="Comma-separated list of field value patterns that indicate mesh requirement")
    
    # Dimension field mapping
    width_field_name = fields.Char('Width Field Name', default='width',
        help="Name of the field that contains the width dimension")
    height_field_name = fields.Char('Height Field Name', default='height', 
        help="Name of the field that contains the height dimension")
    
    # Mesh series mapping
    series_mapping_ids = fields.One2many('mesh.series.mapping', 'wizard_id', 'Series Mapping')
    
    # Preview
    preview_text = fields.Text('Detection Preview', readonly=True)
    
    @api.onchange('template_id', 'mesh_detection_method', 'mesh_field_patterns', 'mesh_value_patterns')
    def _onchange_preview(self):
        """Generate preview of mesh detection logic"""
        if not self.template_id:
            self.preview_text = "Select a template to see preview"
            return
            
        preview_lines = []
        preview_lines.append(f"Template: {self.template_id.name}")
        preview_lines.append(f"Detection Method: {dict(self._fields['mesh_detection_method'].selection)[self.mesh_detection_method]}")
        
        if self.mesh_detection_method == 'field_names':
            patterns = [p.strip() for p in (self.mesh_field_patterns or '').split(',') if p.strip()]
            preview_lines.append(f"Will detect mesh requirement if field names contain: {', '.join(patterns)}")
            
        elif self.mesh_detection_method == 'field_values':
            patterns = [p.strip() for p in (self.mesh_value_patterns or '').split(',') if p.strip()]
            preview_lines.append(f"Will detect mesh requirement if field values contain: {', '.join(patterns)}")
            
        preview_lines.append(f"Width will be taken from field: {self.width_field_name or 'width'}")
        preview_lines.append(f"Height will be taken from field: {self.height_field_name or 'height'}")
        
        self.preview_text = '\n'.join(preview_lines)
    
    def action_apply_configuration(self):
        """Apply the mesh configuration to the template"""
        self.ensure_one()
        
        if not self.template_id:
            raise UserError(_("Please select a configuration template."))
        
        # Create or update mesh configuration for this template
        mesh_config = self.env['config.matrix.mesh.config'].search([
            ('template_id', '=', self.template_id.id)
        ], limit=1)
        
        config_vals = {
            'template_id': self.template_id.id,
            'detection_method': self.mesh_detection_method,
            'field_name_patterns': self.mesh_field_patterns,
            'field_value_patterns': self.mesh_value_patterns,
            'width_field_name': self.width_field_name,
            'height_field_name': self.height_field_name,
        }
        
        if mesh_config:
            mesh_config.write(config_vals)
        else:
            mesh_config = self.env['config.matrix.mesh.config'].create(config_vals)
        
        # Create series mappings
        mesh_config.series_mapping_ids.unlink()
        for mapping in self.series_mapping_ids:
            self.env['config.matrix.mesh.series.mapping'].create({
                'mesh_config_id': mesh_config.id,
                'field_value_pattern': mapping.field_value_pattern,
                'mesh_series': mapping.mesh_series,
            })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Mesh configuration has been applied to the template.'),
                'type': 'success',
            }
        }


class MeshSeriesMapping(models.TransientModel):
    _name = 'mesh.series.mapping'
    _description = 'Mesh Series Mapping'
    
    wizard_id = fields.Many2one('mesh.configuration.wizard', 'Wizard', ondelete='cascade')
    field_value_pattern = fields.Char('Field Value Pattern', required=True,
        help="Pattern to match in field values (e.g., 'saltwater', 'diamond')")
    mesh_series = fields.Selection( MESH_SERIES_SELECTION, string='Mesh Series', required=True, default=DEFAULT_MESH_SERIES)


class ConfigMatrixMeshConfig(models.Model):
    _name = 'config.matrix.mesh.config'
    _description = 'Mesh Configuration for Templates'
    
    template_id = fields.Many2one('config.matrix.template', 'Template', required=True, ondelete='cascade')
    
    # Detection configuration
    detection_method = fields.Selection([
        ('field_names', 'Detect by Field Names'),
        ('field_values', 'Detect by Field Values'),
        ('manual', 'Manual Configuration'),
    ], string='Detection Method', required=True)
    
    field_name_patterns = fields.Text('Field Name Patterns')
    field_value_patterns = fields.Text('Field Value Patterns')
    
    # Dimension mapping
    width_field_name = fields.Char('Width Field Name', default='width')
    height_field_name = fields.Char('Height Field Name', default='height')
    
    # Series mappings
    series_mapping_ids = fields.One2many('config.matrix.mesh.series.mapping', 'mesh_config_id', 'Series Mappings')
    
    active = fields.Boolean('Active', default=True)


class ConfigMatrixMeshSeriesMapping(models.Model):
    _name = 'config.matrix.mesh.series.mapping'
    _description = 'Mesh Series Mapping Configuration'
    
    mesh_config_id = fields.Many2one('config.matrix.mesh.config', 'Mesh Config', required=True, ondelete='cascade')
    field_value_pattern = fields.Char('Field Value Pattern', required=True)
    mesh_series = fields.Selection(MESH_SERIES_SELECTION, string='Mesh Series', required=True, default=DEFAULT_MESH_SERIES)
