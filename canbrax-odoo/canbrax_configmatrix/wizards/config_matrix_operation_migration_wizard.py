# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class ConfigMatrixOperationMigrationWizard(models.TransientModel):
    _name = 'config.matrix.operation.migration.wizard'
    _description = 'Operation Template Duration/Cost Migration Wizard'

    def _get_templates_needing_migration(self):
        """Get operation templates that have duration_formula but no cost_formula"""
        return self.env['config.matrix.operation.template'].search([
            ('duration_formula', '!=', False),
            ('cost_formula', '=', False)
        ])

    template_ids = fields.Many2many(
        'config.matrix.operation.template',
        'operation_migration_template_rel',
        'wizard_id',
        'template_id',
        string='Templates to Migrate',
        default=_get_templates_needing_migration,
        help='Operation templates that need duration/cost formula migration'
    )
    
    migration_mode = fields.Selection([
        ('auto', 'Automatic Migration'),
        ('manual', 'Manual Review'),
    ], string='Migration Mode', default='auto',
       help='Auto: Automatically split formulas. Manual: Review each template individually.')

    def action_migrate_templates(self):
        """Migrate the selected operation templates"""
        self.ensure_one()
        
        if not self.template_ids:
            raise UserError(_("No templates selected for migration"))
        
        migrated_count = 0
        
        for template in self.template_ids:
            if self.migration_mode == 'auto':
                self._auto_migrate_template(template)
                migrated_count += 1
            else:
                # For manual mode, open each template for review
                return self._open_template_for_review(template)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Migration Complete'),
                'message': _('Successfully migrated %d operation templates') % migrated_count,
                'type': 'success',
                'sticky': False,
            }
        }

    def _auto_migrate_template(self, template):
        """Automatically migrate a template by analyzing its current formula"""
        current_formula = template.duration_formula
        
        if not current_formula:
            return
        
        _logger.info(f"Migrating template '{template.name}' with formula: {current_formula}")
        
        # Analyze the formula to extract duration and cost parts
        duration_formula, cost_formula = self._split_formula(current_formula)
        
        # Update the template
        template.write({
            'duration_formula': duration_formula,
            'cost_formula': cost_formula,
        })
        
        _logger.info(f"Migrated '{template.name}': duration='{duration_formula}', cost='{cost_formula}'")

    def _split_formula(self, formula):
        """
        Split a formula into duration and cost components
        
        Args:
            formula (str): Original formula like "get_fixed_price('RecMatlT')*2*get_fixed_price('WageP')"
            
        Returns:
            tuple: (duration_formula, cost_formula)
        """
        if not formula:
            return ('', '')
        
        # Common patterns to detect
        if '*' in formula and 'get_fixed_price' in formula:
            # Pattern: get_fixed_price('RecMatlT')*2*get_fixed_price('WageP')
            # Duration: get_fixed_price('RecMatlT')
            # Cost: full formula
            
            parts = formula.split('*')
            if len(parts) >= 2 and 'get_fixed_price' in parts[0]:
                duration_part = parts[0].strip()
                cost_part = formula  # Keep full formula for cost
                return (duration_part, cost_part)
        
        # If we can't split it intelligently, assume it's a duration formula
        # and leave cost empty for manual entry
        return (formula, '')

    def _open_template_for_review(self, template):
        """Open a template for manual review"""
        return {
            'name': _('Review Template: %s') % template.name,
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.operation.template',
            'res_id': template.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'migration_mode': True,
                'original_formula': template.duration_formula,
            }
        }

    def action_preview_migration(self):
        """Preview what the migration will do"""
        self.ensure_one()
        
        preview_lines = []
        for template in self.template_ids:
            current_formula = template.duration_formula
            duration_formula, cost_formula = self._split_formula(current_formula)
            
            preview_lines.append({
                'template_name': template.name,
                'current_formula': current_formula,
                'new_duration_formula': duration_formula,
                'new_cost_formula': cost_formula,
            })
        
        # Create a simple preview view
        preview_text = "Migration Preview:\n\n"
        for line in preview_lines:
            preview_text += f"Template: {line['template_name']}\n"
            preview_text += f"  Current: {line['current_formula']}\n"
            preview_text += f"  → Duration: {line['new_duration_formula']}\n"
            preview_text += f"  → Cost: {line['new_cost_formula']}\n\n"
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Migration Preview'),
                'message': preview_text,
                'type': 'info',
                'sticky': True,
            }
        }
