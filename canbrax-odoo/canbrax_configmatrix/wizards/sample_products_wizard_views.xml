<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Sample Products Wizard Form -->
    <record id="view_sample_products_wizard_form" model="ir.ui.view">
        <field name="name">sample.products.wizard.form</field>
        <field name="model">sample.products.wizard</field>
        <field name="arch" type="xml">
            <form string="Create Sample Products">
                <sheet>
                    <div class="oe_title">
                        <h1>Create Sample Products</h1>
                        <p>Generate sample products for testing the configuration system.</p>
                    </div>
                    
                    <group>
                        <group string="Product Types">
                            <field name="product_type"/>
                        </group>
                        <group string="Additional Options">
                            <field name="create_templates" invisible="product_type == 'mesh'"/>
                            <field name="create_stock" invisible="product_type == 'configurable'"/>
                        </group>
                    </group>
                    
                    <div class="alert alert-info">
                        <p><strong>This will create the following sample products:</strong></p>
                        
                        <div invisible="product_type == 'mesh'">
                            <h5>Configurable Products:</h5>
                            <ul>
                                <li>Sample Door Frame - Configurable (SAMPLE-DOOR-001)</li>
                                <li>Sample Window Frame - Configurable (SAMPLE-WINDOW-001)</li>
                                <li>Sample Screen Door - Configurable (SAMPLE-SCREEN-001)</li>
                            </ul>
                        </div>
                        
                        <div invisible="product_type == 'configurable'">
                            <h5>Mesh Products:</h5>
                            <ul>
                                <li><strong>Master Sheets:</strong> 1100x620mm, 1250x1000mm</li>
                                <li><strong>Planned Off-cuts:</strong> 600x400mm, 500x300mm</li>
                                <li><strong>Unplanned Off-cut Template:</strong> For lot tracking</li>
                            </ul>
                        </div>
                        
                        <p><strong>Note:</strong> This will also create appropriate product categories and organize the products.</p>
                    </div>
                </sheet>
                
                <footer>
                    <button name="action_create_sample_products" type="object"
                            string="Create Sample Products" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action for Sample Products Wizard -->
    <record id="action_sample_products_wizard" model="ir.actions.act_window">
        <field name="name">Create Sample Products</field>
        <field name="res_model">sample.products.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Menu item under DEV -->
    <menuitem id="menu_sample_products_wizard"
              name="Create Sample Products"
              parent="canbrax_configmatrix.menu_config_matrix_dev"
              action="action_sample_products_wizard"
              sequence="5"/>
</odoo>
