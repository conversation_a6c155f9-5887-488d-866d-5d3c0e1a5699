# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class MeshSampleStockWizard(models.TransientModel):
    _name = 'mesh.sample.stock.wizard'
    _description = 'Create Sample Stock for Mesh Products'

    def action_create_sample_stock(self):
        """Create sample stock for mesh products"""
        try:
            # Get stock location
            stock_location = self.env.ref('stock.stock_location_stock')

            created_count = 0
            errors = []

            # Create stock for master sheets
            master_products = [
                ('canbrax_configmatrix.product_saltwater_master_1100x620', 5.0),
                ('canbrax_configmatrix.product_saltwater_master_1250x1000', 3.0),
            ]

            for product_ref, qty in master_products:
                try:
                    product_template = self.env.ref(product_ref, raise_if_not_found=False)
                    if not product_template:
                        errors.append(f"Product template not found: {product_ref}")
                        continue

                    # In Odoo 18, products need to be 'consu' (goods) with tracking enabled to be stockable
                    if product_template.type != 'consu':
                        errors.append(f"Product {product_template.name} is not goods type (type: {product_template.type}). Use 'Fix Product Types' button first.")
                        continue

                    # Check if tracking is enabled (required for stockable products in Odoo 18)
                    if product_template.tracking == 'none' and product_template.mesh_type != 'unplanned':
                        # For master and planned products, we need to enable tracking
                        product_template.write({'tracking': 'none'})  # This enables inventory tracking

                    # Get the product variant
                    product_variant = product_template.product_variant_ids[0] if product_template.product_variant_ids else None
                    if not product_variant:
                        errors.append(f"No product variant found for {product_template.name}")
                        continue

                    self.env['stock.quant']._update_available_quantity(
                        product_variant,
                        stock_location,
                        qty
                    )
                    created_count += 1
                except Exception as e:
                    errors.append(f"Error creating stock for {product_ref}: {str(e)}")
                    continue

            # Create stock for planned off-cuts
            planned_products = [
                ('canbrax_configmatrix.product_saltwater_offcut_600x400', 8.0),
                ('canbrax_configmatrix.product_saltwater_offcut_500x300', 12.0),
            ]

            for product_ref, qty in planned_products:
                try:
                    product_template = self.env.ref(product_ref, raise_if_not_found=False)
                    if not product_template:
                        errors.append(f"Product template not found: {product_ref}")
                        continue

                    # In Odoo 18, products need to be 'consu' (goods) to be stockable
                    if product_template.type != 'consu':
                        errors.append(f"Product {product_template.name} is not goods type (type: {product_template.type}). Use 'Fix Product Types' button first.")
                        continue

                    # Get the product variant
                    product_variant = product_template.product_variant_ids[0] if product_template.product_variant_ids else None
                    if not product_variant:
                        errors.append(f"No product variant found for {product_template.name}")
                        continue

                    self.env['stock.quant']._update_available_quantity(
                        product_variant,
                        stock_location,
                        qty
                    )
                    created_count += 1
                except Exception as e:
                    errors.append(f"Error creating stock for {product_ref}: {str(e)}")
                    continue

            # Create stock for unplanned off-cuts with lots
            unplanned_lots = [
                ('canbrax_configmatrix.lot_unplanned_450x380', 1.0),
                ('canbrax_configmatrix.lot_unplanned_320x280', 1.0),
                ('canbrax_configmatrix.lot_unplanned_500x400', 1.0),
                ('canbrax_configmatrix.lot_unplanned_600x350', 1.0),
            ]

            for lot_ref, qty in unplanned_lots:
                try:
                    lot = self.env.ref(lot_ref, raise_if_not_found=False)
                    if not lot:
                        errors.append(f"Lot not found: {lot_ref}")
                        continue

                    if not lot.product_id or lot.product_id.type != 'consu':
                        errors.append(f"Lot {lot.name} has no goods product")
                        continue

                    # Get the product variant
                    product_variant = lot.product_id.product_variant_ids[0] if lot.product_id.product_variant_ids else None
                    if not product_variant:
                        errors.append(f"No product variant found for lot {lot.name}")
                        continue

                    self.env['stock.quant']._update_available_quantity(
                        product_variant,
                        stock_location,
                        qty,
                        lot_id=lot
                    )
                    created_count += 1
                except Exception as e:
                    errors.append(f"Error creating stock for lot {lot_ref}: {str(e)}")
                    continue

            # Prepare result message
            if created_count > 0:
                message = _('Created stock for %d mesh products') % created_count
                if errors:
                    message += _('\n\nWarnings:\n') + '\n'.join(errors[:5])  # Show first 5 errors
                notification_type = 'success'
                title = _('Sample Stock Created')
            else:
                message = _('No stock was created. Errors:\n') + '\n'.join(errors[:10])  # Show first 10 errors
                notification_type = 'warning'
                title = _('Stock Creation Failed')

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': title,
                    'message': message,
                    'type': notification_type,
                }
            }

        except Exception as e:
            raise UserError(_("Failed to create sample stock: %s") % str(e))

    def action_fix_product_types(self):
        """Fix product types to be stockable"""
        try:
            fixed_count = 0
            errors = []

            # Fix product types
            product_refs = [
                'canbrax_configmatrix.product_saltwater_master_1100x620',
                'canbrax_configmatrix.product_saltwater_master_1250x1000',
                'canbrax_configmatrix.product_saltwater_offcut_600x400',
                'canbrax_configmatrix.product_saltwater_offcut_500x300',
                'canbrax_configmatrix.product_saltwater_unplanned_offcut',
            ]

            for product_ref in product_refs:
                try:
                    product_template = self.env.ref(product_ref, raise_if_not_found=False)
                    if product_template:
                        updates = {}
                        if product_template.type != 'consu':
                            updates['type'] = 'consu'

                        # Ensure tracking is properly set for stockable products
                        if product_template.mesh_type == 'unplanned' and product_template.tracking != 'lot':
                            updates['tracking'] = 'lot'
                        elif product_template.mesh_type in ('master', 'planned') and product_template.tracking not in ('none', 'lot', 'serial'):
                            updates['tracking'] = 'none'  # Enable basic inventory tracking

                        if updates:
                            product_template.write(updates)
                            fixed_count += 1
                    else:
                        errors.append(f"Product not found: {product_ref}")
                except Exception as e:
                    errors.append(f"Error fixing {product_ref}: {str(e)}")

            message = _('Fixed %d products to be stockable') % fixed_count
            if errors:
                message += _('\n\nErrors:\n') + '\n'.join(errors[:5])

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Product Types Fixed'),
                    'message': message,
                    'type': 'success',
                }
            }

        except Exception as e:
            raise UserError(_("Failed to fix product types: %s") % str(e))

    def action_debug_products(self):
        """Debug method to check what products exist"""
        debug_info = []

        # Check if products exist
        product_refs = [
            'canbrax_configmatrix.product_saltwater_master_1100x620',
            'canbrax_configmatrix.product_saltwater_master_1250x1000',
            'canbrax_configmatrix.product_saltwater_offcut_600x400',
            'canbrax_configmatrix.product_saltwater_offcut_500x300',
            'canbrax_configmatrix.product_saltwater_unplanned_offcut',
        ]

        for ref in product_refs:
            try:
                product = self.env.ref(ref, raise_if_not_found=False)
                if product:
                    debug_info.append(f"✓ {ref}: {product.name} (type: {product.type}, variants: {len(product.product_variant_ids)})")
                else:
                    debug_info.append(f"✗ {ref}: NOT FOUND")
            except Exception as e:
                debug_info.append(f"✗ {ref}: ERROR - {str(e)}")

        # Check lots
        lot_refs = [
            'canbrax_configmatrix.lot_unplanned_450x380',
            'canbrax_configmatrix.lot_unplanned_320x280',
            'canbrax_configmatrix.lot_unplanned_500x400',
            'canbrax_configmatrix.lot_unplanned_600x350',
        ]

        debug_info.append("\nLots:")
        for ref in lot_refs:
            try:
                lot = self.env.ref(ref, raise_if_not_found=False)
                if lot:
                    debug_info.append(f"✓ {ref}: {lot.name} (product: {lot.product_id.name if lot.product_id else 'None'})")
                else:
                    debug_info.append(f"✗ {ref}: NOT FOUND")
            except Exception as e:
                debug_info.append(f"✗ {ref}: ERROR - {str(e)}")

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Debug Info'),
                'message': '\n'.join(debug_info),
                'type': 'info',
            }
        }
