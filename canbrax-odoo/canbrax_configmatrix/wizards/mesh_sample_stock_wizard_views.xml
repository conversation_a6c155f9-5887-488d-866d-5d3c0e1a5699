<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Mesh Sample Stock Wizard -->
    <record id="view_mesh_sample_stock_wizard_form" model="ir.ui.view">
        <field name="name">mesh.sample.stock.wizard.form</field>
        <field name="model">mesh.sample.stock.wizard</field>
        <field name="arch" type="xml">
            <form string="Create Sample Stock">
                <div class="oe_title">
                    <h1>Create Sample Stock for Mesh Products</h1>
                </div>
                <group>
                    <div class="alert alert-info" role="alert">
                        <p>This will create sample stock quantities for mesh products:</p>
                        <ul>
                            <li><strong>Master Sheets:</strong> 1100x620mm (5 units), 1250x1000mm (3 units)</li>
                            <li><strong>Planned Off-cuts:</strong> 600x400mm (8 units), 500x300mm (12 units)</li>
                            <li><strong>Unplanned Off-cuts:</strong> 4 different lots with unique dimensions</li>
                        </ul>
                        <p>This is useful for testing the "Find Mesh" functionality.</p>
                    </div>
                </group>
                <footer>
                    <button name="action_create_sample_stock" type="object" string="Create Sample Stock" class="btn-primary"/>
                    <button name="action_fix_product_types" type="object" string="Fix Product Types" class="btn-warning"/>
                    <button name="action_debug_products" type="object" string="Debug Products" class="btn-secondary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_mesh_sample_stock_wizard" model="ir.actions.act_window">
        <field name="name">Create Sample Stock</field>
        <field name="res_model">mesh.sample.stock.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_mesh_sample_stock_wizard"
              name="Create Sample Stock"
              parent="canbrax_configmatrix.menu_config_matrix_dev"
              action="action_mesh_sample_stock_wizard"
              sequence="10"/>
</odoo>
