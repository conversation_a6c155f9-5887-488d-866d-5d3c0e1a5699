# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from ..models.mesh_constants import MESH_SERIES_SELECTION

class MeshCutWizard(models.TransientModel):
    _name = 'mesh.cut.wizard'
    _description = 'Mesh Cut Operation Wizard'
    
    # Basic requirements
    name = fields.Char('Operation Name', required=True, default=lambda self: _('New Mesh Cut Operation'))
    required_width = fields.Float('Required Width (mm)', required=True)
    required_height = fields.Float('Required Height (mm)', required=True)
    required_qty = fields.Float('Required Quantity', default=1.0, required=True)
    mesh_series = fields.Selection(MESH_SERIES_SELECTION, string='Mesh Series', required=True)

    
    # Context fields
    sale_order_id = fields.Many2one('sale.order', 'Sale Order')
    config_id = fields.Many2one('config.matrix.configuration', 'Configuration')
    
    # Search results
    search_performed = fields.Boolean('Search Performed', default=False)
    mesh_options_ids = fields.One2many('mesh.cut.wizard.option', 'wizard_id', 'Available Options')
    selected_option_id = fields.Many2one('mesh.cut.wizard.option', 'Selected Option')
    
    # Results display
    no_options_found = fields.Boolean('No Options Found', default=False)
    search_message = fields.Text('Search Message', readonly=True)
    
    @api.constrains('required_width', 'required_height', 'required_qty')
    def _check_positive_values(self):
        for wizard in self:
            if wizard.required_width <= 0:
                raise ValidationError(_("Required width must be positive."))
            if wizard.required_height <= 0:
                raise ValidationError(_("Required height must be positive."))
            if wizard.required_qty <= 0:
                raise ValidationError(_("Required quantity must be positive."))
    
    def action_search_mesh(self):
        """Search for available mesh options"""
        self.ensure_one()
        
        # Clear previous results
        self.mesh_options_ids.unlink()
        self.search_performed = True
        self.no_options_found = False
        self.search_message = ""
        
        # Find ALL suitable mesh options
        mesh_options = self._find_all_mesh_options()

        if not mesh_options:
            self.no_options_found = True
            self.search_message = _(
                "No suitable mesh found for dimensions %sx%smm in %s series.\n\n"
                "Consider:\n"
                "• Checking if master sheets are available\n"
                "• Reviewing cutting matrices\n"
                "• Purchasing new master sheets"
            ) % (self.required_width, self.required_height, self.mesh_series)
            return self._return_wizard()

        # Create option records for all found options
        created_options = []
        for mesh_result in mesh_options:
            option_vals = {
                'wizard_id': self.id,
                'source_type': mesh_result['type'],
                'source_product_id': mesh_result['product'].id,
                'source_quant_id': mesh_result['quant'].id,
                'efficiency': mesh_result.get('efficiency', 0),
                'waste_width': mesh_result.get('waste_width', 0),
                'waste_height': mesh_result.get('waste_height', 0),
            }

            if mesh_result['type'] == 'unplanned':
                option_vals.update({
                    'source_lot_id': mesh_result['lot'].id,
                    'actual_width': mesh_result['lot'].mesh_width,
                    'actual_height': mesh_result['lot'].mesh_height,
                })
            else:
                option_vals.update({
                    'actual_width': mesh_result['product'].mesh_width,
                    'actual_height': mesh_result['product'].mesh_height,
                })

            option = self.env['mesh.cut.wizard.option'].create(option_vals)
            created_options.append(option)

        # Auto-select the best option (highest efficiency)
        if created_options:
            best_option = max(created_options, key=lambda x: x.efficiency)
            self.selected_option_id = best_option.id

        self.search_message = _(
            "Found %d suitable mesh options. Best option: %s mesh %sx%smm with %.1f%% efficiency"
        ) % (
            len(created_options),
            created_options[0].source_type if created_options else '',
            created_options[0].actual_width if created_options else 0,
            created_options[0].actual_height if created_options else 0,
            max(opt.efficiency for opt in created_options) if created_options else 0
        )
        
        return self._return_wizard()

    def _find_all_mesh_options(self):
        """Find all suitable mesh options, not just the best one"""
        # Get the cutting matrix for this series
        matrix = self.env['mesh.cut.matrix'].search([
            ('mesh_series', '=', self.mesh_series),
            ('active', '=', True)
        ], limit=1)

        if not matrix:
            return []

        return matrix.get_all_suitable_options(self.required_width, self.required_height)

    def action_create_operation(self):
        """Create the mesh cut operation"""
        self.ensure_one()
        
        if not self.selected_option_id:
            raise UserError(_("Please search for mesh options first and select one."))
        
        option = self.selected_option_id
        
        # Create the operation
        operation_vals = {
            'name': self.name,
            'sale_order_id': self.sale_order_id.id if self.sale_order_id else False,
            'config_id': self.config_id.id if self.config_id else False,
            'required_width': self.required_width,
            'required_height': self.required_height,
            'required_qty': self.required_qty,
            'mesh_series': self.mesh_series,
            'source_type': option.source_type,
            'source_product_id': option.source_product_id.id,
            'source_quant_id': option.source_quant_id.id,
            'source_lot_id': option.source_lot_id.id if option.source_lot_id else False,
            'actual_width': option.actual_width,
            'actual_height': option.actual_height,
            'waste_width': option.waste_width,
            'waste_height': option.waste_height,
            'efficiency': option.efficiency,
        }
        
        operation = self.env['mesh.cut.operation'].create(operation_vals)
        
        # Return action to view the created operation
        return {
            'name': _('Mesh Cut Operation'),
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.operation',
            'view_mode': 'form',
            'res_id': operation.id,
            'target': 'current',
        }
    
    def _return_wizard(self):
        """Return action to keep wizard open"""
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
        }


class MeshCutWizardOption(models.TransientModel):
    _name = 'mesh.cut.wizard.option'
    _description = 'Mesh Cut Wizard Option'
    _order = 'efficiency desc'
    
    wizard_id = fields.Many2one('mesh.cut.wizard', 'Wizard', required=True, ondelete='cascade')
    
    # Source details
    source_type = fields.Selection([
        ('unplanned', 'Unplanned Off-cut'),
        ('planned', 'Planned Off-cut'),
        ('master', 'Master Sheet'),
    ], string='Source Type', required=True)
    
    source_product_id = fields.Many2one('product.product', 'Source Product', required=True)
    source_lot_id = fields.Many2one('stock.lot', 'Source Lot/Serial')
    source_quant_id = fields.Many2one('stock.quant', 'Source Stock', required=True)
    
    # Dimensions and efficiency
    actual_width = fields.Float('Actual Width (mm)')
    actual_height = fields.Float('Actual Height (mm)')
    waste_width = fields.Float('Waste Width (mm)')
    waste_height = fields.Float('Waste Height (mm)')
    efficiency = fields.Float('Efficiency (%)')
    
    # Display fields
    display_name = fields.Char('Option', compute='_compute_display_name')
    available_qty = fields.Float('Available Qty', related='source_quant_id.quantity')
    
    @api.depends('source_type', 'actual_width', 'actual_height', 'efficiency')
    def _compute_display_name(self):
        for option in self:
            option.display_name = f"{option.source_type.title()}: {option.actual_width}x{option.actual_height}mm ({option.efficiency:.1f}% efficient)"
