# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json
import logging

_logger = logging.getLogger(__name__)

class TestMeshSystemWizard(models.TransientModel):
    _name = 'test.mesh.system.wizard'
    _description = 'Test Mesh System Wizard'

    # Option 1: Use existing configuration
    config_id = fields.Many2one('config.matrix.configuration', 'Existing Configuration',
                               help="Select an existing Draft BOM to test with")

    # Option 2: Use template with test data
    template_id = fields.Many2one('config.matrix.template', 'Configuration Template')
    mesh_width = fields.Float('Test Mesh Width', default=900)
    mesh_height = fields.Float('Test Mesh Height', default=1100)

    # Results
    test_results = fields.Text('Test Results', readonly=True)
    
    def action_test_mesh_system(self):
        """Test the mesh system with sample data"""
        self.ensure_one()
        
        results = []
        results.append("=== MESH SYSTEM TEST ===\n")
        
        try:
            # Determine which configuration to use
            if self.config_id:
                config = self.config_id
                results.append(f"Using existing configuration: {config.name}")
                template_id = config.template_id.id if config.template_id else None
            elif self.template_id:
                results.append(f"Using template: {self.template_id.name}")
                template_id = self.template_id.id
                config = None
            else:
                # Try to find any existing configuration
                existing_configs = self.env['config.matrix.configuration'].search([
                    ('state', '=', 'draft')
                ], limit=1)
                if existing_configs:
                    config = existing_configs[0]
                    template_id = config.template_id.id if config.template_id else None
                    results.append(f"Using first available draft configuration: {config.name}")
                else:
                    results.append("✗ No configuration available for testing")
                    self.test_results = '\n'.join(results)
                    return self._return_wizard()

            # 1. Test calculated fields
            results.append("\n1. Testing Calculated Fields:")

            if config:
                # Use existing configuration's calculated fields
                mesh_fields = [
                    '_CALCULATED_mesh_required',
                    '_CALCULATED_mesh_width',
                    '_CALCULATED_mesh_height',
                    '_CALCULATED_mesh_series',
                    '_CALCULATED_mesh_area',
                    '_CALCULATED_mesh_operation_required',
                    '_CALCULATED_mesh_operation_type',
                    '_CALCULATED_mesh_area_m2',
                    '_CALCULATED_mesh_perimeter',
                    '_CALCULATED_mesh_aspect_ratio',
                    '_CALCULATED_mesh_size_category'
                ]

                for field_name in mesh_fields:
                    if hasattr(config, field_name):
                        value = getattr(config, field_name)
                        results.append(f"   {field_name}: {value}")
                    else:
                        results.append(f"   {field_name}: Field not found")
            else:
                # Create test config values for template testing
                test_config = {
                    'door_width': self.mesh_width,
                    'door_height': self.mesh_height,
                    'width': self.mesh_width,
                    'height': self.mesh_height,
                }

                # Calculate fields
                if template_id:
                    calc_field_model = self.env['config.matrix.calculated.field']
                    calculated_results = calc_field_model.calculate_fields(test_config, template_id)

                    for field_name, value in calculated_results.items():
                        if 'mesh' in field_name.lower():
                            results.append(f"   {field_name}: {value}")
                else:
                    results.append("   ✗ No template available for field calculation")
            
            # 2. Test mesh operation template
            results.append("\n2. Testing Mesh Operation Template:")
            
            mesh_templates = self.env['config.matrix.operation.template'].search([
                ('is_mesh_operation', '=', True),
                ('active', '=', True)
            ])
            
            if mesh_templates:
                template = mesh_templates[0]
                results.append(f"   Found template: {template.name}")
                results.append(f"   Auto find mesh: {template.auto_find_mesh}")
                results.append(f"   Create BOM component: {template.create_bom_component}")
                
                # Test execution
                test_config.update(calculated_results)
                test_config['config_id'] = False
                
                try:
                    result = template.execute_mesh_operation(test_config)
                    if result.get('success'):
                        results.append(f"   ✓ Mesh operation executed successfully")
                        results.append(f"   Created operation: {result.get('mesh_operation_name')}")
                    else:
                        results.append(f"   ✗ Mesh operation failed: {result.get('error')}")
                except Exception as e:
                    results.append(f"   ✗ Exception during execution: {str(e)}")
            else:
                results.append("   ✗ No mesh operation templates found")
            
            # 3. Test mesh cut matrix
            results.append("\n3. Testing Mesh Cut Matrix:")
            
            matrices = self.env['mesh.cut.matrix'].search([('active', '=', True)])
            if matrices:
                matrix = matrices[0]
                results.append(f"   Found matrix: {matrix.name} ({matrix.mesh_series})")
                
                # Test best fit
                try:
                    best_fit = matrix.get_best_fit(self.mesh_width, self.mesh_height)
                    if best_fit:
                        results.append(f"   ✓ Best fit found: {best_fit.get('source_type')} - {best_fit.get('efficiency', 0):.1f}% efficiency")
                    else:
                        results.append("   ✗ No suitable mesh found")
                except Exception as e:
                    results.append(f"   ✗ Error finding best fit: {str(e)}")
            else:
                results.append("   ✗ No mesh cut matrices found")
            
            # 4. Test operation mappings
            results.append("\n4. Testing Operation Mappings:")
            
            mappings = self.env['config.matrix.field.operation.mapping'].search([
                ('operation_template_id.is_mesh_operation', '=', True)
            ])
            
            if mappings:
                results.append(f"   Found {len(mappings)} mesh operation mappings:")
                for mapping in mappings:
                    results.append(f"   - Field: {mapping.field_id.name}")
                    results.append(f"     Template: {mapping.operation_template_id.name}")
                    results.append(f"     Condition: {mapping.condition}")
            else:
                results.append("   ✗ No mesh operation mappings found")
            
            results.append("\n=== TEST COMPLETE ===")
            
        except Exception as e:
            results.append(f"\n✗ FATAL ERROR: {str(e)}")
            _logger.error(f"Error in mesh system test: {str(e)}")
        
        self.test_results = '\n'.join(results)
        
        return self._return_wizard()

    def _return_wizard(self):
        """Return to wizard view"""
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'test.mesh.system.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
        }
