# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError


class TestOperationTemplateDurationCost(TransactionCase):
    """Test the separate duration and cost fields in operation templates"""

    def setUp(self):
        super().setUp()
        
        # Create a work center for testing
        self.workcenter = self.env['mrp.workcenter'].create({
            'name': 'Test Work Center',
            'capacity': 1.0,
            'time_efficiency': 100.0,
        })
        
        # Create a configuration template for testing
        self.config_template = self.env['config.matrix.template'].create({
            'name': 'Test Config Template',
            'description': 'Test template for operation testing',
        })

    def test_duration_and_cost_fields_separate(self):
        """Test that duration and cost are calculated separately"""
        
        # Create operation template with both duration and cost formulas
        operation_template = self.env['config.matrix.operation.template'].create({
            'name': 'Test Operation',
            'workcenter_id': self.workcenter.id,
            'config_template_id': self.config_template.id,
            'duration_formula': '0.035',  # Duration in hours (like RecMatlT lookup)
            'cost_formula': '0.035 * 2 * 25.0',  # Duration * multiplier * wage rate
            'test_height': 2100,
            'test_width': 860,
        })
        
        # Test that duration and cost are calculated correctly
        test_config = {
            'height': 2100,
            'width': 860,
        }
        
        duration = operation_template.get_calculated_duration(test_config)
        cost = operation_template.get_calculated_cost(test_config)
        
        # Duration should be 0.035 hours = 2.1 minutes
        self.assertAlmostEqual(duration, 0.035, places=3)
        
        # Cost should be 0.035 * 2 * 25.0 = 1.75
        self.assertAlmostEqual(cost, 1.75, places=2)
        
        # Verify computed fields are updated
        self.assertAlmostEqual(operation_template.duration_minutes, 0.035, places=3)
        self.assertAlmostEqual(operation_template.cost_amount, 1.75, places=2)

    def test_default_values(self):
        """Test default values when no formulas are provided"""

        operation_template = self.env['config.matrix.operation.template'].create({
            'name': 'Test Operation Default',
            'workcenter_id': self.workcenter.id,
            'config_template_id': self.config_template.id,
            'default_duration': 45.0,  # 45 minutes
        })

        # Should use default duration and zero cost (no cost formula)
        self.assertEqual(operation_template.duration_minutes, 45.0)
        self.assertEqual(operation_template.cost_amount, 0.0)

    def test_constraints(self):
        """Test validation constraints"""
        
        # Test negative default duration
        with self.assertRaises(ValidationError):
            self.env['config.matrix.operation.template'].create({
                'name': 'Invalid Duration',
                'workcenter_id': self.workcenter.id,
                'config_template_id': self.config_template.id,
                'default_duration': -10.0,
            })
        


    def test_formula_error_handling(self):
        """Test that formula errors fall back to default values"""
        
        operation_template = self.env['config.matrix.operation.template'].create({
            'name': 'Test Error Handling',
            'workcenter_id': self.workcenter.id,
            'config_template_id': self.config_template.id,
            'duration_formula': 'invalid_function()',
            'cost_formula': 'another_invalid_function()',
            'default_duration': 30.0,
        })

        # Should fall back to defaults when formulas fail
        test_config = {'height': 2100, 'width': 860}

        duration = operation_template.get_calculated_duration(test_config)
        cost = operation_template.get_calculated_cost(test_config)

        self.assertEqual(duration, 30.0)
        self.assertEqual(cost, 0.0)  # No default cost, should be 0
