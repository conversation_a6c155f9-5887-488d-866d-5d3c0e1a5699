# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase

class TestEfficiencyCalculation(TransactionCase):
    
    def setUp(self):
        super().setUp()
        self.matrix = self.env['mesh.cut.matrix'].create({
            'name': 'Test Matrix',
            'mesh_series': 'saltwater',
            'active': True,
        })
    
    def test_efficiency_calculation_returns_decimal(self):
        """Test that efficiency calculation returns decimal (0.64) not percentage (64)"""
        # Test case: 1000x800mm required from 1250x1000mm available
        # Expected efficiency: (1000*800) / (1250*1000) = 800000 / 1250000 = 0.64
        
        efficiency = self.matrix._calculate_efficiency(1250, 1000, 1000, 800)
        
        # Should be 0.64 (64% as decimal)
        self.assertAlmostEqual(efficiency, 0.64, places=4)
        
        # Should NOT be 64.0 (which was the bug)
        self.assertNotEqual(efficiency, 64.0)
    
    def test_efficiency_calculation_perfect_fit(self):
        """Test efficiency calculation for perfect fit"""
        efficiency = self.matrix._calculate_efficiency(1000, 800, 1000, 800)
        self.assertAlmostEqual(efficiency, 1.0, places=4)
    
    def test_efficiency_calculation_small_piece(self):
        """Test efficiency calculation for small piece from large sheet"""
        # 300x250mm from 1250x1000mm
        # Expected: (300*250) / (1250*1000) = 75000 / 1250000 = 0.06
        efficiency = self.matrix._calculate_efficiency(1250, 1000, 300, 250)
        self.assertAlmostEqual(efficiency, 0.06, places=4)
    
    def test_efficiency_calculation_zero_available(self):
        """Test efficiency calculation with zero available dimensions"""
        efficiency = self.matrix._calculate_efficiency(0, 1000, 500, 400)
        self.assertEqual(efficiency, 0.0)
        
        efficiency = self.matrix._calculate_efficiency(1000, 0, 500, 400)
        self.assertEqual(efficiency, 0.0)
