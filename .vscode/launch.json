{
    "version": "0.2.0",
    "configurations": [
        { 
            "name": "Canbrax 18 - Start", 
            "type": "python", 
            "request": "launch",
            "stopOnEntry": "false",
            "console": "integratedTerminal", 
            "program": "/home/<USER>/odoo/itms/canbrax18/odoo/odoo-bin", 
            "args": [ 
                "-c", 
                "/home/<USER>/odoo/itms/canbrax18/.vscode/canbrax.conf",
                "--dev=xml",
                "-d", 
                "canbrax_sb_8",
            ],

            "cwd": "/home/<USER>/odoo/itms/canbrax18", 
        }, 
        { 
            "name": "Canbrax 18 - Upgrade", 
            "type": "python", 
            "request": "launch", 
            "stopOnEntry": "false",
            "console": "integratedTerminal", 
            "program": "/home/<USER>/odoo/itms/canbrax18/odoo/odoo-bin", 
            "args": [ 
                "-c", 
                "/home/<USER>/odoo/itms/canbrax18/.vscode/canbrax.conf",
                "-u",
                "canbrax_configmatrix",
                "-d", 
                "canbrax_sb_8",
            ],
            "cwd": "/home/<USER>/odoo/itms/canbrax18", 
        }, 
        { 
            "name": "Canbrax 18 - Tests", 
            "type": "python", 
            "request": "launch",
            # "GEVENT_SUPPORT": True,
            "stopOnEntry": "false", 
            "program": "/home/<USER>/odoo/itms/canbrax18/odoo/odoo-bin", 
            "args": [ 
                "-c", 
                "/home/<USER>/odoo/itms/canbrax18/.vscode/canbrax.conf",
                "-i", 
                "canbrax_configmatrix", 
                "-d", 
                "canbrax_sb_8", 
                "--test-enable", 
                "--stop-after-init", 
            ], 
            "cwd": "/home/<USER>/odoo/itms/canbrax18", 
        },
    ]
}